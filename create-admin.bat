@echo off
chcp 65001 >nul

echo 👤 创建管理员账号...
echo.

if not exist "backend" (
    echo ❌ backend目录不存在，请在项目根目录运行此脚本
    pause
    exit /b 1
)

cd backend

if not exist "venv" (
    echo ❌ 虚拟环境不存在，请先运行 start-dev.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 创建Python脚本文件
echo 正在创建管理员账号...
echo from django.contrib.auth.models import User > create_admin.py
echo import os >> create_admin.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'defect_classification.settings') >> create_admin.py
echo import django >> create_admin.py
echo django.setup() >> create_admin.py
echo. >> create_admin.py
echo if not User.objects.filter(username='admin').exists(): >> create_admin.py
echo     User.objects.create_superuser('admin', '<EMAIL>', 'admin123') >> create_admin.py
echo     print('✅ 管理员账号创建成功') >> create_admin.py
echo     print('   用户名: admin') >> create_admin.py
echo     print('   密码: admin123') >> create_admin.py
echo     print('   邮箱: <EMAIL>') >> create_admin.py
echo else: >> create_admin.py
echo     print('✅ 管理员账号已存在') >> create_admin.py
echo     print('   用户名: admin') >> create_admin.py
echo     print('   密码: admin123') >> create_admin.py

REM 执行脚本
python create_admin.py

REM 清理临时文件
del create_admin.py

echo.
echo 🌐 现在可以访问管理后台: http://localhost:8000/admin
echo.

cd ..
pause
