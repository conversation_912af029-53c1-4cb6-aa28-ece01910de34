#!/bin/bash

# Celery Worker启动脚本 (可选)

echo "🔄 启动Celery Worker..."

# 检测操作系统
OS="unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    OS="windows"
fi

# 检查Redis是否可用
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️  Redis未安装，Celery将无法正常工作"
    echo "   请先安装Redis或使用Docker运行Redis:"
    echo "   docker run -d -p 6379:6379 redis:alpine"
    echo ""
    read -p "是否继续启动Celery? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查Redis连接
if command -v redis-cli &> /dev/null; then
    if ! redis-cli ping >/dev/null 2>&1; then
        echo "⚠️  无法连接到Redis服务"
        echo "   请确保Redis服务正在运行"
        echo ""
        read -p "是否继续启动Celery? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        echo "✅ Redis连接正常"
    fi
fi

# 检查项目结构
if [ ! -d "backend" ]; then
    echo "❌ backend目录不存在，请确保在项目根目录运行"
    exit 1
fi

cd backend

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先运行 start-backend.sh"
    exit 1
fi

# 激活虚拟环境
if [[ "$OS" == "windows" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# 检查Celery是否安装
if ! python -c "import celery" 2>/dev/null; then
    echo "❌ Celery未安装，正在安装..."
    pip install celery redis
fi

echo ""
echo "✅ Celery环境准备完成！"
echo ""
echo "🔄 启动Celery Worker..."
echo "   监控地址: http://localhost:5555 (如果安装了flower)"
echo "   日志级别: INFO"
echo ""
echo "💡 Celery功能:"
echo "   - 异步数据导入处理"
echo "   - 后台任务执行"
echo "   - 进度通知推送"
echo ""
echo "⏹️  停止服务: Ctrl+C"
echo ""

# 启动Celery Worker
if [[ "$OS" == "windows" ]]; then
    # Windows下使用solo pool
    celery -A config worker --loglevel=info --pool=solo
else
    # Linux/macOS下使用默认pool
    celery -A config worker --loglevel=info
fi
