# 开发指南

## 🏗️ 项目架构

### 后端架构 (Django)

```
backend/
├── config/                 # 项目配置
│   ├── settings.py        # Django设置
│   ├── urls.py           # 主URL配置
│   ├── wsgi.py           # WSGI入口
│   ├── asgi.py           # ASGI入口(WebSocket)
│   └── celery.py         # Celery配置
├── apps/                 # 应用模块
│   ├── defects/          # 缺陷管理
│   ├── tasks/            # 任务队列
│   ├── users/            # 用户管理
│   └── notifications/    # 通知系统
└── manage.py            # Django管理脚本
```

### 前端架构 (Vue3)

```
frontend/src/
├── api/                 # API接口层
├── components/          # 可复用组件
├── views/              # 页面组件
├── stores/             # Pinia状态管理
├── composables/        # 组合式函数
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
└── router/             # 路由配置
```

## 🚀 开发环境搭建

### 1. 环境要求

- **Python**: 3.11+
- **Node.js**: 18+
- **Redis**: 6+ (可选，用于Celery和WebSocket)
- **PostgreSQL**: 13+ (可选，默认使用SQLite)

### 2. 快速启动

#### 分别启动 (推荐)

```bash
# 1. 启动后端
./start-backend.sh    # Linux/macOS
start-backend.bat     # Windows

# 2. 启动前端
./start-frontend.sh   # Linux/macOS
start-frontend.bat    # Windows

# 3. 启动Celery (可选)
./start-celery.sh     # Linux/macOS
start-celery.bat      # Windows
```

#### 一键启动

```bash
./start-dev.sh        # Linux/macOS
start-dev.bat         # Windows
```

### 3. 手动启动

#### 后端手动启动

```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动服务
python manage.py runserver
```

#### 前端手动启动

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 🔧 开发配置

### 1. 环境变量配置

创建 `backend/.env` 文件：

```env
# Django配置
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 数据库配置 (可选，默认使用SQLite)
DB_NAME=defect_classification
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379

# 功能开关
ENABLE_CELERY=False
ENABLE_WEBSOCKET=False
```

### 2. 依赖管理

#### 后端依赖

- **requirements.txt**: 完整依赖
- **requirements-windows.txt**: Windows优化版本
- **requirements-minimal.txt**: 最小化依赖

#### 前端依赖

使用npm管理，主要依赖：
- Vue 3
- TypeScript
- Element Plus
- Pinia
- Vue Router
- Vite

### 3. 数据库配置

#### SQLite (默认)
- 无需额外配置
- 数据库文件: `backend/db.sqlite3`

#### PostgreSQL (可选)
```bash
# 安装PostgreSQL
# 创建数据库
createdb defect_classification

# 配置环境变量
export DB_NAME=defect_classification
export DB_USER=postgres
export DB_PASSWORD=password
```

## 📝 开发规范

### 1. 代码规范

#### Python (后端)
- 遵循PEP 8规范
- 使用类型注解
- 文档字符串使用Google风格

```python
def process_defect_data(data: Dict[str, Any]) -> DefectRecord:
    """处理缺陷数据
    
    Args:
        data: 原始缺陷数据字典
        
    Returns:
        DefectRecord: 处理后的缺陷记录对象
        
    Raises:
        ValidationError: 数据验证失败时抛出
    """
    pass
```

#### TypeScript (前端)
- 严格类型检查
- 使用接口定义数据结构
- 组件使用Composition API

```typescript
interface DefectRecord {
  id: string
  issue_key: string
  summary: string
  status: string
  priority: string
}

const useDefectManagement = () => {
  const defects = ref<DefectRecord[]>([])
  
  const fetchDefects = async (): Promise<void> => {
    // 实现逻辑
  }
  
  return {
    defects,
    fetchDefects
  }
}
```

### 2. Git工作流

```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: 添加新功能"

# 推送分支
git push origin feature/new-feature

# 创建Pull Request
```

### 3. 提交信息规范

使用Conventional Commits规范：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 🧪 测试

### 1. 后端测试

```bash
cd backend

# 运行所有测试
python manage.py test

# 运行特定应用测试
python manage.py test apps.defects

# 生成测试覆盖率报告
coverage run --source='.' manage.py test
coverage report
```

### 2. 前端测试

```bash
cd frontend

# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

## 📦 部署

### 1. 开发环境部署

使用提供的启动脚本即可。

### 2. 生产环境部署

#### Docker部署

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 手动部署

```bash
# 后端部署
cd backend
pip install -r requirements.txt
python manage.py collectstatic
python manage.py migrate
gunicorn config.wsgi:application

# 前端部署
cd frontend
npm install
npm run build
# 将dist目录部署到Web服务器
```

## 🔍 调试

### 1. 后端调试

#### Django Debug Toolbar
```python
# settings.py
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']
```

#### 日志调试
```python
import logging
logger = logging.getLogger(__name__)
logger.info("调试信息")
```

### 2. 前端调试

#### Vue DevTools
安装浏览器扩展进行调试

#### 控制台调试
```typescript
console.log('调试信息', data)
console.error('错误信息', error)
```

## 📚 API文档

### 1. 后端API

启动后端服务后访问：
- API根路径: http://localhost:8000/api/
- 管理后台: http://localhost:8000/admin/

### 2. 主要API端点

```
GET  /api/defects/records/          # 获取缺陷列表
POST /api/defects/records/          # 创建缺陷
GET  /api/defects/records/{id}/     # 获取缺陷详情
PUT  /api/defects/records/{id}/     # 更新缺陷
DELETE /api/defects/records/{id}/   # 删除缺陷

POST /api/defects/records/import_data/     # 导入数据
GET  /api/defects/records/export_data/     # 导出数据
POST /api/defects/records/batch_classify/  # 批量分类

GET  /api/defects/batches/          # 获取批次列表
GET  /api/defects/rules/            # 获取分类规则
POST /api/defects/rules/            # 创建分类规则
```

## 🐛 常见问题

### 1. 后端问题

**问题**: 数据库迁移失败
**解决**: 
```bash
python manage.py makemigrations --empty appname
python manage.py migrate --fake-initial
```

**问题**: 静态文件404
**解决**:
```bash
python manage.py collectstatic
```

### 2. 前端问题

**问题**: 依赖安装失败
**解决**:
```bash
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**问题**: 热重载不工作
**解决**: 检查vite.config.ts中的server配置

### 3. 跨域问题

确保后端CORS配置正确：
```python
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]
```

## 📞 技术支持

- 项目文档: README.md
- 问题反馈: GitHub Issues
- 开发讨论: GitHub Discussions
