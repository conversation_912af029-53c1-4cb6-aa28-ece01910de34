#!/bin/bash

# 清理重复代码和文件的脚本

echo "🧹 开始清理重复代码和文件..."

# 删除旧的Django项目配置目录
if [ -d "backend/defect_classification" ]; then
    echo "删除旧的Django配置目录: backend/defect_classification"
    rm -rf backend/defect_classification
fi

# 删除旧的应用目录（如果为空）
for dir in "backend/defects" "backend/tasks" "backend/users" "backend/notifications"; do
    if [ -d "$dir" ] && [ -z "$(ls -A $dir)" ]; then
        echo "删除空目录: $dir"
        rmdir "$dir"
    elif [ -d "$dir" ]; then
        echo "⚠️  目录 $dir 不为空，请手动检查"
    fi
done

# 删除临时文件
temp_files=(
    "create_user.py"
    "init_mapping.py" 
    "main.py"
    "*.pyc"
    "__pycache__"
    ".DS_Store"
    "Thumbs.db"
)

for pattern in "${temp_files[@]}"; do
    find . -name "$pattern" -type f -delete 2>/dev/null
    find . -name "$pattern" -type d -exec rm -rf {} + 2>/dev/null
done

# 删除CSV测试文件
find . -name "*.csv" -path "./16.0*" -delete 2>/dev/null

# 删除重复的requirements文件（如果存在）
if [ -f "backend/requirements-old.txt" ]; then
    rm backend/requirements-old.txt
fi

# 清理日志文件
if [ -d "backend/logs" ]; then
    find backend/logs -name "*.log" -mtime +7 -delete 2>/dev/null
fi

# 清理前端构建文件
if [ -d "frontend/dist" ]; then
    echo "清理前端构建文件"
    rm -rf frontend/dist
fi

if [ -d "frontend/node_modules/.cache" ]; then
    echo "清理前端缓存"
    rm -rf frontend/node_modules/.cache
fi

echo "✅ 清理完成！"
echo ""
echo "📋 当前项目结构:"
echo "backend/"
echo "├── config/          # Django配置"
echo "├── apps/            # 应用模块"
echo "│   ├── defects/     # 缺陷管理"
echo "│   ├── tasks/       # 任务管理"
echo "│   ├── users/       # 用户管理"
echo "│   └── notifications/ # 通知系统"
echo "└── manage.py        # Django管理脚本"
echo ""
echo "frontend/"
echo "├── src/             # 源代码"
echo "├── public/          # 公共资源"
echo "└── package.json     # 依赖配置"
