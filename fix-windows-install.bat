@echo off
chcp 65001 >nul

echo 🔧 修复Windows安装问题...
echo.

if not exist "backend" (
    echo ❌ 请在项目根目录运行此脚本
    pause
    exit /b 1
)

cd backend

if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
)

echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo 📦 升级pip和setuptools...
python -m pip install --upgrade pip setuptools wheel

echo 🔧 尝试多种安装方法...

REM 方法1: 使用Windows专用requirements
echo.
echo 方法1: 使用Windows专用依赖文件...
pip install -r requirements-windows.txt --no-cache-dir
if not errorlevel 1 (
    echo ✅ 方法1成功！
    goto :success
)

REM 方法2: 使用国内镜像源
echo.
echo 方法2: 使用国内镜像源...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn --no-cache-dir
if not errorlevel 1 (
    echo ✅ 方法2成功！
    goto :success
)

REM 方法3: 逐个安装核心包
echo.
echo 方法3: 逐个安装核心包...
pip install Django==4.2.7 --no-cache-dir
pip install djangorestframework==3.14.0 --no-cache-dir
pip install django-cors-headers==4.3.1 --no-cache-dir
pip install celery==5.3.4 --no-cache-dir
pip install redis==5.0.1 --no-cache-dir
pip install pandas==2.1.4 --no-cache-dir
pip install psycopg2-binary==2.9.9 --no-cache-dir
pip install python-dotenv==1.0.0 --no-cache-dir

echo ✅ 核心包安装完成！

REM 方法4: 安装可选包
echo.
echo 方法4: 安装可选包...
pip install channels==4.0.0 --no-cache-dir
pip install channels-redis==4.1.0 --no-cache-dir
pip install openpyxl==3.1.2 --no-cache-dir
pip install django-extensions==3.2.3 --no-cache-dir
pip install django-filter==23.5 --no-cache-dir

echo ✅ 可选包安装完成！

:success
echo.
echo ✅ 依赖安装完成！
echo.

echo 🗄️ 执行数据库迁移...
python manage.py migrate
if errorlevel 1 (
    echo ❌ 数据库迁移失败，但可以继续
)

echo.
echo 🎉 修复完成！现在可以尝试启动服务：
echo    python manage.py runserver
echo.

cd ..
pause
