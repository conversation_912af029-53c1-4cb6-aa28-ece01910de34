@echo off
chcp 65001 >nul

echo 🧹 开始清理重复代码和文件...
echo.

REM 删除旧的Django项目配置目录
if exist "backend\defect_classification" (
    echo 删除旧的Django配置目录: backend\defect_classification
    rmdir /s /q "backend\defect_classification"
)

REM 删除旧的应用目录（如果为空）
for %%d in ("backend\defects" "backend\tasks" "backend\users" "backend\notifications") do (
    if exist "%%d" (
        dir /b "%%d" | findstr . >nul
        if errorlevel 1 (
            echo 删除空目录: %%d
            rmdir "%%d"
        ) else (
            echo ⚠️  目录 %%d 不为空，请手动检查
        )
    )
)

REM 删除临时文件
if exist "create_user.py" del "create_user.py"
if exist "init_mapping.py" del "init_mapping.py"
if exist "main.py" del "main.py"

REM 删除Python缓存文件
for /r . %%f in (*.pyc) do del "%%f" 2>nul
for /r . %%d in (__pycache__) do rmdir /s /q "%%d" 2>nul

REM 删除系统文件
for /r . %%f in (.DS_Store Thumbs.db) do del "%%f" 2>nul

REM 删除CSV测试文件
for %%f in ("16.0*.csv") do if exist "%%f" del "%%f"

REM 删除重复的requirements文件
if exist "backend\requirements-old.txt" del "backend\requirements-old.txt"

REM 清理日志文件（7天前的）
if exist "backend\logs" (
    forfiles /p "backend\logs" /m *.log /d -7 /c "cmd /c del @path" 2>nul
)

REM 清理前端构建文件
if exist "frontend\dist" (
    echo 清理前端构建文件
    rmdir /s /q "frontend\dist"
)

if exist "frontend\node_modules\.cache" (
    echo 清理前端缓存
    rmdir /s /q "frontend\node_modules\.cache"
)

echo.
echo ✅ 清理完成！
echo.
echo 📋 当前项目结构:
echo backend/
echo ├── config/          # Django配置
echo ├── apps/            # 应用模块
echo │   ├── defects/     # 缺陷管理
echo │   ├── tasks/       # 任务管理
echo │   ├── users/       # 用户管理
echo │   └── notifications/ # 通知系统
echo └── manage.py        # Django管理脚本
echo.
echo frontend/
echo ├── src/             # 源代码
echo ├── public/          # 公共资源
echo └── package.json     # 依赖配置
echo.
pause
