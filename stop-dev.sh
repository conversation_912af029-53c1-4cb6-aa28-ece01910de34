#!/bin/bash

# 缺陷分类工具开发环境停止脚本 (支持Linux/macOS/Windows)

echo "🛑 停止缺陷分类工具开发环境..."

# 检测操作系统
OS="unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    OS="windows"
fi

echo "📱 检测到操作系统: $OS"

if [[ "$OS" == "windows" ]]; then
    # Windows下停止服务
    echo "🌐 停止Django开发服务器..."
    taskkill /F /IM python.exe /FI "WINDOWTITLE eq Django Server*" 2>nul
    taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Django Server*" 2>nul

    echo "🎨 停止前端开发服务器..."
    taskkill /F /IM node.exe /FI "WINDOWTITLE eq Frontend Server*" 2>nul
    taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Frontend Server*" 2>nul

    echo "🔄 停止Celery Worker..."
    taskkill /F /IM python.exe /FI "WINDOWTITLE eq Celery Worker*" 2>nul
    taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Celery Worker*" 2>nul

    # 备用方法：通过进程名停止
    echo "🔄 清理残留进程..."
    wmic process where "name='python.exe' and commandline like '%manage.py runserver%'" delete 2>nul
    wmic process where "name='python.exe' and commandline like '%celery%worker%'" delete 2>nul
    wmic process where "name='node.exe' and commandline like '%vite%'" delete 2>nul
    wmic process where "name='node.exe' and commandline like '%npm run dev%'" delete 2>nul

else
    # Linux/macOS下停止服务
    echo "🌐 停止Django开发服务器..."
    pkill -f "python manage.py runserver"
    pkill -f "python.*manage.py runserver"

    echo "🎨 停止前端开发服务器..."
    pkill -f "npm run dev"
    pkill -f "vite"

    echo "🔄 停止Celery Worker..."
    pkill -f "celery.*worker"
fi

# 停止Redis（可选，如果是专门为此项目启动的）
# echo "📦 停止Redis服务..."
# if [[ "$OS" == "windows" ]]; then
#     taskkill /F /IM redis-server.exe 2>nul
# else
#     pkill redis-server
# fi

echo "✅ 开发环境已停止"

if [[ "$OS" == "windows" ]]; then
    echo ""
    echo "💡 提示: 如果某些进程未能停止，请手动关闭对应的命令行窗口"
    pause
fi
