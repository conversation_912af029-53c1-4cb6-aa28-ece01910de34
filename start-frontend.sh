#!/bin/bash

# 前端启动脚本 (支持Linux/macOS/Windows)

echo "🚀 启动Vue3前端服务..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装npm"
    exit 1
fi

# 检查项目结构
if [ ! -d "frontend" ]; then
    echo "❌ frontend目录不存在，请确保在项目根目录运行"
    exit 1
fi

cd frontend

# 检查package.json
if [ ! -f "package.json" ]; then
    echo "❌ package.json 不存在，请确保前端项目结构完整"
    exit 1
fi

echo "📦 Node.js版本: $(node --version)"
echo "📦 npm版本: $(npm --version)"

# 安装依赖
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "📦 安装前端依赖..."
    
    # 尝试使用国内镜像源加速
    echo "🔧 配置npm镜像源..."
    npm config set registry https://registry.npmmirror.com
    
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，尝试清理缓存后重试..."
        npm cache clean --force
        rm -rf node_modules package-lock.json
        npm install
        
        if [ $? -ne 0 ]; then
            echo "❌ 依赖安装失败，请检查网络连接或手动安装"
            exit 1
        fi
    fi
else
    echo "✅ 依赖已安装，跳过安装步骤"
fi

# 检查关键依赖
echo "🔍 检查关键依赖..."
if ! npm list vue >/dev/null 2>&1; then
    echo "❌ Vue3 未正确安装"
    exit 1
fi

if ! npm list vite >/dev/null 2>&1; then
    echo "❌ Vite 未正确安装"
    exit 1
fi

echo ""
echo "✅ 前端环境准备完成！"
echo ""
echo "🌐 启动前端开发服务器..."
echo "   访问地址: http://localhost:3000"
echo "   后端API: http://localhost:8000/api (请确保后端已启动)"
echo ""
echo "💡 开发提示:"
echo "   - 修改代码后会自动热重载"
echo "   - 使用 Ctrl+C 停止服务"
echo "   - 如需修改端口，编辑 vite.config.ts"
echo ""
echo "⏹️  停止服务: Ctrl+C"
echo ""

# 启动开发服务器
npm run dev
