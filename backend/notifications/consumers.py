import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User


class NotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket通知消费者"""
    
    async def connect(self):
        """连接处理"""
        self.user = self.scope["user"]
        
        if self.user.is_authenticated:
            self.user_group_name = f"user_{self.user.id}"
            
            # 加入用户组
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )
            
            await self.accept()
            
            # 发送连接成功消息
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'message': 'WebSocket连接已建立'
            }))
        else:
            await self.close()
    
    async def disconnect(self, close_code):
        """断开连接处理"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        """接收消息处理"""
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'ping':
                # 心跳检测
                await self.send(text_data=json.dumps({
                    'type': 'pong',
                    'timestamp': text_data_json.get('timestamp')
                }))
            elif message_type == 'mark_notification_read':
                # 标记通知为已读
                notification_id = text_data_json.get('notification_id')
                if notification_id:
                    await self.mark_notification_read(notification_id)
        except json.JSONDecodeError:
            pass
    
    async def send_notification(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'data': event['notification']
        }))
    
    async def send_progress(self, event):
        """发送进度更新"""
        await self.send(text_data=json.dumps({
            'type': 'progress',
            'data': event['progress']
        }))
    
    async def send_task_status(self, event):
        """发送任务状态更新"""
        await self.send(text_data=json.dumps({
            'type': 'task_status',
            'data': event['task_status']
        }))
    
    @database_sync_to_async
    def mark_notification_read(self, notification_id):
        """标记通知为已读"""
        try:
            from .models import Notification
            notification = Notification.objects.get(
                id=notification_id, 
                user=self.user
            )
            notification.mark_as_read()
        except Notification.DoesNotExist:
            pass
