import json
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone
from .models import Notification


class NotificationService:
    """实时通知服务"""
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_user_notification(self, user, notification_type, title, message, data=None):
        """发送用户通知"""
        notification_data = {
            'type': 'user_notification',
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'timestamp': timezone.now().isoformat(),
            'data': data or {}
        }
        
        # 发送到用户专用频道
        user_channel = f"user_{user.id}"
        
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                user_channel,
                {
                    'type': 'send_notification',
                    'notification': notification_data
                }
            )
        
        # 保存到数据库
        self._save_notification_to_db(user, notification_data)
    
    def send_progress_update(self, user, task_id, progress, step_description):
        """发送进度更新"""
        progress_data = {
            'type': 'progress_update',
            'task_id': task_id,
            'progress': progress,
            'step_description': step_description,
            'timestamp': timezone.now().isoformat()
        }
        
        user_channel = f"user_{user.id}"
        
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                user_channel,
                {
                    'type': 'send_progress',
                    'progress': progress_data
                }
            )
    
    def send_task_status_update(self, user, task_id, status, result=None):
        """发送任务状态更新"""
        task_status_data = {
            'type': 'task_status_update',
            'task_id': task_id,
            'status': status,
            'result': result,
            'timestamp': timezone.now().isoformat()
        }
        
        user_channel = f"user_{user.id}"
        
        if self.channel_layer:
            async_to_sync(self.channel_layer.group_send)(
                user_channel,
                {
                    'type': 'send_task_status',
                    'task_status': task_status_data
                }
            )
    
    def broadcast_system_message(self, title, message, data=None):
        """广播系统消息"""
        from django.contrib.auth.models import User
        
        # 发送给所有在线用户
        users = User.objects.filter(is_active=True)
        for user in users:
            self.send_user_notification(
                user=user,
                notification_type='system_message',
                title=title,
                message=message,
                data=data
            )
    
    def _save_notification_to_db(self, user, notification_data):
        """保存通知到数据库"""
        try:
            Notification.objects.create(
                user=user,
                notification_type=notification_data['notification_type'],
                title=notification_data['title'],
                message=notification_data['message'],
                data=notification_data.get('data', {})
            )
        except Exception as e:
            # 记录错误但不影响主流程
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"保存通知到数据库失败: {str(e)}")
    
    def get_user_notifications(self, user, limit=50, unread_only=False):
        """获取用户通知"""
        queryset = Notification.objects.filter(user=user)
        
        if unread_only:
            queryset = queryset.filter(is_read=False)
        
        return queryset.order_by('-created_at')[:limit]
    
    def mark_notification_read(self, notification_id, user):
        """标记通知为已读"""
        try:
            notification = Notification.objects.get(id=notification_id, user=user)
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False
    
    def mark_all_notifications_read(self, user):
        """标记所有通知为已读"""
        Notification.objects.filter(user=user, is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )
    
    def cleanup_old_notifications(self, days=30):
        """清理旧通知"""
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=days)
        deleted_count = Notification.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        return deleted_count
