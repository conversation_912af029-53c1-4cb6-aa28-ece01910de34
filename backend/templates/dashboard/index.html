<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缺陷分类工具 - 首页</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
        }
        
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .welcome-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .welcome-subtitle {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- 欢迎卡片 -->
                <div class="welcome-card">
                    <div class="logo">
                        <i class="fas fa-bug"></i>
                    </div>
                    <h1 class="welcome-title">缺陷分类工具</h1>
                    <p class="welcome-subtitle lead">
                        智能化缺陷管理与分析平台，提升测试效率，优化质量管控
                    </p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <a href="{% url 'dashboard:dashboard' %}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>进入仪表板
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="/admin/" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-cog me-2"></i>系统管理
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 功能特性 -->
                <div class="row mt-5">
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <h4>数据导入</h4>
                            <p class="text-muted">
                                支持CSV、Excel文件导入，智能字段映射，批量数据处理
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h4>智能分类</h4>
                            <p class="text-muted">
                                基于规则引擎的自动分类，支持自定义规则，提升分类准确性
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h4>数据分析</h4>
                            <p class="text-muted">
                                多维度统计分析，趋势图表展示，支持数据导出
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h4>多用户管理</h4>
                            <p class="text-muted">
                                支持多用户并发，权限分级管理，数据安全隔离
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h4>任务队列</h4>
                            <p class="text-muted">
                                异步任务处理，进度实时跟踪，支持任务取消和重试
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h4>实时通知</h4>
                            <p class="text-muted">
                                WebSocket实时通知，任务状态推送，操作结果反馈
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- 底部信息 -->
                <div class="text-center mt-5">
                    <p class="text-white-50">
                        <i class="fas fa-code me-2"></i>
                        基于 Django + Vue3 + Element Plus 构建
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
