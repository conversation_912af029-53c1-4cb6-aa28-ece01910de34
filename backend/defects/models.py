from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class DefectRecord(models.Model):
    """缺陷记录模型"""
    
    # 核心标识字段
    issue_key = models.Char<PERSON>ield(max_length=50, unique=True, verbose_name='Issue key')
    issue_id = models.CharField(max_length=50, verbose_name='Issue id', blank=True)
    summary = models.TextField(verbose_name='Summary', blank=True)
    
    # 基础状态字段
    issue_type = models.CharField(max_length=50, verbose_name='Issue Type', blank=True)
    status = models.CharField(max_length=50, verbose_name='Status', blank=True)
    priority = models.CharField(max_length=20, verbose_name='Priority', blank=True)
    resolution = models.CharField(max_length=50, verbose_name='Resolution', blank=True)
    
    # 项目相关字段
    project_key = models.CharField(max_length=50, verbose_name='Project key', blank=True)
    project_name = models.CharField(max_length=200, verbose_name='Project name', blank=True)
    project_type = models.CharField(max_length=50, verbose_name='Project type', blank=True)
    project_lead = models.CharField(max_length=100, verbose_name='Project lead', blank=True)
    
    # 人员相关字段
    assignee = models.CharField(max_length=100, verbose_name='Assignee', blank=True)
    reporter = models.CharField(max_length=100, verbose_name='Reporter', blank=True)
    creator = models.CharField(max_length=100, verbose_name='Creator', blank=True)
    
    # 时间相关字段
    created = models.DateTimeField(verbose_name='Created', null=True, blank=True)
    updated = models.DateTimeField(verbose_name='Updated', null=True, blank=True)
    resolved = models.DateTimeField(verbose_name='Resolved', null=True, blank=True)
    due_date = models.DateTimeField(verbose_name='Due Date', null=True, blank=True)
    
    # 版本相关字段
    affects_versions = models.TextField(verbose_name='Affects Version/s', blank=True)
    components = models.TextField(verbose_name='Component/s', blank=True)
    
    # 描述和环境字段
    description = models.TextField(verbose_name='Description', blank=True)
    environment = models.TextField(verbose_name='Environment', blank=True)
    
    # 自定义字段
    device_model = models.CharField(max_length=100, verbose_name='机型', blank=True)
    bug_category_custom = models.CharField(max_length=100, verbose_name='Bug category', blank=True)
    common_issue = models.CharField(max_length=100, verbose_name='CommonIssue', blank=True)
    rd_owner = models.CharField(max_length=100, verbose_name='RD owner', blank=True)
    root_cause = models.TextField(verbose_name='根因', blank=True)
    solution = models.TextField(verbose_name='解决方案', blank=True)
    
    # 数据处理字段（自动生成）
    project_extracted = models.CharField(max_length=100, verbose_name='提取的项目', blank=True)
    summary_text = models.TextField(verbose_name='摘要文本', blank=True)
    bug_category = models.CharField(max_length=100, verbose_name='缺陷分类', blank=True)
    commonality = models.CharField(max_length=100, verbose_name='共性问题', blank=True)
    
    # 原始数据存储
    original_data = models.JSONField(default=dict, verbose_name='原始导入数据')
    
    # 数据权限和导入标识
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', db_index=True)
    import_timestamp = models.DateTimeField(verbose_name='导入时间戳', db_index=True)
    uploaded_by = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        verbose_name='上传用户',
        related_name='uploaded_defects'
    )
    
    # 系统字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='记录更新时间')
    
    class Meta:
        verbose_name = '缺陷记录'
        verbose_name_plural = '缺陷记录'
        ordering = ['-import_timestamp', '-created']
        indexes = [
            models.Index(fields=['import_batch_id', 'uploaded_by']),
            models.Index(fields=['import_timestamp', 'uploaded_by']),
            models.Index(fields=['issue_key']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['project_key', 'uploaded_by']),
        ]
    
    def __str__(self):
        return f"{self.issue_key} - {self.summary[:50]}"
    
    @property
    def created_date(self):
        """兼容性属性，返回创建日期"""
        return self.created.date() if self.created else None


class FieldMapping(models.Model):
    """字段映射配置模型"""
    FIELD_TYPE_CHOICES = [
        ('string', '字符串'),
        ('text', '文本'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('datetime', '日期时间'),
        ('date', '日期'),
        ('boolean', '布尔值'),
        ('json', 'JSON'),
    ]
    
    csv_field_name = models.CharField(max_length=200, verbose_name='CSV字段名')
    db_field_name = models.CharField(max_length=100, verbose_name='数据库字段名')
    field_type = models.CharField(max_length=20, choices=FIELD_TYPE_CHOICES, verbose_name='字段类型')
    
    is_required = models.BooleanField(default=False, verbose_name='是否必需')
    is_core_field = models.BooleanField(default=False, verbose_name='是否核心字段')
    default_value = models.TextField(verbose_name='默认值', blank=True)
    
    date_format = models.CharField(max_length=50, verbose_name='日期格式', blank=True)
    max_length = models.IntegerField(verbose_name='最大长度', null=True, blank=True)
    validation_regex = models.TextField(verbose_name='验证正则表达式', blank=True)
    
    description = models.TextField(verbose_name='字段描述', blank=True)
    example_value = models.CharField(max_length=200, verbose_name='示例值', blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = '字段映射'
        verbose_name_plural = '字段映射'
        unique_together = ['csv_field_name', 'db_field_name']
        ordering = ['-is_core_field', '-is_required', 'csv_field_name']
    
    def __str__(self):
        return f"{self.csv_field_name} -> {self.db_field_name}"


class ImportBatch(models.Model):
    """导入批次管理模型"""
    STATUS_CHOICES = [
        ('uploaded', '已上传'),
        ('queued', '排队中'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    batch_id = models.CharField(max_length=50, unique=True, verbose_name='批次ID')
    batch_name = models.CharField(max_length=200, verbose_name='批次名称', blank=True)
    import_timestamp = models.DateTimeField(auto_now_add=True, verbose_name='导入时间')
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='上传用户')
    
    # 导入统计
    total_records = models.IntegerField(default=0, verbose_name='总记录数')
    success_records = models.IntegerField(default=0, verbose_name='成功记录数')
    failed_records = models.IntegerField(default=0, verbose_name='失败记录数')
    processed_records = models.IntegerField(default=0, verbose_name='已处理记录数')
    
    # 数据源信息
    data_source = models.CharField(max_length=50, verbose_name='数据源类型')
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)
    source_file_size = models.BigIntegerField(verbose_name='文件大小(字节)', null=True, blank=True)
    
    # 处理状态和进度
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded', verbose_name='处理状态')
    progress_percentage = models.FloatField(default=0.0, verbose_name='处理进度百分比')
    current_step = models.CharField(max_length=100, verbose_name='当前处理步骤', blank=True)
    
    # 处理时间
    processing_started_at = models.DateTimeField(verbose_name='开始处理时间', null=True, blank=True)
    processing_completed_at = models.DateTimeField(verbose_name='处理完成时间', null=True, blank=True)
    
    # 错误信息
    error_message = models.TextField(verbose_name='错误信息', blank=True)
    error_details = models.JSONField(default=dict, verbose_name='详细错误信息')
    
    # 任务ID
    task_id = models.CharField(max_length=100, verbose_name='任务ID', blank=True, db_index=True)
    
    class Meta:
        verbose_name = '导入批次'
        verbose_name_plural = '导入批次'
        ordering = ['-import_timestamp']
        indexes = [
            models.Index(fields=['status', 'uploaded_by']),
            models.Index(fields=['task_id']),
        ]
    
    def __str__(self):
        return f"{self.batch_name} ({self.batch_id})"


class ClassificationRule(models.Model):
    """分类规则模型"""
    RULE_TYPE_CHOICES = [
        ('project', '项目提取'),
        ('category', '缺陷分类'),
        ('commonality', '共性问题'),
    ]
    
    SCOPE_CHOICES = [
        ('global', '全局规则'),
        ('personal', '个人规则'),
    ]
    
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES, verbose_name='规则类型')
    rule_name = models.CharField(max_length=100, verbose_name='规则名称')
    pattern = models.TextField(verbose_name='匹配模式')
    target_value = models.CharField(max_length=100, verbose_name='目标值')
    priority = models.IntegerField(default=0, verbose_name='优先级')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, verbose_name='规则描述')
    
    # 权限和作用域控制
    scope = models.CharField(max_length=20, choices=SCOPE_CHOICES, default='personal', verbose_name='规则作用域')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建用户', related_name='created_rules')
    
    # 规则来源追踪
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', blank=True, db_index=True)
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = '分类规则'
        verbose_name_plural = '分类规则'
        ordering = ['scope', '-priority', 'rule_type']
        indexes = [
            models.Index(fields=['scope', 'created_by', 'is_active']),
            models.Index(fields=['rule_type', 'scope', 'priority']),
        ]
    
    def __str__(self):
        return f"{self.rule_name} ({self.get_scope_display()})"
    
    def can_edit(self, user):
        """检查用户是否可以编辑此规则"""
        if self.scope == 'global':
            return user.is_staff or hasattr(user, 'userprofile') and user.userprofile.can_manage_rules
        else:
            return self.created_by == user
