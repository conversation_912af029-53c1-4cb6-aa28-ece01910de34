from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    DefectRecordViewSet, ImportBatchViewSet, 
    ClassificationRuleViewSet, FieldMappingViewSet
)

router = DefaultRouter()
router.register(r'records', DefectRecordViewSet, basename='defectrecord')
router.register(r'batches', ImportBatchViewSet, basename='importbatch')
router.register(r'rules', ClassificationRuleViewSet, basename='classificationrule')
router.register(r'field-mappings', FieldMappingViewSet, basename='fieldmapping')

urlpatterns = [
    path('', include(router.urls)),
]
