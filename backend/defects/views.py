import os
import tempfile
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from .models import DefectRecord, ImportBatch, ClassificationRule, FieldMapping
from .serializers import (
    DefectRecordSerializer, DefectRecordListSerializer, ImportBatchSerializer,
    ClassificationRuleSerializer, FieldMappingSerializer, FileUploadSerializer,
    DefectFilterSerializer, ExportSerializer
)
from .services import (
    DefectDataImportService, TaskQueueService, FieldMappingService,
    AutoClassificationService
)
from users.models import UserProfile
from users.permissions import DataPermissionMixin
import logging

logger = logging.getLogger(__name__)


class DefectRecordViewSet(DataPermissionMixin, viewsets.ModelViewSet):
    """缺陷记录视图集"""
    serializer_class = DefectRecordSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'priority', 'assignee', 'project_key', 'import_batch_id']
    search_fields = ['issue_key', 'summary', 'description']
    ordering_fields = ['created', 'updated', 'priority']
    ordering = ['-import_timestamp', '-created']
    
    def get_queryset(self):
        """获取用户可访问的缺陷记录"""
        return self.get_accessible_defects()
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return DefectRecordListSerializer
        return DefectRecordSerializer
    
    @action(detail=False, methods=['post'], parser_classes=[MultiPartParser, FormParser])
    def import_data(self, request):
        """导入缺陷数据"""
        serializer = FileUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 保存上传的文件到临时目录
            uploaded_file = serializer.validated_data['file']
            batch_name = serializer.validated_data.get('batch_name')
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{uploaded_file.name.split(".")[-1]}') as temp_file:
                for chunk in uploaded_file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name
            
            # 提交导入任务
            task_service = TaskQueueService()
            result = task_service.submit_import_task(
                user=request.user,
                file_path=temp_file_path,
                batch_name=batch_name
            )
            
            return Response(result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"导入数据失败: {str(e)}")
            return Response(
                {'error': f'导入失败: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['post'], parser_classes=[MultiPartParser, FormParser])
    def validate_headers(self, request):
        """验证CSV表头字段"""
        serializer = FileUploadSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            uploaded_file = serializer.validated_data['file']
            
            # 保存临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{uploaded_file.name.split(".")[-1]}') as temp_file:
                for chunk in uploaded_file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name
            
            # 读取文件头部
            import_service = DefectDataImportService(request.user)
            raw_data = import_service._read_data(temp_file_path, 'excel' if uploaded_file.name.endswith(('.xlsx', '.xls')) else 'csv')
            
            if raw_data:
                headers = list(raw_data[0].keys())
                field_service = FieldMappingService()
                validation_result = field_service.validate_csv_headers(headers)
                
                # 清理临时文件
                os.unlink(temp_file_path)
                
                return Response(validation_result)
            else:
                return Response({'error': '文件为空'}, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"验证表头失败: {str(e)}")
            return Response(
                {'error': f'验证失败: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def export_data(self, request):
        """导出缺陷数据"""
        serializer = ExportSerializer(data=request.query_params)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 获取导出参数
            export_format = serializer.validated_data.get('format', 'excel')
            include_all_fields = serializer.validated_data.get('include_all_fields', True)
            batch_id = serializer.validated_data.get('batch_id')
            
            # 构建查询集
            queryset = self.get_queryset()
            if batch_id:
                queryset = queryset.filter(import_batch_id=batch_id)
            
            # 应用筛选条件
            filters = serializer.validated_data.get('filters', {})
            if filters:
                if filters.get('status'):
                    queryset = queryset.filter(status=filters['status'])
                if filters.get('priority'):
                    queryset = queryset.filter(priority=filters['priority'])
                if filters.get('keyword'):
                    queryset = queryset.filter(
                        Q(issue_key__icontains=filters['keyword']) |
                        Q(summary__icontains=filters['keyword'])
                    )
            
            # 导出数据
            from .services import DefectDataExportService
            export_service = DefectDataExportService(request.user)
            
            if export_format == 'excel':
                output = export_service.export_defects_to_excel(queryset, include_all_fields)
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                filename = f'defects_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            else:
                output = export_service.export_defects_to_csv(queryset, include_all_fields)
                content_type = 'text/csv'
                filename = f'defects_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv'
            
            response = HttpResponse(output.getvalue(), content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
            
        except Exception as e:
            logger.error(f"导出数据失败: {str(e)}")
            return Response(
                {'error': f'导出失败: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['post'])
    def batch_classify(self, request):
        """批量分类"""
        defect_ids = request.data.get('defect_ids', [])
        if not defect_ids:
            return Response({'error': '请选择要分类的缺陷'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # 获取用户可访问的缺陷
            queryset = self.get_queryset().filter(id__in=defect_ids)
            
            # 执行自动分类
            classification_service = AutoClassificationService(request.user)
            updated_defects = []
            
            for defect in queryset:
                # 重新分类
                title = defect.summary or ''
                description = defect.description or ''
                
                defect.project_extracted = classification_service.extract_project(title)
                defect.summary_text = classification_service.extract_summary_text(title)
                defect.bug_category = classification_service.classify_bug_category(title, description)
                defect.commonality = classification_service.identify_commonality(defect.bug_category)
                
                defect.save()
                updated_defects.append(defect)
            
            serializer = DefectRecordListSerializer(updated_defects, many=True)
            return Response({
                'message': f'成功分类 {len(updated_defects)} 条缺陷',
                'defects': serializer.data
            })
            
        except Exception as e:
            logger.error(f"批量分类失败: {str(e)}")
            return Response(
                {'error': f'分类失败: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def supported_fields(self, request):
        """获取支持的字段列表"""
        field_service = FieldMappingService()
        fields = field_service.get_supported_fields()
        return Response({'fields': fields})


class ImportBatchViewSet(DataPermissionMixin, viewsets.ReadOnlyModelViewSet):
    """导入批次视图集"""
    serializer_class = ImportBatchSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['status', 'data_source']
    ordering = ['-import_timestamp']
    
    def get_queryset(self):
        """获取用户的导入批次"""
        if self.is_admin:
            return ImportBatch.objects.all()
        return ImportBatch.objects.filter(uploaded_by=self.request.user)
    
    @action(detail=True, methods=['get'])
    def defects(self, request, pk=None):
        """获取批次下的缺陷数据"""
        batch = self.get_object()
        defects = self.get_accessible_defects().filter(import_batch_id=batch.batch_id)
        
        # 分页
        page = self.paginate_queryset(defects)
        if page is not None:
            serializer = DefectRecordListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = DefectRecordListSerializer(defects, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def export(self, request, pk=None):
        """导出批次数据"""
        batch = self.get_object()
        
        try:
            # 获取批次下的缺陷数据
            defects = self.get_accessible_defects().filter(import_batch_id=batch.batch_id)
            
            # 导出数据
            from .services import DefectDataExportService
            export_service = DefectDataExportService(request.user)
            
            export_format = request.query_params.get('format', 'excel')
            if export_format == 'excel':
                output = export_service.export_defects_to_excel(defects, True)
                content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                filename = f'batch_{batch.batch_id}_export.xlsx'
            else:
                output = export_service.export_defects_to_csv(defects, True)
                content_type = 'text/csv'
                filename = f'batch_{batch.batch_id}_export.csv'
            
            response = HttpResponse(output.getvalue(), content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
            
        except Exception as e:
            logger.error(f"导出批次数据失败: {str(e)}")
            return Response(
                {'error': f'导出失败: {str(e)}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class ClassificationRuleViewSet(viewsets.ModelViewSet):
    """分类规则视图集"""
    serializer_class = ClassificationRuleSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['rule_type', 'scope', 'is_active']
    ordering = ['scope', '-priority', 'rule_type']
    
    def get_queryset(self):
        """获取用户可访问的规则"""
        user = self.request.user
        if user.is_staff or (hasattr(user, 'userprofile') and user.userprofile.can_manage_rules):
            # 管理员可以看到所有规则
            return ClassificationRule.objects.all()
        else:
            # 普通用户只能看到全局规则和自己的个人规则
            return ClassificationRule.objects.filter(
                Q(scope='global') | Q(scope='personal', created_by=user)
            )
    
    def perform_create(self, serializer):
        """创建规则时设置创建者"""
        serializer.save(created_by=self.request.user)
    
    def perform_update(self, serializer):
        """更新规则时检查权限"""
        rule = self.get_object()
        if not rule.can_edit(self.request.user):
            raise PermissionError("没有编辑此规则的权限")
        serializer.save()
    
    def perform_destroy(self, serializer):
        """删除规则时检查权限"""
        rule = self.get_object()
        if not rule.can_edit(self.request.user):
            raise PermissionError("没有删除此规则的权限")
        rule.delete()
    
    @action(detail=False, methods=['post'])
    def test_rule(self, request):
        """测试分类规则"""
        pattern = request.data.get('pattern')
        test_text = request.data.get('test_text')
        
        if not pattern or not test_text:
            return Response(
                {'error': '请提供匹配模式和测试文本'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            import re
            if re.search(pattern, test_text, re.IGNORECASE):
                return Response({'match': True, 'message': '匹配成功'})
            else:
                return Response({'match': False, 'message': '未匹配'})
        except re.error as e:
            return Response({
                'match': False, 
                'message': f'正则表达式错误: {str(e)}'
            })


class FieldMappingViewSet(viewsets.ModelViewSet):
    """字段映射视图集"""
    queryset = FieldMapping.objects.all()
    serializer_class = FieldMappingSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['field_type', 'is_required', 'is_core_field']
    ordering = ['-is_core_field', '-is_required', 'csv_field_name']
    
    def get_permissions(self):
        """只有管理员可以修改字段映射"""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            self.permission_classes = [IsAuthenticated]
            # 在视图中检查管理员权限
        return super().get_permissions()
    
    def perform_create(self, serializer):
        """创建字段映射时检查权限"""
        if not (self.request.user.is_staff or 
                (hasattr(self.request.user, 'userprofile') and 
                 self.request.user.userprofile.can_manage_rules)):
            raise PermissionError("只有管理员可以创建字段映射")
        serializer.save()
    
    def perform_update(self, serializer):
        """更新字段映射时检查权限"""
        if not (self.request.user.is_staff or 
                (hasattr(self.request.user, 'userprofile') and 
                 self.request.user.userprofile.can_manage_rules)):
            raise PermissionError("只有管理员可以修改字段映射")
        serializer.save()
    
    def perform_destroy(self, instance):
        """删除字段映射时检查权限"""
        if not (self.request.user.is_staff or 
                (hasattr(self.request.user, 'userprofile') and 
                 self.request.user.userprofile.can_manage_rules)):
            raise PermissionError("只有管理员可以删除字段映射")
        instance.delete()


# 添加缺失的导入
from django.utils import timezone
