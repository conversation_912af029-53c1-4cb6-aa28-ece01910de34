"""
URL configuration for defect_classification project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/defects/', include('apps.defects.urls')),
    path('api/tasks/', include('apps.tasks.urls')),
    path('api/users/', include('apps.users.urls')),
    path('api/notifications/', include('apps.notifications.urls')),
]

# 开发环境下提供静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
