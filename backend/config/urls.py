"""
URL configuration for defect_classification project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse


def api_root(request):
    """API根路径，返回可用的API端点"""
    return JsonResponse({
        'message': '缺陷分类工具 API',
        'version': '1.0.0',
        'endpoints': {
            'defects': '/api/defects/',
            'tasks': '/api/tasks/',
            'users': '/api/users/',
            'notifications': '/api/notifications/',
            'admin': '/admin/',
        },
        'documentation': {
            'defects_api': {
                'records': '/api/defects/records/',
                'field_mappings': '/api/defects/field-mappings/',
                'import_batches': '/api/defects/import-batches/',
                'classification_rules': '/api/defects/classification-rules/',
            }
        }
    })


urlpatterns = [
    # 管理后台
    path('admin/', admin.site.urls),

    # API根路径
    path('api/', api_root, name='api-root'),

    # 应用API
    path('api/defects/', include('apps.defects.urls')),
    path('api/tasks/', include('apps.tasks.urls')),
    path('api/users/', include('apps.users.urls')),
    path('api/notifications/', include('apps.notifications.urls')),
]

# 开发环境下提供静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
