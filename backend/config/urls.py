"""
URL configuration for defect_classification project.
"""
from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from django.views.generic import TemplateView


def api_root(request):
    """API根路径，返回可用的API端点"""
    return JsonResponse({
        'message': '缺陷分类工具 API',
        'version': '1.0.0',
        'endpoints': {
            'defects': '/api/defects/',
            'tasks': '/api/tasks/',
            'users': '/api/users/',
            'notifications': '/api/notifications/',
            'dashboard': '/api/dashboard/',
            'admin': '/admin/',
        },
        'documentation': {
            'defects_api': {
                'records': '/api/defects/records/',
                'field_mappings': '/api/defects/field-mappings/',
                'import_batches': '/api/defects/import-batches/',
                'classification_rules': '/api/defects/classification-rules/',
            }
        }
    })


urlpatterns = [
    # 管理后台
    path('admin/', admin.site.urls),

    # API根路径
    path('api/', api_root, name='api-root'),

    # 应用API
    path('api/defects/', include('apps.defects.urls')),
    path('api/tasks/', include('apps.tasks.urls')),
    path('api/users/', include('apps.users.urls')),
    path('api/notifications/', include('apps.notifications.urls')),
    path('api/dashboard/', include('apps.dashboard.urls')),

    # 认证API
    path('api/auth/', include('apps.users.auth_urls')),
]

# 开发环境下提供静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# 前端路由支持 - 将所有非API请求重定向到前端
# 注意：这个应该放在最后，作为兜底路由
urlpatterns += [
    # 前端路由兜底，所有未匹配的路径都返回前端应用
    re_path(r'^(?!api/|admin/).*$', TemplateView.as_view(template_name='index.html'), name='frontend'),
]

# 开发环境下提供静态文件和媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
