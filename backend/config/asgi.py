"""
ASGI config for defect_classification project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/asgi/
"""

import os
from django.core.asgi import get_asgi_application

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# 获取Django ASGI应用
django_asgi_app = get_asgi_application()

# 如果启用了WebSocket，则配置Channels
try:
    from channels.routing import ProtocolTypeRouter, URLRouter
    from channels.auth import AuthMiddlewareStack
    from channels.security.websocket import AllowedHostsOriginValidator
    import apps.notifications.routing

    application = ProtocolTypeRouter({
        "http": django_asgi_app,
        "websocket": AllowedHostsOriginValidator(
            AuthMiddlewareStack(
                URLRouter(
                    apps.notifications.routing.websocket_urlpatterns
                )
            )
        ),
    })
except ImportError:
    # 如果没有安装channels或相关依赖，则只使用HTTP
    application = django_asgi_app
