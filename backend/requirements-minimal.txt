# 最小化依赖 - 只包含核心功能，避免编译问题

# Django核心 - 必需
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1

# 基础工具 - 必需
python-dotenv==1.0.0
django-filter==23.5

# 数据库 - 使用SQLite，避免PostgreSQL编译问题
# psycopg2-binary==2.9.9  # 可选，如果需要PostgreSQL

# 简单任务处理 - 避免Celery复杂性
# 可以先不安装Celery，使用同步处理

# 基础数据处理 - 可选
# pandas==2.1.4  # 如果需要数据分析功能
# openpyxl==3.1.2  # 如果需要Excel支持

# 部署相关 - 开发环境可选
# gunicorn==21.2.0
# whitenoise==6.6.0
