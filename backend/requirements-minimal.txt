# 最小化依赖 - 只包含核心功能，避免编译问题

# Django核心 - 必需
Django>=4.2,<5.0
djangorestframework>=3.14.0
django-cors-headers>=4.0.0

# 基础工具 - 必需
python-dotenv>=1.0.0
django-filter>=23.0

# 静态文件处理
whitenoise>=6.5.0

# 数据库 - 默认SQLite，可选PostgreSQL
# psycopg2-binary>=2.9.0  # 如果需要PostgreSQL，取消注释

# 基础数据处理 - 可选
# pandas>=2.0.0  # 如果需要数据分析功能，取消注释
# openpyxl>=3.1.0  # 如果需要Excel支持，取消注释

# 异步任务 - 可选
# celery>=5.2.0  # 如果需要异步任务，取消注释
# redis>=4.5.0   # 如果需要Redis，取消注释

# WebSocket - 可选
# channels>=4.0.0  # 如果需要WebSocket，取消注释
# channels-redis>=4.0.0  # 如果需要WebSocket+Redis，取消注释
