import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from channels.auth import AuthMiddlewareStack
from channels.security.websocket import AllowedHostsOriginValidator
import notifications.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'defect_classification.settings')

django_asgi_app = get_asgi_application()

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                notifications.routing.websocket_urlpatterns
            )
        )
    ),
})
