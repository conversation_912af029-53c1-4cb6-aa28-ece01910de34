from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import NotificationViewSet

# 创建路由器并注册ViewSet
router = DefaultRouter()
router.register(r'notifications', NotificationViewSet, basename='notification')

# URL模式
urlpatterns = [
    # API路由
    path('', include(router.urls)),
]

# 生成的URL模式：
# GET    /api/notifications/notifications/                    - 获取通知列表
# POST   /api/notifications/notifications/                    - 创建通知
# GET    /api/notifications/notifications/{id}/               - 获取单个通知
# PUT    /api/notifications/notifications/{id}/               - 更新通知
# PATCH  /api/notifications/notifications/{id}/               - 部分更新通知
# DELETE /api/notifications/notifications/{id}/               - 删除通知
# POST   /api/notifications/notifications/{id}/mark_as_read/  - 标记为已读
# POST   /api/notifications/notifications/mark_all_as_read/   - 标记所有为已读
# GET    /api/notifications/notifications/unread_count/       - 获取未读数量
# GET    /api/notifications/notifications/recent_notifications/ - 获取最近通知
# DELETE /api/notifications/notifications/clear_read_notifications/ - 清理已读通知
# POST   /api/notifications/notifications/batch_mark_as_read/ - 批量标记已读
# DELETE /api/notifications/notifications/batch_delete/       - 批量删除
# GET    /api/notifications/notifications/statistics/         - 获取统计信息
