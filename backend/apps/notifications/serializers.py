from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Notification


class NotificationSerializer(serializers.ModelSerializer):
    """通知序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    notification_type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    time_since_created = serializers.SerializerMethodField()
    time_since_read = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = '__all__'
        read_only_fields = ['user', 'created_at', 'read_at']
    
    def get_time_since_created(self, obj):
        """获取创建时间距现在的时长"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        delta = now - obj.created_at
        
        if delta.days > 0:
            return f"{delta.days}天前"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return f"{hours}小时前"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    
    def get_time_since_read(self, obj):
        """获取阅读时间距现在的时长"""
        if not obj.read_at:
            return None
        
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        delta = now - obj.read_at
        
        if delta.days > 0:
            return f"{delta.days}天前"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return f"{hours}小时前"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"
    
    def validate_notification_type(self, value):
        """验证通知类型"""
        valid_types = [choice[0] for choice in Notification.NOTIFICATION_TYPE_CHOICES]
        if value not in valid_types:
            raise serializers.ValidationError(f"无效的通知类型: {value}")
        return value
    
    def validate_title(self, value):
        """验证标题"""
        if not value.strip():
            raise serializers.ValidationError("标题不能为空")
        if len(value) > 200:
            raise serializers.ValidationError("标题长度不能超过200个字符")
        return value.strip()
    
    def validate_message(self, value):
        """验证消息内容"""
        if not value.strip():
            raise serializers.ValidationError("消息内容不能为空")
        return value.strip()


class NotificationListSerializer(serializers.ModelSerializer):
    """通知列表序列化器（简化版）"""
    notification_type_display = serializers.CharField(source='get_notification_type_display', read_only=True)
    time_since_created = serializers.SerializerMethodField()
    
    class Meta:
        model = Notification
        fields = [
            'id', 'notification_type', 'notification_type_display',
            'title', 'is_read', 'created_at', 'time_since_created'
        ]
    
    def get_time_since_created(self, obj):
        """获取创建时间距现在的时长"""
        from django.utils import timezone
        
        now = timezone.now()
        delta = now - obj.created_at
        
        if delta.days > 0:
            return f"{delta.days}天前"
        elif delta.seconds > 3600:
            hours = delta.seconds // 3600
            return f"{hours}小时前"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"{minutes}分钟前"
        else:
            return "刚刚"


class NotificationCreateSerializer(serializers.ModelSerializer):
    """通知创建序列化器"""
    
    class Meta:
        model = Notification
        fields = [
            'notification_type', 'title', 'message', 'data'
        ]
    
    def validate_data(self, value):
        """验证附加数据"""
        if value is not None and not isinstance(value, dict):
            raise serializers.ValidationError("附加数据必须是字典格式")
        return value or {}
    
    def create(self, validated_data):
        """创建通知"""
        # 如果没有指定用户，使用当前用户
        if 'user' not in validated_data:
            request = self.context.get('request')
            if request and request.user:
                validated_data['user'] = request.user
        
        return super().create(validated_data)


class NotificationBulkCreateSerializer(serializers.Serializer):
    """批量创建通知序列化器"""
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="接收通知的用户ID列表"
    )
    notification_type = serializers.ChoiceField(
        choices=Notification.NOTIFICATION_TYPE_CHOICES
    )
    title = serializers.CharField(max_length=200)
    message = serializers.CharField()
    data = serializers.JSONField(required=False, default=dict)
    
    def validate_user_ids(self, value):
        """验证用户ID列表"""
        if not value:
            raise serializers.ValidationError("用户ID列表不能为空")
        
        # 检查用户是否存在
        existing_users = User.objects.filter(id__in=value).values_list('id', flat=True)
        invalid_ids = set(value) - set(existing_users)
        
        if invalid_ids:
            raise serializers.ValidationError(f"以下用户ID不存在: {list(invalid_ids)}")
        
        return value
    
    def create(self, validated_data):
        """批量创建通知"""
        user_ids = validated_data.pop('user_ids')
        
        notifications = []
        for user_id in user_ids:
            notification_data = validated_data.copy()
            notification_data['user_id'] = user_id
            notifications.append(Notification(**notification_data))
        
        # 批量创建
        created_notifications = Notification.objects.bulk_create(notifications)
        
        return {
            'created_count': len(created_notifications),
            'user_count': len(user_ids),
            'notifications': created_notifications
        }
