from django.shortcuts import render
from django.db.models import Q, Count
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from .models import Notification
from .serializers import NotificationSerializer, NotificationListSerializer
from .filters import NotificationFilter
from .permissions import IsNotificationOwnerOrAdmin


class NotificationViewSet(viewsets.ModelViewSet):
    """通知ViewSet"""
    queryset = Notification.objects.all()
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, IsNotificationOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = NotificationFilter
    search_fields = ['title', 'message']
    ordering_fields = ['created_at', 'read_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 用户只能看到自己的通知
        queryset = queryset.filter(user=user)

        return queryset

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action == 'list':
            return NotificationListSerializer
        return NotificationSerializer

    def perform_create(self, serializer):
        """创建通知时设置接收用户"""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """标记通知为已读"""
        notification = self.get_object()

        if not notification.is_read:
            notification.mark_as_read()

        return Response({'message': '通知已标记为已读'})

    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """标记所有通知为已读"""
        queryset = self.get_queryset().filter(is_read=False)

        from django.utils import timezone
        now = timezone.now()

        updated_count = queryset.update(is_read=True, read_at=now)

        return Response({
            'message': f'已标记 {updated_count} 条通知为已读',
            'updated_count': updated_count
        })

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """获取未读通知数量"""
        unread_count = self.get_queryset().filter(is_read=False).count()

        return Response({
            'unread_count': unread_count
        })

    @action(detail=False, methods=['get'])
    def recent_notifications(self, request):
        """获取最近的通知"""
        # 获取最近7天的通知
        from django.utils import timezone
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)

        queryset = self.get_queryset().filter(created_at__gte=week_ago)
        queryset = self.filter_queryset(queryset)

        # 限制数量
        limit = int(request.query_params.get('limit', 10))
        queryset = queryset[:limit]

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['delete'])
    def clear_read_notifications(self, request):
        """清理已读通知"""
        read_notifications = self.get_queryset().filter(is_read=True)

        # 只删除7天前的已读通知
        from django.utils import timezone
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        old_read_notifications = read_notifications.filter(read_at__lt=week_ago)

        deleted_count = old_read_notifications.count()
        old_read_notifications.delete()

        return Response({
            'message': f'已清理 {deleted_count} 条已读通知',
            'deleted_count': deleted_count
        })

    @action(detail=False, methods=['post'])
    def batch_mark_as_read(self, request):
        """批量标记通知为已读"""
        notification_ids = request.data.get('notification_ids', [])

        if not notification_ids:
            return Response(
                {'error': '请选择要标记的通知'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取用户的通知
        queryset = self.get_queryset().filter(
            id__in=notification_ids,
            is_read=False
        )

        from django.utils import timezone
        now = timezone.now()

        updated_count = queryset.update(is_read=True, read_at=now)

        return Response({
            'message': f'已标记 {updated_count} 条通知为已读',
            'updated_count': updated_count
        })

    @action(detail=False, methods=['delete'])
    def batch_delete(self, request):
        """批量删除通知"""
        notification_ids = request.data.get('notification_ids', [])

        if not notification_ids:
            return Response(
                {'error': '请选择要删除的通知'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取用户的通知
        queryset = self.get_queryset().filter(id__in=notification_ids)

        deleted_count = queryset.count()
        queryset.delete()

        return Response({
            'message': f'已删除 {deleted_count} 条通知',
            'deleted_count': deleted_count
        })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取通知统计"""
        queryset = self.get_queryset()

        # 基础统计
        total_count = queryset.count()
        unread_count = queryset.filter(is_read=False).count()
        read_count = queryset.filter(is_read=True).count()

        # 按类型统计
        type_stats = queryset.values('notification_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 最近7天的通知统计
        from django.utils import timezone
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        recent_count = queryset.filter(created_at__gte=week_ago).count()

        return Response({
            'total_notifications': total_count,
            'unread_notifications': unread_count,
            'read_notifications': read_count,
            'recent_notifications': recent_count,
            'type_distribution': list(type_stats),
            'read_rate': round((read_count / total_count) * 100, 2) if total_count > 0 else 0,
        })
