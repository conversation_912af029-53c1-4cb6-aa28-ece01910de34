import django_filters
from django.db.models import Q
from .models import Notification


class NotificationFilter(django_filters.FilterSet):
    """通知过滤器"""
    
    # 基础字段过滤
    notification_type = django_filters.MultipleChoiceFilter(
        choices=Notification.NOTIFICATION_TYPE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    title = django_filters.CharFilter(lookup_expr='icontains')
    message = django_filters.CharFilter(lookup_expr='icontains')
    is_read = django_filters.BooleanFilter()
    
    # 时间范围过滤
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    read_after = django_filters.DateTimeFilter(field_name='read_at', lookup_expr='gte')
    read_before = django_filters.DateTimeFilter(field_name='read_at', lookup_expr='lte')
    
    # 自定义过滤
    is_recent = django_filters.BooleanFilter(method='filter_is_recent')
    has_data = django_filters.BooleanFilter(method='filter_has_data')
    
    # 复合搜索
    search = django_filters.CharFilter(method='filter_search')
    
    class Meta:
        model = Notification
        fields = ['notification_type', 'is_read']
    
    def filter_is_recent(self, queryset, name, value):
        """过滤最近的通知（7天内）"""
        if value:
            from django.utils import timezone
            from datetime import timedelta
            week_ago = timezone.now() - timedelta(days=7)
            return queryset.filter(created_at__gte=week_ago)
        else:
            from django.utils import timezone
            from datetime import timedelta
            week_ago = timezone.now() - timedelta(days=7)
            return queryset.filter(created_at__lt=week_ago)
    
    def filter_has_data(self, queryset, name, value):
        """过滤是否有附加数据"""
        if value:
            return queryset.exclude(data__exact={})
        else:
            return queryset.filter(data__exact={})
    
    def filter_search(self, queryset, name, value):
        """全文搜索"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(title__icontains=value) |
            Q(message__icontains=value)
        )
