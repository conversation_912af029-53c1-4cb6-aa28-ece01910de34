from rest_framework import permissions


class IsNotificationOwnerOrAdmin(permissions.BasePermission):
    """
    自定义权限：只有通知接收者或管理员可以访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有通知
        if request.user.is_staff:
            return True
        
        # 检查是否是通知接收者
        return obj.user == request.user


class CanCreateNotifications(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以创建通知
    """
    
    def has_permission(self, request, view):
        if request.method != 'POST':
            return True
        
        # 只有管理员可以创建系统通知
        notification_type = request.data.get('notification_type')
        if notification_type == 'system_message':
            return request.user.is_staff
        
        # 其他类型的通知通常由系统自动创建
        return request.user.is_authenticated


class CanBulkCreateNotifications(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以批量创建通知
    """
    
    def has_permission(self, request, view):
        # 只有管理员可以批量创建通知
        return request.user.is_staff
