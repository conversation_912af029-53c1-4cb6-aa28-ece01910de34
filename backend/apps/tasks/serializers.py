from rest_framework import serializers
from django.contrib.auth.models import User
from .models import TaskQueue


class TaskQueueSerializer(serializers.ModelSerializer):
    """任务队列序列化器"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    duration = serializers.SerializerMethodField()
    can_cancel = serializers.SerializerMethodField()
    can_retry = serializers.SerializerMethodField()
    
    class Meta:
        model = TaskQueue
        fields = '__all__'
        read_only_fields = [
            'task_id', 'created_by', 'created_at', 'started_at', 
            'completed_at', 'status', 'progress', 'current_step',
            'result_data', 'error_message'
        ]
    
    def get_duration(self, obj):
        """获取任务执行时长（秒）"""
        if obj.started_at and obj.completed_at:
            delta = obj.completed_at - obj.started_at
            return int(delta.total_seconds())
        elif obj.started_at:
            from django.utils import timezone
            delta = timezone.now() - obj.started_at
            return int(delta.total_seconds())
        return None
    
    def get_can_cancel(self, obj):
        """检查是否可以取消"""
        return obj.status in ['pending', 'running']
    
    def get_can_retry(self, obj):
        """检查是否可以重试"""
        return obj.status == 'failed'
    
    def validate_task_type(self, value):
        """验证任务类型"""
        valid_types = [choice[0] for choice in TaskQueue.TASK_TYPE_CHOICES]
        if value not in valid_types:
            raise serializers.ValidationError(f"无效的任务类型: {value}")
        return value
    
    def validate_priority(self, value):
        """验证优先级"""
        valid_priorities = [choice[0] for choice in TaskQueue.PRIORITY_CHOICES]
        if value not in valid_priorities:
            raise serializers.ValidationError(f"无效的优先级: {value}")
        return value


class TaskQueueListSerializer(serializers.ModelSerializer):
    """任务队列列表序列化器（简化版）"""
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    duration = serializers.SerializerMethodField()
    can_cancel = serializers.SerializerMethodField()
    
    class Meta:
        model = TaskQueue
        fields = [
            'id', 'task_name', 'task_type', 'status', 'priority',
            'progress', 'current_step', 'created_by_name', 
            'created_at', 'started_at', 'completed_at', 'duration',
            'can_cancel'
        ]
    
    def get_duration(self, obj):
        """获取任务执行时长（秒）"""
        if obj.started_at and obj.completed_at:
            delta = obj.completed_at - obj.started_at
            return int(delta.total_seconds())
        elif obj.started_at:
            from django.utils import timezone
            delta = timezone.now() - obj.started_at
            return int(delta.total_seconds())
        return None
    
    def get_can_cancel(self, obj):
        """检查是否可以取消"""
        return obj.status in ['pending', 'running']


class TaskQueueCreateSerializer(serializers.ModelSerializer):
    """任务队列创建序列化器"""
    
    class Meta:
        model = TaskQueue
        fields = [
            'task_name', 'task_type', 'task_params', 
            'priority', 'estimated_duration'
        ]
    
    def validate_task_params(self, value):
        """验证任务参数"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("任务参数必须是字典格式")
        return value
    
    def create(self, validated_data):
        """创建任务时生成task_id"""
        import uuid
        validated_data['task_id'] = str(uuid.uuid4())
        return super().create(validated_data)
