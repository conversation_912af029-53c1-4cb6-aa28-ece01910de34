from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import TaskQueueViewSet

# 创建路由器并注册ViewSet
router = DefaultRouter()
router.register(r'queue', TaskQueueViewSet, basename='taskqueue')

# URL模式
urlpatterns = [
    # API路由
    path('', include(router.urls)),
]

# 生成的URL模式：
# GET    /api/tasks/queue/                         - 获取任务列表
# POST   /api/tasks/queue/                         - 创建任务
# GET    /api/tasks/queue/{id}/                    - 获取单个任务
# PUT    /api/tasks/queue/{id}/                    - 更新任务
# PATCH  /api/tasks/queue/{id}/                    - 部分更新任务
# DELETE /api/tasks/queue/{id}/                    - 删除任务
# POST   /api/tasks/queue/{id}/cancel/             - 取消任务
# POST   /api/tasks/queue/{id}/retry/              - 重试任务
# GET    /api/tasks/queue/queue_status/            - 获取队列状态
# DELETE /api/tasks/queue/clear_completed/         - 清理已完成任务
# POST   /api/tasks/queue/batch_cancel/            - 批量取消任务
