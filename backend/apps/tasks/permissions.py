from rest_framework import permissions


class IsTaskOwnerOrAdmin(permissions.BasePermission):
    """
    自定义权限：只有任务创建者或管理员可以访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有任务
        if request.user.is_staff:
            return True
        
        # 检查是否是任务创建者
        return obj.created_by == request.user


class CanManageTasks(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以管理任务
    """
    
    def has_permission(self, request, view):
        # 管理员可以管理所有任务
        if request.user.is_staff:
            return True
        
        # 检查用户是否有任务管理权限
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.max_concurrent_tasks > 0
        
        return True  # 默认允许普通用户管理自己的任务


class CanCreateTasks(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以创建任务
    """
    
    def has_permission(self, request, view):
        if request.method != 'POST':
            return True
        
        user = request.user
        
        # 管理员无限制
        if user.is_staff:
            return True
        
        # 检查用户的并发任务限制
        if hasattr(user, 'userprofile'):
            max_concurrent = user.userprofile.max_concurrent_tasks
            current_running = user.taskqueue_set.filter(
                status__in=['pending', 'running']
            ).count()
            
            if current_running >= max_concurrent:
                return False
        
        return True
