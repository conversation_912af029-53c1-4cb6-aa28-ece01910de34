from django.db import models
from django.contrib.auth.models import User


class TaskQueue(models.Model):
    """任务队列管理模型"""
    TASK_TYPE_CHOICES = [
        ('data_import', '数据导入'),
        ('data_analysis', '数据分析'),
        ('rule_import', '规则导入'),
        ('batch_export', '批量导出'),
    ]
    
    PRIORITY_CHOICES = [
        (1, '低优先级'),
        (2, '普通优先级'),
        (3, '高优先级'),
        (4, '紧急优先级'),
    ]
    
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    task_id = models.CharField(max_length=100, unique=True, verbose_name='任务ID')
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES, verbose_name='任务类型')
    task_name = models.CharField(max_length=200, verbose_name='任务名称')
    
    # 用户信息
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='创建用户')
    
    # 任务参数
    task_params = models.JSONField(default=dict, verbose_name='任务参数')
    
    # 优先级和队列
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2, verbose_name='优先级')
    queue_position = models.IntegerField(default=0, verbose_name='队列位置')
    
    # 状态和进度
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='任务状态')
    progress = models.FloatField(default=0.0, verbose_name='执行进度')
    current_step = models.CharField(max_length=100, verbose_name='当前步骤', blank=True)
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    started_at = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    completed_at = models.DateTimeField(verbose_name='完成时间', null=True, blank=True)
    estimated_duration = models.IntegerField(default=0, verbose_name='预估执行时间(秒)')
    
    # 结果和错误
    result_data = models.JSONField(default=dict, verbose_name='执行结果')
    error_message = models.TextField(verbose_name='错误信息', blank=True)
    
    # 关联对象
    related_batch_id = models.CharField(max_length=50, verbose_name='关联批次ID', blank=True, db_index=True)
    
    class Meta:
        verbose_name = '任务队列'
        verbose_name_plural = '任务队列'
        ordering = ['-priority', 'queue_position', 'created_at']
        indexes = [
            models.Index(fields=['status', 'priority', 'queue_position']),
            models.Index(fields=['created_by', 'status']),
            models.Index(fields=['task_type', 'status']),
        ]
    
    def __str__(self):
        return f"{self.task_name} ({self.get_status_display()})"
