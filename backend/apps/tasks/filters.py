import django_filters
from django.db.models import Q
from .models import TaskQueue


class TaskQueueFilter(django_filters.FilterSet):
    """任务队列过滤器"""
    
    # 基础字段过滤
    task_name = django_filters.CharFilter(lookup_expr='icontains')
    task_id = django_filters.CharFilter(lookup_expr='icontains')
    task_type = django_filters.MultipleChoiceFilter(
        choices=TaskQueue.TASK_TYPE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    status = django_filters.MultipleChoiceFilter(
        choices=TaskQueue.STATUS_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    priority = django_filters.MultipleChoiceFilter(
        choices=TaskQueue.PRIORITY_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    
    # 时间范围过滤
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    started_after = django_filters.DateTimeFilter(field_name='started_at', lookup_expr='gte')
    started_before = django_filters.DateTimeFilter(field_name='started_at', lookup_expr='lte')
    completed_after = django_filters.DateTimeFilter(field_name='completed_at', lookup_expr='gte')
    completed_before = django_filters.DateTimeFilter(field_name='completed_at', lookup_expr='lte')
    
    # 进度范围过滤
    min_progress = django_filters.NumberFilter(field_name='progress', lookup_expr='gte')
    max_progress = django_filters.NumberFilter(field_name='progress', lookup_expr='lte')
    
    # 执行时间范围过滤
    min_duration = django_filters.NumberFilter(field_name='estimated_duration', lookup_expr='gte')
    max_duration = django_filters.NumberFilter(field_name='estimated_duration', lookup_expr='lte')
    
    # 关联对象过滤
    related_batch_id = django_filters.CharFilter()
    
    # 自定义过滤
    is_running = django_filters.BooleanFilter(method='filter_is_running')
    is_completed = django_filters.BooleanFilter(method='filter_is_completed')
    has_error = django_filters.BooleanFilter(method='filter_has_error')
    can_cancel = django_filters.BooleanFilter(method='filter_can_cancel')
    
    # 复合搜索
    search = django_filters.CharFilter(method='filter_search')
    
    class Meta:
        model = TaskQueue
        fields = [
            'task_name', 'task_type', 'status', 'priority',
            'related_batch_id'
        ]
    
    def filter_is_running(self, queryset, name, value):
        """过滤正在运行的任务"""
        if value:
            return queryset.filter(status='running')
        else:
            return queryset.exclude(status='running')
    
    def filter_is_completed(self, queryset, name, value):
        """过滤已完成的任务"""
        if value:
            return queryset.filter(status__in=['completed', 'failed', 'cancelled'])
        else:
            return queryset.exclude(status__in=['completed', 'failed', 'cancelled'])
    
    def filter_has_error(self, queryset, name, value):
        """过滤有错误的任务"""
        if value:
            return queryset.exclude(error_message__exact='')
        else:
            return queryset.filter(error_message__exact='')
    
    def filter_can_cancel(self, queryset, name, value):
        """过滤可以取消的任务"""
        if value:
            return queryset.filter(status__in=['pending', 'running'])
        else:
            return queryset.exclude(status__in=['pending', 'running'])
    
    def filter_search(self, queryset, name, value):
        """全文搜索"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(task_name__icontains=value) |
            Q(task_id__icontains=value) |
            Q(current_step__icontains=value) |
            Q(error_message__icontains=value)
        )
