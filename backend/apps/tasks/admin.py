from django.contrib import admin
from .models import TaskQueue


@admin.register(TaskQueue)
class TaskQueueAdmin(admin.ModelAdmin):
    list_display = ['task_name', 'task_type', 'status', 'priority', 'created_by', 'created_at']
    list_filter = ['task_type', 'status', 'priority', 'created_by']
    search_fields = ['task_name', 'task_id']
    readonly_fields = ['task_id', 'created_at', 'started_at', 'completed_at']
