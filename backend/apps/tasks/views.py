from django.shortcuts import render
from django.db.models import Q, Count
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from .models import TaskQueue
from .serializers import TaskQueueSerializer, TaskQueueListSerializer
from .filters import TaskQueueFilter
from .permissions import IsTaskOwnerOrAdmin


class TaskQueueViewSet(viewsets.ModelViewSet):
    """任务队列ViewSet"""
    queryset = TaskQueue.objects.all()
    serializer_class = TaskQueueSerializer
    permission_classes = [IsAuthenticated, IsTaskOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = TaskQueueFilter
    search_fields = ['task_name', 'task_id']
    ordering_fields = ['created_at', 'started_at', 'completed_at', 'priority']
    ordering = ['-priority', 'created_at']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 如果用户不是管理员，只能看到自己的任务
        if not user.is_staff:
            queryset = queryset.filter(created_by=user)

        return queryset

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action == 'list':
            return TaskQueueListSerializer
        return TaskQueueSerializer

    def perform_create(self, serializer):
        """创建任务时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消任务"""
        task = self.get_object()

        if task.status not in ['pending', 'running']:
            return Response(
                {'error': '只能取消等待中或执行中的任务'},
                status=status.HTTP_400_BAD_REQUEST
            )

        task.status = 'cancelled'
        task.save()

        # 这里可以添加取消Celery任务的逻辑
        # if task.task_id:
        #     from celery import current_app
        #     current_app.control.revoke(task.task_id, terminate=True)

        return Response({'message': '任务已取消'})

    @action(detail=True, methods=['post'])
    def retry(self, request, pk=None):
        """重试失败的任务"""
        task = self.get_object()

        if task.status != 'failed':
            return Response(
                {'error': '只能重试失败的任务'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 重置任务状态
        task.status = 'pending'
        task.progress = 0.0
        task.current_step = ''
        task.error_message = ''
        task.started_at = None
        task.completed_at = None
        task.save()

        # 这里可以添加重新提交任务到Celery的逻辑

        return Response({'message': '任务已重新提交'})

    @action(detail=False, methods=['get'])
    def queue_status(self, request):
        """获取队列状态"""
        queryset = self.get_queryset()

        # 按状态统计
        status_stats = queryset.values('status').annotate(
            count=Count('id')
        ).order_by('status')

        # 按任务类型统计
        type_stats = queryset.values('task_type').annotate(
            count=Count('id')
        ).order_by('task_type')

        # 按优先级统计
        priority_stats = queryset.values('priority').annotate(
            count=Count('id')
        ).order_by('-priority')

        # 等待中的任务数量
        pending_count = queryset.filter(status='pending').count()
        running_count = queryset.filter(status='running').count()

        return Response({
            'total_tasks': queryset.count(),
            'pending_tasks': pending_count,
            'running_tasks': running_count,
            'status_distribution': list(status_stats),
            'type_distribution': list(type_stats),
            'priority_distribution': list(priority_stats),
        })

    @action(detail=False, methods=['delete'])
    def clear_completed(self, request):
        """清理已完成的任务"""
        queryset = self.get_queryset()
        completed_tasks = queryset.filter(status__in=['completed', 'failed', 'cancelled'])

        # 只删除7天前的任务
        from django.utils import timezone
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        old_completed_tasks = completed_tasks.filter(completed_at__lt=week_ago)

        deleted_count = old_completed_tasks.count()
        old_completed_tasks.delete()

        return Response({
            'message': f'已清理 {deleted_count} 个已完成的任务',
            'deleted_count': deleted_count
        })

    @action(detail=False, methods=['post'])
    def batch_cancel(self, request):
        """批量取消任务"""
        task_ids = request.data.get('task_ids', [])

        if not task_ids:
            return Response(
                {'error': '请选择要取消的任务'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取用户有权限的任务
        queryset = self.get_queryset().filter(
            id__in=task_ids,
            status__in=['pending', 'running']
        )

        cancelled_count = queryset.count()
        queryset.update(status='cancelled')

        return Response({
            'message': f'已取消 {cancelled_count} 个任务',
            'cancelled_count': cancelled_count
        })
