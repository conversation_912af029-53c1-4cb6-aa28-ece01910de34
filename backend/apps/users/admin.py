from django.contrib import admin
from .models import UserProfile, OperationLog


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'can_view_all_data', 'total_imports', 'last_import_at']
    list_filter = ['role', 'can_view_all_data', 'can_manage_rules']
    search_fields = ['user__username', 'user__email']


@admin.register(OperationLog)
class OperationLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'operation_type', 'target_type', 'target_id', 'created_at']
    list_filter = ['operation_type', 'target_type', 'created_at']
    search_fields = ['user__username', 'target_id']
    readonly_fields = ['created_at']
