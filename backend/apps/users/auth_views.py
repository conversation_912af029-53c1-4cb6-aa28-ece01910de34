from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from .serializers import UserSerializer


@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """用户登录"""
    username = request.data.get('username')
    password = request.data.get('password')
    
    if not username or not password:
        return Response(
            {'error': '用户名和密码不能为空'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 验证用户
    user = authenticate(username=username, password=password)
    if user is None:
        return Response(
            {'error': '用户名或密码错误'}, 
            status=status.HTTP_401_UNAUTHORIZED
        )
    
    if not user.is_active:
        return Response(
            {'error': '用户账户已被禁用'}, 
            status=status.HTTP_401_UNAUTHORIZED
        )
    
    # 登录用户
    login(request, user)
    
    # 获取或创建Token
    token, created = Token.objects.get_or_create(user=user)
    
    # 序列化用户信息
    user_serializer = UserSerializer(user)
    
    return Response({
        'token': token.key,
        'user': user_serializer.data,
        'message': '登录成功'
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """用户登出"""
    try:
        # 删除用户的Token
        request.user.auth_token.delete()
    except:
        pass
    
    # 登出用户
    logout(request)
    
    return Response({'message': '登出成功'})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info_view(request):
    """获取当前用户信息"""
    serializer = UserSerializer(request.user)
    return Response(serializer.data)


@api_view(['POST'])
@permission_classes([AllowAny])
def register_view(request):
    """用户注册"""
    username = request.data.get('username')
    password = request.data.get('password')
    email = request.data.get('email', '')
    
    if not username or not password:
        return Response(
            {'error': '用户名和密码不能为空'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 检查用户名是否已存在
    if User.objects.filter(username=username).exists():
        return Response(
            {'error': '用户名已存在'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # 创建用户
    try:
        user = User.objects.create_user(
            username=username,
            password=password,
            email=email
        )
        
        # 创建Token
        token = Token.objects.create(user=user)
        
        # 序列化用户信息
        user_serializer = UserSerializer(user)
        
        return Response({
            'token': token.key,
            'user': user_serializer.data,
            'message': '注册成功'
        }, status=status.HTTP_201_CREATED)
        
    except Exception as e:
        return Response(
            {'error': f'注册失败: {str(e)}'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
