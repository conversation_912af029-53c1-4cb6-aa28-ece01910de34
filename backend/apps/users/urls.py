from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import UserViewSet, UserProfileViewSet, OperationLogViewSet

# 创建路由器并注册ViewSet
router = DefaultRouter()
router.register(r'users', UserViewSet, basename='user')
router.register(r'profiles', UserProfileViewSet, basename='userprofile')
router.register(r'operation-logs', OperationLogViewSet, basename='operationlog')

# URL模式
urlpatterns = [
    # API路由
    path('', include(router.urls)),
]

# 生成的URL模式：
# GET    /api/users/users/                         - 获取用户列表
# POST   /api/users/users/                         - 创建用户
# GET    /api/users/users/{id}/                    - 获取单个用户
# PUT    /api/users/users/{id}/                    - 更新用户
# DELETE /api/users/users/{id}/                    - 删除用户
# POST   /api/users/users/{id}/reset_password/     - 重置密码
# POST   /api/users/users/{id}/toggle_active/      - 切换激活状态

# GET    /api/users/profiles/                      - 获取用户配置列表
# POST   /api/users/profiles/                      - 创建用户配置
# GET    /api/users/profiles/{id}/                 - 获取单个用户配置
# PUT    /api/users/profiles/{id}/                 - 更新用户配置
# DELETE /api/users/profiles/{id}/                 - 删除用户配置
# GET    /api/users/profiles/{id}/statistics/      - 获取用户统计
# GET    /api/users/profiles/my_profile/           - 获取当前用户配置
# PUT    /api/users/profiles/update_my_profile/    - 更新当前用户配置

# GET    /api/users/operation-logs/                - 获取操作日志列表
# GET    /api/users/operation-logs/{id}/           - 获取单个操作日志
# GET    /api/users/operation-logs/my_logs/        - 获取当前用户日志
# GET    /api/users/operation-logs/statistics/     - 获取日志统计
# DELETE /api/users/operation-logs/cleanup_old_logs/ - 清理旧日志
