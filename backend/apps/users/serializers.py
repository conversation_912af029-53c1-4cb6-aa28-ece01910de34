from rest_framework import serializers
from django.contrib.auth.models import User
from .models import UserProfile, OperationLog


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器"""
    profile = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'is_active', 'is_staff', 'date_joined', 'last_login', 'profile'
        ]
        read_only_fields = ['date_joined', 'last_login']
    
    def get_profile(self, obj):
        """获取用户配置信息"""
        try:
            profile = obj.userprofile
            return {
                'role': profile.role,
                'can_view_all_data': profile.can_view_all_data,
                'can_manage_rules': profile.can_manage_rules,
                'can_export_data': profile.can_export_data,
                'total_imports': profile.total_imports,
                'total_records': profile.total_records,
                'last_import_at': profile.last_import_at,
            }
        except UserProfile.DoesNotExist:
            return None


class UserProfileSerializer(serializers.ModelSerializer):
    """用户配置序列化器"""
    user_info = serializers.SerializerMethodField()
    quota_usage = serializers.SerializerMethodField()
    
    class Meta:
        model = UserProfile
        fields = '__all__'
        read_only_fields = [
            'user', 'total_imports', 'total_records', 
            'last_import_at', 'created_at', 'updated_at'
        ]
    
    def get_user_info(self, obj):
        """获取用户基本信息"""
        return {
            'id': obj.user.id,
            'username': obj.user.username,
            'email': obj.user.email,
            'first_name': obj.user.first_name,
            'last_name': obj.user.last_name,
            'is_active': obj.user.is_active,
            'date_joined': obj.user.date_joined,
            'last_login': obj.user.last_login,
        }
    
    def get_quota_usage(self, obj):
        """获取配额使用情况"""
        user = obj.user
        
        # 获取当前使用情况
        from apps.defects.models import ClassificationRule
        from apps.tasks.models import TaskQueue
        
        personal_rules_count = ClassificationRule.objects.filter(
            created_by=user, scope='personal'
        ).count()
        
        running_tasks_count = TaskQueue.objects.filter(
            created_by=user, status__in=['pending', 'running']
        ).count()
        
        return {
            'personal_rules': {
                'used': personal_rules_count,
                'limit': obj.max_personal_rules,
                'percentage': round((personal_rules_count / obj.max_personal_rules) * 100, 2) if obj.max_personal_rules > 0 else 0
            },
            'concurrent_tasks': {
                'used': running_tasks_count,
                'limit': obj.max_concurrent_tasks,
                'percentage': round((running_tasks_count / obj.max_concurrent_tasks) * 100, 2) if obj.max_concurrent_tasks > 0 else 0
            }
        }


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    """用户配置更新序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'role', 'can_view_all_data', 'can_manage_rules', 'can_export_data',
            'max_import_size', 'max_records_per_import', 
            'max_personal_rules', 'max_concurrent_tasks'
        ]
    
    def validate_role(self, value):
        """验证角色"""
        request = self.context.get('request')
        if request and not request.user.is_staff:
            # 非管理员不能修改角色
            if self.instance and self.instance.role != value:
                raise serializers.ValidationError("您没有权限修改角色")
        return value
    
    def validate_can_view_all_data(self, value):
        """验证查看所有数据权限"""
        request = self.context.get('request')
        if request and not request.user.is_staff and value:
            raise serializers.ValidationError("您没有权限设置查看所有数据")
        return value
    
    def validate_can_manage_rules(self, value):
        """验证管理规则权限"""
        request = self.context.get('request')
        if request and not request.user.is_staff and value:
            raise serializers.ValidationError("您没有权限设置管理规则")
        return value


class OperationLogSerializer(serializers.ModelSerializer):
    """操作日志序列化器"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    
    class Meta:
        model = OperationLog
        fields = '__all__'
        read_only_fields = ['created_at']
    
    def to_representation(self, instance):
        """自定义序列化输出"""
        data = super().to_representation(instance)
        
        # 格式化操作详情
        if instance.operation_detail:
            data['operation_summary'] = self.format_operation_summary(instance)
        
        return data
    
    def format_operation_summary(self, instance):
        """格式化操作摘要"""
        operation_type = instance.operation_type
        detail = instance.operation_detail
        
        if operation_type == 'import':
            return f"导入了 {detail.get('record_count', 0)} 条记录"
        elif operation_type == 'export':
            return f"导出了 {detail.get('record_count', 0)} 条记录"
        elif operation_type == 'delete':
            return f"删除了 {detail.get('record_count', 1)} 条记录"
        elif operation_type == 'rule_create':
            return f"创建了规则: {detail.get('rule_name', '')}"
        elif operation_type == 'rule_update':
            return f"更新了规则: {detail.get('rule_name', '')}"
        elif operation_type == 'rule_delete':
            return f"删除了规则: {detail.get('rule_name', '')}"
        else:
            return f"执行了 {instance.get_operation_type_display()} 操作"


class OperationLogListSerializer(serializers.ModelSerializer):
    """操作日志列表序列化器（简化版）"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    operation_type_display = serializers.CharField(source='get_operation_type_display', read_only=True)
    
    class Meta:
        model = OperationLog
        fields = [
            'id', 'user_name', 'operation_type', 'operation_type_display',
            'target_type', 'target_id', 'created_at'
        ]
