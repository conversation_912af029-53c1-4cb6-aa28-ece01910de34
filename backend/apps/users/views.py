from django.shortcuts import render
from django.contrib.auth.models import User
from django.db.models import Q, Count
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from .models import UserProfile, OperationLog
from .serializers import (
    UserProfileSerializer, OperationLogSerializer,
    UserSerializer, UserProfileUpdateSerializer
)
from .filters import OperationLogFilter
from .permissions import IsOwnerOrAdmin, CanViewUserProfiles


class UserViewSet(viewsets.ModelViewSet):
    """用户ViewSet"""
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering_fields = ['date_joined', 'last_login', 'username']
    ordering = ['-date_joined']

    @action(detail=True, methods=['post'])
    def reset_password(self, request, pk=None):
        """重置用户密码"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response(
                {'error': '请提供新密码'},
                status=status.HTTP_400_BAD_REQUEST
            )

        user.set_password(new_password)
        user.save()

        return Response({'message': '密码重置成功'})

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """切换用户激活状态"""
        user = self.get_object()
        user.is_active = not user.is_active
        user.save()

        status_text = '激活' if user.is_active else '禁用'
        return Response({'message': f'用户已{status_text}'})


class UserProfileViewSet(viewsets.ModelViewSet):
    """用户配置ViewSet"""
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__username', 'user__email']
    filterset_fields = ['role', 'can_view_all_data', 'can_manage_rules', 'can_export_data']
    ordering_fields = ['created_at', 'total_imports', 'last_import_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 如果用户不是管理员，只能看到自己的配置
        if not user.is_staff:
            queryset = queryset.filter(user=user)

        return queryset

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action in ['update', 'partial_update']:
            return UserProfileUpdateSerializer
        return UserProfileSerializer

    @action(detail=True, methods=['get'])
    def statistics(self, request, pk=None):
        """获取用户统计信息"""
        profile = self.get_object()
        user = profile.user

        # 导入统计
        from apps.defects.models import ImportBatch
        import_batches = ImportBatch.objects.filter(uploaded_by=user)

        # 任务统计
        from apps.tasks.models import TaskQueue
        tasks = TaskQueue.objects.filter(created_by=user)

        # 规则统计
        from apps.defects.models import ClassificationRule
        rules = ClassificationRule.objects.filter(created_by=user)

        return Response({
            'user_info': {
                'username': user.username,
                'email': user.email,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
            },
            'import_statistics': {
                'total_batches': import_batches.count(),
                'total_records': profile.total_records,
                'last_import': profile.last_import_at,
                'success_batches': import_batches.filter(status='completed').count(),
                'failed_batches': import_batches.filter(status='failed').count(),
            },
            'task_statistics': {
                'total_tasks': tasks.count(),
                'completed_tasks': tasks.filter(status='completed').count(),
                'failed_tasks': tasks.filter(status='failed').count(),
                'running_tasks': tasks.filter(status='running').count(),
            },
            'rule_statistics': {
                'total_rules': rules.count(),
                'active_rules': rules.filter(is_active=True).count(),
                'personal_rules': rules.filter(scope='personal').count(),
                'global_rules': rules.filter(scope='global').count(),
            },
            'quota_usage': {
                'max_import_size': profile.max_import_size,
                'max_records_per_import': profile.max_records_per_import,
                'max_personal_rules': profile.max_personal_rules,
                'max_concurrent_tasks': profile.max_concurrent_tasks,
                'current_personal_rules': rules.filter(scope='personal').count(),
                'current_running_tasks': tasks.filter(status__in=['pending', 'running']).count(),
            }
        })

    @action(detail=False, methods=['get'])
    def my_profile(self, request):
        """获取当前用户的配置"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            # 如果用户配置不存在，创建默认配置
            profile = UserProfile.objects.create(user=request.user)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'])
    def update_my_profile(self, request):
        """更新当前用户的配置"""
        try:
            profile = UserProfile.objects.get(user=request.user)
        except UserProfile.DoesNotExist:
            profile = UserProfile.objects.create(user=request.user)

        serializer = UserProfileUpdateSerializer(
            profile,
            data=request.data,
            partial=request.method == 'PATCH'
        )

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class OperationLogViewSet(viewsets.ReadOnlyModelViewSet):
    """操作日志ViewSet（只读）"""
    queryset = OperationLog.objects.all()
    serializer_class = OperationLogSerializer
    permission_classes = [IsAuthenticated, CanViewUserProfiles]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = OperationLogFilter
    search_fields = ['target_id', 'user__username']
    ordering_fields = ['created_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 如果用户不是管理员，只能看到自己的操作日志
        if not user.is_staff:
            queryset = queryset.filter(user=user)

        return queryset

    @action(detail=False, methods=['get'])
    def my_logs(self, request):
        """获取当前用户的操作日志"""
        queryset = self.queryset.filter(user=request.user)
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取操作日志统计"""
        queryset = self.get_queryset()

        # 按操作类型统计
        operation_stats = queryset.values('operation_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 按目标类型统计
        target_stats = queryset.values('target_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 按用户统计（仅管理员可见）
        user_stats = []
        if request.user.is_staff:
            user_stats = queryset.values('user__username').annotate(
                count=Count('id')
            ).order_by('-count')[:10]

        # 最近7天的活动统计
        from django.utils import timezone
        from datetime import timedelta
        week_ago = timezone.now() - timedelta(days=7)
        recent_logs = queryset.filter(created_at__gte=week_ago)

        return Response({
            'total_logs': queryset.count(),
            'recent_logs': recent_logs.count(),
            'operation_distribution': list(operation_stats),
            'target_distribution': list(target_stats),
            'user_distribution': list(user_stats),
        })

    @action(detail=False, methods=['delete'])
    def cleanup_old_logs(self, request):
        """清理旧的操作日志（仅管理员）"""
        if not request.user.is_staff:
            return Response(
                {'error': '权限不足'},
                status=status.HTTP_403_FORBIDDEN
            )

        # 删除30天前的日志
        from django.utils import timezone
        from datetime import timedelta
        month_ago = timezone.now() - timedelta(days=30)

        old_logs = self.queryset.filter(created_at__lt=month_ago)
        deleted_count = old_logs.count()
        old_logs.delete()

        return Response({
            'message': f'已清理 {deleted_count} 条旧日志',
            'deleted_count': deleted_count
        })
