from rest_framework import permissions


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    自定义权限：只有数据所有者或管理员可以访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有数据
        if request.user.is_staff:
            return True
        
        # 检查是否是数据所有者
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanViewUserProfiles(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以查看用户配置
    """
    
    def has_permission(self, request, view):
        # 管理员可以查看所有用户配置
        if request.user.is_staff:
            return True
        
        # 普通用户只能查看自己的配置
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有配置
        if request.user.is_staff:
            return True
        
        # 用户只能访问自己的配置
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanManageUsers(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以管理其他用户
    """
    
    def has_permission(self, request, view):
        # 只有管理员可以管理用户
        return request.user.is_staff
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以管理所有用户，但不能删除自己
        if request.method == 'DELETE' and obj == request.user:
            return False
        
        return request.user.is_staff


class CanViewOperationLogs(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以查看操作日志
    """
    
    def has_permission(self, request, view):
        # 所有认证用户都可以查看操作日志（但会根据权限过滤）
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以查看所有日志
        if request.user.is_staff:
            return True
        
        # 用户只能查看自己的操作日志
        return obj.user == request.user
