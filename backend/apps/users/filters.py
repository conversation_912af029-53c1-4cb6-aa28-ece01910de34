import django_filters
from django.db.models import Q
from django.contrib.auth.models import User
from .models import UserProfile, OperationLog


class UserFilter(django_filters.FilterSet):
    """用户过滤器"""
    
    username = django_filters.CharFilter(lookup_expr='icontains')
    email = django_filters.CharFilter(lookup_expr='icontains')
    first_name = django_filters.CharFilter(lookup_expr='icontains')
    last_name = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    is_staff = django_filters.BooleanFilter()
    
    # 时间范围过滤
    joined_after = django_filters.DateTimeFilter(field_name='date_joined', lookup_expr='gte')
    joined_before = django_filters.DateTimeFilter(field_name='date_joined', lookup_expr='lte')
    last_login_after = django_filters.DateTimeFilter(field_name='last_login', lookup_expr='gte')
    last_login_before = django_filters.DateTimeFilter(field_name='last_login', lookup_expr='lte')
    
    # 复合搜索
    search = django_filters.CharFilter(method='filter_search')
    
    class Meta:
        model = User
        fields = ['username', 'email', 'is_active', 'is_staff']
    
    def filter_search(self, queryset, name, value):
        """全文搜索"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(username__icontains=value) |
            Q(email__icontains=value) |
            Q(first_name__icontains=value) |
            Q(last_name__icontains=value)
        )


class UserProfileFilter(django_filters.FilterSet):
    """用户配置过滤器"""
    
    role = django_filters.MultipleChoiceFilter(
        choices=UserProfile.ROLE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    can_view_all_data = django_filters.BooleanFilter()
    can_manage_rules = django_filters.BooleanFilter()
    can_export_data = django_filters.BooleanFilter()
    
    # 统计范围过滤
    min_imports = django_filters.NumberFilter(field_name='total_imports', lookup_expr='gte')
    max_imports = django_filters.NumberFilter(field_name='total_imports', lookup_expr='lte')
    min_records = django_filters.NumberFilter(field_name='total_records', lookup_expr='gte')
    max_records = django_filters.NumberFilter(field_name='total_records', lookup_expr='lte')
    
    # 时间范围过滤
    last_import_after = django_filters.DateTimeFilter(field_name='last_import_at', lookup_expr='gte')
    last_import_before = django_filters.DateTimeFilter(field_name='last_import_at', lookup_expr='lte')
    
    class Meta:
        model = UserProfile
        fields = ['role', 'can_view_all_data', 'can_manage_rules', 'can_export_data']


class OperationLogFilter(django_filters.FilterSet):
    """操作日志过滤器"""
    
    operation_type = django_filters.MultipleChoiceFilter(
        choices=OperationLog.OPERATION_TYPE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    target_type = django_filters.CharFilter(lookup_expr='icontains')
    target_id = django_filters.CharFilter(lookup_expr='icontains')
    user = django_filters.ModelChoiceFilter(queryset=User.objects.all())
    
    # 时间范围过滤
    created_after = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    
    # IP地址过滤
    ip_address = django_filters.CharFilter(lookup_expr='icontains')
    
    # 复合搜索
    search = django_filters.CharFilter(method='filter_search')
    
    class Meta:
        model = OperationLog
        fields = ['operation_type', 'target_type', 'user']
    
    def filter_search(self, queryset, name, value):
        """全文搜索"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(target_id__icontains=value) |
            Q(user__username__icontains=value) |
            Q(ip_address__icontains=value) |
            Q(user_agent__icontains=value)
        )
