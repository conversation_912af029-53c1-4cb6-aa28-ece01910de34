from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from apps.defects.models import DefectRecord, ImportBatch, ClassificationRule
from apps.tasks.models import TaskQueue
from apps.users.models import UserProfile, OperationLog
from apps.notifications.models import Notification


def home_view(request):
    """首页视图"""
    return render(request, 'dashboard/index.html')


@login_required
def dashboard_view(request):
    """仪表板页面视图"""
    user = request.user
    
    # 获取用户权限
    can_view_all = user.is_staff or (
        hasattr(user, 'userprofile') and user.userprofile.can_view_all_data
    )
    
    # 基础数据查询
    if can_view_all:
        defects = DefectRecord.objects.all()
        batches = ImportBatch.objects.all()
        tasks = TaskQueue.objects.all()
    else:
        defects = DefectRecord.objects.filter(uploaded_by=user)
        batches = ImportBatch.objects.filter(uploaded_by=user)
        tasks = TaskQueue.objects.filter(created_by=user)
    
    # 计算统计数据
    context = {
        'user': user,
        'can_view_all': can_view_all,
        'stats': get_dashboard_stats(defects, batches, tasks, user),
    }
    
    return render(request, 'dashboard/dashboard.html', context)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_api(request):
    """仪表板API接口"""
    user = request.user
    
    # 获取用户权限
    can_view_all = user.is_staff or (
        hasattr(user, 'userprofile') and user.userprofile.can_view_all_data
    )
    
    # 基础数据查询
    if can_view_all:
        defects = DefectRecord.objects.all()
        batches = ImportBatch.objects.all()
        tasks = TaskQueue.objects.all()
        users = UserProfile.objects.all()
    else:
        defects = DefectRecord.objects.filter(uploaded_by=user)
        batches = ImportBatch.objects.filter(uploaded_by=user)
        tasks = TaskQueue.objects.filter(created_by=user)
        users = UserProfile.objects.filter(user=user)
    
    # 获取统计数据
    stats = get_dashboard_stats(defects, batches, tasks, user)
    
    # 添加用户统计
    if can_view_all:
        stats['user_stats'] = {
            'total_users': users.count(),
            'active_users': users.filter(user__is_active=True).count(),
            'admin_users': users.filter(role='admin').count(),
            'recent_logins': users.filter(
                user__last_login__gte=timezone.now() - timedelta(days=7)
            ).count(),
        }
    
    return Response(stats)


def get_dashboard_stats(defects, batches, tasks, user):
    """获取仪表板统计数据"""
    now = timezone.now()
    week_ago = now - timedelta(days=7)
    month_ago = now - timedelta(days=30)
    
    # 缺陷统计
    defect_stats = {
        'total_defects': defects.count(),
        'open_defects': defects.filter(status='Open').count(),
        'resolved_defects': defects.filter(status='Resolved').count(),
        'high_priority': defects.filter(priority='High').count(),
        'recent_defects': defects.filter(import_timestamp__gte=week_ago).count(),
        'classified_defects': defects.exclude(
            Q(bug_category__isnull=True) | Q(bug_category__exact='')
        ).count(),
    }
    
    # 按状态分布
    status_distribution = list(
        defects.values('status').annotate(count=Count('id')).order_by('-count')
    )
    
    # 按优先级分布
    priority_distribution = list(
        defects.values('priority').annotate(count=Count('id')).order_by('-count')
    )
    
    # 按项目分布（前10）
    project_distribution = list(
        defects.values('project_key').annotate(count=Count('id')).order_by('-count')[:10]
    )
    
    # 按设备型号分布（前10）
    device_distribution = list(
        defects.exclude(
            Q(device_model__isnull=True) | Q(device_model__exact='')
        ).values('device_model').annotate(count=Count('id')).order_by('-count')[:10]
    )
    
    # 导入批次统计
    batch_stats = {
        'total_batches': batches.count(),
        'completed_batches': batches.filter(status='completed').count(),
        'failed_batches': batches.filter(status='failed').count(),
        'processing_batches': batches.filter(status='processing').count(),
        'recent_batches': batches.filter(import_timestamp__gte=week_ago).count(),
    }
    
    # 任务统计
    task_stats = {
        'total_tasks': tasks.count(),
        'pending_tasks': tasks.filter(status='pending').count(),
        'running_tasks': tasks.filter(status='running').count(),
        'completed_tasks': tasks.filter(status='completed').count(),
        'failed_tasks': tasks.filter(status='failed').count(),
        'recent_tasks': tasks.filter(created_at__gte=week_ago).count(),
    }
    
    # 通知统计
    notifications = Notification.objects.filter(user=user)
    notification_stats = {
        'total_notifications': notifications.count(),
        'unread_notifications': notifications.filter(is_read=False).count(),
        'recent_notifications': notifications.filter(created_at__gte=week_ago).count(),
    }
    
    # 最近7天的趋势数据
    daily_trends = []
    for i in range(7):
        date = now.date() - timedelta(days=i)
        daily_defects = defects.filter(import_timestamp__date=date).count()
        daily_trends.append({
            'date': date.strftime('%Y-%m-%d'),
            'defects': daily_defects,
        })
    daily_trends.reverse()
    
    # 分类规则统计
    rules = ClassificationRule.objects.filter(created_by=user)
    rule_stats = {
        'total_rules': rules.count(),
        'active_rules': rules.filter(is_active=True).count(),
        'personal_rules': rules.filter(scope='personal').count(),
        'global_rules': rules.filter(scope='global').count(),
    }
    
    return {
        'defect_stats': defect_stats,
        'batch_stats': batch_stats,
        'task_stats': task_stats,
        'notification_stats': notification_stats,
        'rule_stats': rule_stats,
        'distributions': {
            'status': status_distribution,
            'priority': priority_distribution,
            'project': project_distribution,
            'device': device_distribution,
        },
        'trends': {
            'daily': daily_trends,
        },
        'summary': {
            'total_records': defects.count(),
            'success_rate': round(
                (batch_stats['completed_batches'] / batch_stats['total_batches'] * 100) 
                if batch_stats['total_batches'] > 0 else 0, 2
            ),
            'classification_rate': round(
                (defect_stats['classified_defects'] / defect_stats['total_defects'] * 100) 
                if defect_stats['total_defects'] > 0 else 0, 2
            ),
        }
    }
