from django.shortcuts import render
from django.db.models import Q, Count
from django.http import HttpResponse
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from .models import DefectRecord, FieldMapping, ImportBatch, ClassificationRule
from .serializers import (
    DefectRecordSerializer, FieldMappingSerializer,
    ImportBatchSerializer, ClassificationRuleSerializer,
    DefectRecordListSerializer, ImportBatchListSerializer,
    ClassificationRuleListSerializer
)
from .filters import DefectRecordFilter, ImportBatchFilter, ClassificationRuleFilter
from .permissions import IsOwnerOrAdmin, CanManageRules, CanExportData, CanImportData
import pandas as pd
import io


class DefectRecordViewSet(viewsets.ModelViewSet):
    """缺陷记录ViewSet"""
    queryset = DefectRecord.objects.all()
    serializer_class = DefectRecordSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = DefectRecordFilter
    search_fields = ['issue_key', 'summary', 'project_key', 'assignee']
    ordering_fields = ['created', 'updated', 'import_timestamp', 'priority']
    ordering = ['-import_timestamp']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 如果用户不是管理员，只能看到自己上传的数据
        if not (user.is_staff or hasattr(user, 'userprofile') and user.userprofile.can_view_all_data):
            queryset = queryset.filter(uploaded_by=user)

        return queryset

    def get_serializer_class(self):
        """根据action选择序列化器"""
        if self.action == 'list':
            return DefectRecordListSerializer
        return DefectRecordSerializer

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated, CanImportData])
    def import_data(self, request):
        """数据导入接口"""
        # 这里实现数据导入逻辑
        # 实际实现会在后续的service层中完成
        return Response({
            'message': '数据导入功能待实现',
            'status': 'pending'
        })

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated, CanExportData])
    def export_data(self, request):
        """数据导出接口"""
        queryset = self.filter_queryset(self.get_queryset())

        # 创建DataFrame
        data = []
        for record in queryset:
            data.append({
                'Issue key': record.issue_key,
                'Summary': record.summary,
                'Status': record.status,
                'Priority': record.priority,
                'Assignee': record.assignee,
                'Created': record.created,
                'Project key': record.project_key,
                'Bug category': record.bug_category,
                'Device model': record.device_model,
                'Commonality': record.commonality,
            })

        df = pd.DataFrame(data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Defects')

        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="defects_export.xlsx"'
        return response

    @action(detail=False, methods=['post'])
    def batch_classify(self, request):
        """批量分类接口"""
        defect_ids = request.data.get('defect_ids', [])

        if not defect_ids:
            return Response(
                {'error': '请选择要分类的缺陷记录'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取用户有权限的记录
        queryset = self.get_queryset().filter(id__in=defect_ids)

        # 这里实现批量分类逻辑
        # 实际实现会在后续的service层中完成

        return Response({
            'message': f'已对 {queryset.count()} 条记录进行分类',
            'classified_count': queryset.count()
        })

    @action(detail=False, methods=['delete'])
    def batch_delete(self, request):
        """批量删除接口"""
        defect_ids = request.data.get('defect_ids', [])

        if not defect_ids:
            return Response(
                {'error': '请选择要删除的缺陷记录'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取用户有权限的记录
        queryset = self.get_queryset().filter(id__in=defect_ids)
        deleted_count = queryset.count()
        queryset.delete()

        return Response({
            'message': f'已删除 {deleted_count} 条记录',
            'deleted_count': deleted_count
        })

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """统计信息接口"""
        queryset = self.get_queryset()

        # 基础统计
        total_count = queryset.count()

        # 按状态统计
        status_stats = queryset.values('status').annotate(
            count=Count('id')
        ).order_by('-count')

        # 按优先级统计
        priority_stats = queryset.values('priority').annotate(
            count=Count('id')
        ).order_by('-count')

        # 按项目统计
        project_stats = queryset.values('project_key').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 取前10个项目

        # 按设备型号统计
        device_stats = queryset.exclude(
            device_model__isnull=True
        ).exclude(
            device_model__exact=''
        ).values('device_model').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 取前10个设备型号

        return Response({
            'total_count': total_count,
            'status_distribution': list(status_stats),
            'priority_distribution': list(priority_stats),
            'project_distribution': list(project_stats),
            'device_distribution': list(device_stats),
        })


class FieldMappingViewSet(viewsets.ModelViewSet):
    """字段映射ViewSet"""
    queryset = FieldMapping.objects.all()
    serializer_class = FieldMappingSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    search_fields = ['csv_field_name', 'db_field_name']
    filterset_fields = ['field_type', 'is_required', 'is_core_field']
    ordering = ['-is_core_field', '-is_required', 'csv_field_name']

    @action(detail=False, methods=['get'])
    def core_fields(self, request):
        """获取核心字段映射"""
        core_mappings = self.queryset.filter(is_core_field=True)
        serializer = self.get_serializer(core_mappings, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def validate_csv_headers(self, request):
        """验证CSV文件头"""
        headers = request.data.get('headers', [])

        if not headers:
            return Response(
                {'error': '请提供CSV文件头'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取所有字段映射
        mappings = self.queryset.all()

        # 分析字段
        supported_fields = []
        unknown_fields = []
        missing_required_fields = []

        mapping_dict = {m.csv_field_name: m for m in mappings}

        for header in headers:
            if header in mapping_dict:
                supported_fields.append(header)
            else:
                unknown_fields.append(header)

        # 检查必需字段
        required_mappings = mappings.filter(is_required=True)
        for mapping in required_mappings:
            if mapping.csv_field_name not in headers:
                missing_required_fields.append(mapping.csv_field_name)

        is_valid = len(missing_required_fields) == 0

        return Response({
            'valid': is_valid,
            'supported_fields': supported_fields,
            'unknown_fields': unknown_fields,
            'missing_required_fields': missing_required_fields,
            'total_fields': len(headers),
            'total_supported': len(supported_fields),
        })


class ImportBatchViewSet(viewsets.ModelViewSet):
    """导入批次ViewSet"""
    queryset = ImportBatch.objects.all()
    serializer_class = ImportBatchSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['batch_name', 'batch_id']
    filterset_fields = ['status', 'data_source']
    ordering_fields = ['import_timestamp', 'processing_started_at', 'processing_completed_at']
    ordering = ['-import_timestamp']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 如果用户不是管理员，只能看到自己的批次
        if not (user.is_staff or hasattr(user, 'userprofile') and user.userprofile.can_view_all_data):
            queryset = queryset.filter(uploaded_by=user)

        return queryset

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消导入任务"""
        batch = self.get_object()

        if batch.status not in ['uploaded', 'queued', 'processing']:
            return Response(
                {'error': '只能取消未完成的任务'},
                status=status.HTTP_400_BAD_REQUEST
            )

        batch.status = 'cancelled'
        batch.save()

        # 这里可以添加取消Celery任务的逻辑

        return Response({'message': '任务已取消'})

    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """获取导入进度"""
        batch = self.get_object()

        return Response({
            'batch_id': batch.batch_id,
            'status': batch.status,
            'progress_percentage': batch.progress_percentage,
            'current_step': batch.current_step,
            'total_records': batch.total_records,
            'processed_records': batch.processed_records,
            'success_records': batch.success_records,
            'failed_records': batch.failed_records,
        })


class ClassificationRuleViewSet(viewsets.ModelViewSet):
    """分类规则ViewSet"""
    queryset = ClassificationRule.objects.all()
    serializer_class = ClassificationRuleSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['rule_name', 'pattern', 'target_value']
    filterset_fields = ['rule_type', 'scope', 'is_active']
    ordering_fields = ['priority', 'created_at']
    ordering = ['scope', '-priority', 'rule_type']

    def get_queryset(self):
        """根据用户权限过滤数据"""
        queryset = super().get_queryset()
        user = self.request.user

        # 用户可以看到全局规则和自己的个人规则
        queryset = queryset.filter(
            Q(scope='global') | Q(created_by=user)
        )

        return queryset

    def perform_create(self, serializer):
        """创建规则时设置创建者"""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def test_rule(self, request, pk=None):
        """测试规则"""
        rule = self.get_object()
        test_text = request.data.get('test_text', '')

        if not test_text:
            return Response(
                {'error': '请提供测试文本'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 这里实现规则测试逻辑
        # 实际实现会在后续的service层中完成

        return Response({
            'rule_name': rule.rule_name,
            'test_text': test_text,
            'matched': False,  # 实际匹配结果
            'result': '测试功能待实现'
        })

    @action(detail=False, methods=['post'])
    def import_rules(self, request):
        """导入规则"""
        # 这里实现规则导入逻辑
        # 实际实现会在后续的service层中完成

        return Response({
            'message': '规则导入功能待实现',
            'status': 'pending'
        })

    @action(detail=False, methods=['get'])
    def export_rules(self, request):
        """导出规则"""
        queryset = self.filter_queryset(self.get_queryset())

        # 创建DataFrame
        data = []
        for rule in queryset:
            data.append({
                'Rule Name': rule.rule_name,
                'Rule Type': rule.rule_type,
                'Pattern': rule.pattern,
                'Target Value': rule.target_value,
                'Priority': rule.priority,
                'Scope': rule.scope,
                'Is Active': rule.is_active,
                'Description': rule.description,
            })

        df = pd.DataFrame(data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Rules')

        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="classification_rules.xlsx"'
        return response
