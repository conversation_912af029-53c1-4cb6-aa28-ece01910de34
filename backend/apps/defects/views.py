from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import DefectRecord, FieldMapping, ImportBatch, ClassificationRule

# 这里可以添加ViewSet和其他视图
# 示例：
# class DefectRecordViewSet(viewsets.ModelViewSet):
#     queryset = DefectRecord.objects.all()
#     serializer_class = DefectRecordSerializer
