from django.contrib import admin
from .models import DefectRecord, FieldMapping, ImportBatch, ClassificationRule


@admin.register(DefectRecord)
class DefectRecordAdmin(admin.ModelAdmin):
    list_display = ['issue_key', 'summary', 'status', 'priority', 'uploaded_by', 'import_timestamp']
    list_filter = ['status', 'priority', 'uploaded_by', 'import_batch_id']
    search_fields = ['issue_key', 'summary', 'project_key']
    readonly_fields = ['import_batch_id', 'import_timestamp', 'uploaded_by', 'created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('issue_key', 'issue_id', 'summary', 'issue_type')
        }),
        ('状态信息', {
            'fields': ('status', 'priority', 'resolution')
        }),
        ('项目信息', {
            'fields': ('project_key', 'project_name', 'project_type', 'project_lead')
        }),
        ('人员信息', {
            'fields': ('assignee', 'reporter', 'creator', 'rd_owner')
        }),
        ('时间信息', {
            'fields': ('created', 'updated', 'resolved', 'due_date')
        }),
        ('分类信息', {
            'fields': ('bug_category', 'commonality', 'device_model')
        }),
        ('系统信息', {
            'fields': ('import_batch_id', 'import_timestamp', 'uploaded_by', 'created_at', 'updated_at')
        }),
    )


@admin.register(FieldMapping)
class FieldMappingAdmin(admin.ModelAdmin):
    list_display = ['csv_field_name', 'db_field_name', 'field_type', 'is_required', 'is_core_field']
    list_filter = ['field_type', 'is_required', 'is_core_field']
    search_fields = ['csv_field_name', 'db_field_name']


@admin.register(ImportBatch)
class ImportBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_name', 'batch_id', 'status', 'total_records', 'uploaded_by', 'import_timestamp']
    list_filter = ['status', 'uploaded_by', 'data_source']
    search_fields = ['batch_name', 'batch_id']
    readonly_fields = ['batch_id', 'import_timestamp', 'processing_started_at', 'processing_completed_at']


@admin.register(ClassificationRule)
class ClassificationRuleAdmin(admin.ModelAdmin):
    list_display = ['rule_name', 'rule_type', 'scope', 'priority', 'is_active', 'created_by']
    list_filter = ['rule_type', 'scope', 'is_active', 'created_by']
    search_fields = ['rule_name', 'pattern', 'target_value']
