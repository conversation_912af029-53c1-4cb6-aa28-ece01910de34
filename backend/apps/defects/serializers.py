from rest_framework import serializers
from django.contrib.auth.models import User
from .models import DefectRecord, FieldMapping, ImportBatch, ClassificationRule


class DefectRecordSerializer(serializers.ModelSerializer):
    """缺陷记录序列化器"""
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)
    created_date = serializers.SerializerMethodField()

    class Meta:
        model = DefectRecord
        fields = '__all__'
        read_only_fields = ['import_batch_id', 'import_timestamp', 'uploaded_by', 'created_at', 'updated_at']

    def get_created_date(self, obj):
        """获取创建日期"""
        return obj.created_date


class DefectRecordListSerializer(serializers.ModelSerializer):
    """缺陷记录列表序列化器（简化版）"""
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)

    class Meta:
        model = DefectRecord
        fields = [
            'id', 'issue_key', 'summary', 'status', 'priority',
            'project_key', 'assignee', 'created', 'device_model',
            'bug_category', 'commonality', 'uploaded_by_name', 'import_timestamp'
        ]


class FieldMappingSerializer(serializers.ModelSerializer):
    """字段映射序列化器"""

    class Meta:
        model = FieldMapping
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

    def validate_csv_field_name(self, value):
        """验证CSV字段名"""
        if not value.strip():
            raise serializers.ValidationError("CSV字段名不能为空")
        return value.strip()

    def validate_db_field_name(self, value):
        """验证数据库字段名"""
        if not value.strip():
            raise serializers.ValidationError("数据库字段名不能为空")

        # 检查字段名是否在模型中存在
        valid_fields = [f.name for f in DefectRecord._meta.get_fields()]
        if value not in valid_fields:
            raise serializers.ValidationError(f"数据库字段名 '{value}' 不存在")

        return value.strip()


class ImportBatchSerializer(serializers.ModelSerializer):
    """导入批次序列化器"""
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)
    duration = serializers.SerializerMethodField()
    success_rate = serializers.SerializerMethodField()

    class Meta:
        model = ImportBatch
        fields = '__all__'
        read_only_fields = [
            'batch_id', 'import_timestamp', 'uploaded_by',
            'processing_started_at', 'processing_completed_at'
        ]

    def get_duration(self, obj):
        """获取处理时长（秒）"""
        if obj.processing_started_at and obj.processing_completed_at:
            delta = obj.processing_completed_at - obj.processing_started_at
            return int(delta.total_seconds())
        return None

    def get_success_rate(self, obj):
        """获取成功率"""
        if obj.total_records > 0:
            return round((obj.success_records / obj.total_records) * 100, 2)
        return 0


class ImportBatchListSerializer(serializers.ModelSerializer):
    """导入批次列表序列化器（简化版）"""
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)
    success_rate = serializers.SerializerMethodField()

    class Meta:
        model = ImportBatch
        fields = [
            'id', 'batch_name', 'batch_id', 'status', 'total_records',
            'success_records', 'failed_records', 'progress_percentage',
            'uploaded_by_name', 'import_timestamp'
        ]

    def get_success_rate(self, obj):
        """获取成功率"""
        if obj.total_records > 0:
            return round((obj.success_records / obj.total_records) * 100, 2)
        return 0


class ClassificationRuleSerializer(serializers.ModelSerializer):
    """分类规则序列化器"""
    can_edit = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = ClassificationRule
        fields = '__all__'
        read_only_fields = ['created_by', 'created_at', 'updated_at']

    def get_can_edit(self, obj):
        """检查是否可以编辑"""
        request = self.context.get('request')
        if request and request.user:
            return obj.can_edit(request.user)
        return False

    def validate_pattern(self, value):
        """验证匹配模式"""
        if not value.strip():
            raise serializers.ValidationError("匹配模式不能为空")

        # 这里可以添加正则表达式验证
        import re
        try:
            re.compile(value)
        except re.error as e:
            raise serializers.ValidationError(f"无效的正则表达式: {e}")

        return value.strip()

    def validate(self, data):
        """整体验证"""
        # 检查个人规则数量限制
        if data.get('scope') == 'personal':
            request = self.context.get('request')
            if request and request.user:
                user_rules_count = ClassificationRule.objects.filter(
                    created_by=request.user,
                    scope='personal'
                ).count()

                # 假设用户最多可以创建100个个人规则
                max_personal_rules = 100
                if hasattr(request.user, 'userprofile'):
                    max_personal_rules = request.user.userprofile.max_personal_rules

                if user_rules_count >= max_personal_rules:
                    raise serializers.ValidationError(
                        f"个人规则数量已达到上限 ({max_personal_rules})"
                    )

        return data


class ClassificationRuleListSerializer(serializers.ModelSerializer):
    """分类规则列表序列化器（简化版）"""
    can_edit = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = ClassificationRule
        fields = [
            'id', 'rule_name', 'rule_type', 'scope', 'priority',
            'is_active', 'created_by_name', 'created_at'
        ]

    def get_can_edit(self, obj):
        """检查是否可以编辑"""
        request = self.context.get('request')
        if request and request.user:
            return obj.can_edit(request.user)
        return False
