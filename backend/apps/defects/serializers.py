from rest_framework import serializers
from .models import DefectRecord, FieldMapping, ImportBatch, ClassificationRule


class DefectRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = DefectRecord
        fields = '__all__'


class FieldMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = FieldMapping
        fields = '__all__'


class ImportBatchSerializer(serializers.ModelSerializer):
    class Meta:
        model = ImportBatch
        fields = '__all__'


class ClassificationRuleSerializer(serializers.ModelSerializer):
    can_edit = serializers.SerializerMethodField()
    
    class Meta:
        model = ClassificationRule
        fields = '__all__'
    
    def get_can_edit(self, obj):
        request = self.context.get('request')
        if request and request.user:
            return obj.can_edit(request.user)
        return False
