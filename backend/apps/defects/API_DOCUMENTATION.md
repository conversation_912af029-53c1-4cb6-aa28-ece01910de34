# Defects API 文档

## 概述

缺陷管理API提供了完整的缺陷记录管理、字段映射配置、导入批次管理和分类规则管理功能。

## 基础URL

```
http://localhost:8000/api/defects/
```

## 认证

所有API端点都需要用户认证。支持以下认证方式：
- Session认证
- Token认证

## API端点

### 1. 缺陷记录 (DefectRecord)

#### 基础端点
- `GET /api/defects/records/` - 获取缺陷记录列表
- `POST /api/defects/records/` - 创建缺陷记录
- `GET /api/defects/records/{id}/` - 获取单个缺陷记录
- `PUT /api/defects/records/{id}/` - 更新缺陷记录
- `PATCH /api/defects/records/{id}/` - 部分更新缺陷记录
- `DELETE /api/defects/records/{id}/` - 删除缺陷记录

#### 自定义动作
- `POST /api/defects/records/import_data/` - 导入数据
- `GET /api/defects/records/export_data/` - 导出数据
- `POST /api/defects/records/batch_classify/` - 批量分类
- `DELETE /api/defects/records/batch_delete/` - 批量删除
- `GET /api/defects/records/statistics/` - 获取统计信息

#### 查询参数

**过滤参数:**
- `issue_key` - 按Issue Key过滤（包含匹配）
- `summary` - 按摘要过滤（包含匹配）
- `status` - 按状态过滤（多选）
- `priority` - 按优先级过滤（多选）
- `project_key` - 按项目Key过滤（包含匹配）
- `assignee` - 按指派人过滤（包含匹配）
- `device_model` - 按设备型号过滤（包含匹配）
- `bug_category` - 按缺陷分类过滤（包含匹配）
- `import_batch_id` - 按导入批次ID过滤

**时间范围过滤:**
- `created_after` - 创建时间起始（YYYY-MM-DD HH:MM:SS）
- `created_before` - 创建时间结束（YYYY-MM-DD HH:MM:SS）
- `import_date_after` - 导入时间起始
- `import_date_before` - 导入时间结束

**布尔过滤:**
- `has_device_model` - 是否有设备型号（true/false）
- `has_classification` - 是否已分类（true/false）
- `has_commonality` - 是否有共性问题（true/false）

**搜索和排序:**
- `search` - 全文搜索
- `ordering` - 排序字段（可用字段：created, updated, import_timestamp, priority）

#### 示例请求

```bash
# 获取缺陷记录列表
curl -H "Authorization: Token your-token" \
     "http://localhost:8000/api/defects/records/?status=Open,In Progress&ordering=-created"

# 批量分类
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"defect_ids": [1, 2, 3]}' \
     "http://localhost:8000/api/defects/records/batch_classify/"

# 导出数据
curl -H "Authorization: Token your-token" \
     "http://localhost:8000/api/defects/records/export_data/" \
     -o defects_export.xlsx
```

### 2. 字段映射 (FieldMapping)

#### 基础端点
- `GET /api/defects/field-mappings/` - 获取字段映射列表
- `POST /api/defects/field-mappings/` - 创建字段映射
- `GET /api/defects/field-mappings/{id}/` - 获取单个字段映射
- `PUT /api/defects/field-mappings/{id}/` - 更新字段映射
- `DELETE /api/defects/field-mappings/{id}/` - 删除字段映射

#### 自定义动作
- `GET /api/defects/field-mappings/core_fields/` - 获取核心字段映射
- `POST /api/defects/field-mappings/validate_csv_headers/` - 验证CSV文件头

#### 示例请求

```bash
# 验证CSV文件头
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"headers": ["Issue key", "Summary", "Status", "Priority"]}' \
     "http://localhost:8000/api/defects/field-mappings/validate_csv_headers/"
```

### 3. 导入批次 (ImportBatch)

#### 基础端点
- `GET /api/defects/import-batches/` - 获取导入批次列表
- `POST /api/defects/import-batches/` - 创建导入批次
- `GET /api/defects/import-batches/{id}/` - 获取单个导入批次
- `PUT /api/defects/import-batches/{id}/` - 更新导入批次
- `DELETE /api/defects/import-batches/{id}/` - 删除导入批次

#### 自定义动作
- `POST /api/defects/import-batches/{id}/cancel/` - 取消导入任务
- `GET /api/defects/import-batches/{id}/progress/` - 获取导入进度

#### 示例请求

```bash
# 获取导入进度
curl -H "Authorization: Token your-token" \
     "http://localhost:8000/api/defects/import-batches/123/progress/"

# 取消导入任务
curl -X POST \
     -H "Authorization: Token your-token" \
     "http://localhost:8000/api/defects/import-batches/123/cancel/"
```

### 4. 分类规则 (ClassificationRule)

#### 基础端点
- `GET /api/defects/classification-rules/` - 获取分类规则列表
- `POST /api/defects/classification-rules/` - 创建分类规则
- `GET /api/defects/classification-rules/{id}/` - 获取单个分类规则
- `PUT /api/defects/classification-rules/{id}/` - 更新分类规则
- `DELETE /api/defects/classification-rules/{id}/` - 删除分类规则

#### 自定义动作
- `POST /api/defects/classification-rules/{id}/test_rule/` - 测试规则
- `POST /api/defects/classification-rules/import_rules/` - 导入规则
- `GET /api/defects/classification-rules/export_rules/` - 导出规则

#### 示例请求

```bash
# 测试规则
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"test_text": "UI界面显示异常"}' \
     "http://localhost:8000/api/defects/classification-rules/1/test_rule/"

# 导出规则
curl -H "Authorization: Token your-token" \
     "http://localhost:8000/api/defects/classification-rules/export_rules/" \
     -o classification_rules.xlsx
```

## 权限说明

### 数据访问权限
- **管理员**: 可以访问所有数据
- **普通用户**: 只能访问自己上传的数据
- **特殊权限用户**: 可以通过UserProfile配置访问权限

### 功能权限
- **数据导入**: 所有认证用户（有配额限制）
- **数据导出**: 需要导出权限
- **规则管理**: 个人规则（所有用户），全局规则（需要管理权限）

## 错误处理

API使用标准HTTP状态码：

- `200 OK` - 请求成功
- `201 Created` - 创建成功
- `400 Bad Request` - 请求参数错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `500 Internal Server Error` - 服务器错误

错误响应格式：
```json
{
    "error": "错误描述",
    "details": "详细错误信息"
}
```

## 分页

列表API支持分页，默认每页20条记录：

```json
{
    "count": 100,
    "next": "http://localhost:8000/api/defects/records/?page=2",
    "previous": null,
    "results": [...]
}
```

查询参数：
- `page` - 页码
- `page_size` - 每页记录数（最大100）

## 示例响应

### 缺陷记录列表响应
```json
{
    "count": 2,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "issue_key": "BUG-001",
            "summary": "登录页面显示异常",
            "status": "Open",
            "priority": "High",
            "project_key": "PROJ-1",
            "assignee": "张三",
            "created": "2025-01-01T10:00:00Z",
            "device_model": "iPhone 14",
            "bug_category": "UI问题",
            "commonality": "登录相关",
            "uploaded_by_name": "admin",
            "import_timestamp": "2025-01-01T09:00:00Z"
        }
    ]
}
```

### 统计信息响应
```json
{
    "total_count": 150,
    "status_distribution": [
        {"status": "Open", "count": 50},
        {"status": "In Progress", "count": 30},
        {"status": "Resolved", "count": 70}
    ],
    "priority_distribution": [
        {"priority": "High", "count": 20},
        {"priority": "Medium", "count": 80},
        {"priority": "Low", "count": 50}
    ],
    "project_distribution": [
        {"project_key": "PROJ-1", "count": 60},
        {"project_key": "PROJ-2", "count": 40}
    ],
    "device_distribution": [
        {"device_model": "iPhone 14", "count": 25},
        {"device_model": "Samsung S23", "count": 20}
    ]
}
```
