from rest_framework import permissions


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    自定义权限：只有数据所有者或管理员可以访问
    """
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有数据
        if request.user.is_staff:
            return True
        
        # 检查用户是否有查看所有数据的权限
        if hasattr(request.user, 'userprofile') and request.user.userprofile.can_view_all_data:
            return True
        
        # 检查是否是数据所有者
        if hasattr(obj, 'uploaded_by'):
            return obj.uploaded_by == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        return False


class CanManageRules(permissions.BasePermission):
    """
    自定义权限：只有有权限的用户可以管理全局规则
    """
    
    def has_permission(self, request, view):
        # 对于全局规则的创建和管理
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            # 检查是否要操作全局规则
            scope = request.data.get('scope', 'personal')
            if scope == 'global':
                return (
                    request.user.is_staff or 
                    (hasattr(request.user, 'userprofile') and request.user.userprofile.can_manage_rules)
                )
        
        return True
    
    def has_object_permission(self, request, view, obj):
        # 对于全局规则的修改和删除
        if obj.scope == 'global':
            return (
                request.user.is_staff or 
                (hasattr(request.user, 'userprofile') and request.user.userprofile.can_manage_rules)
            )
        
        # 个人规则只能由创建者修改
        return obj.created_by == request.user


class CanExportData(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以导出数据
    """
    
    def has_permission(self, request, view):
        if request.user.is_staff:
            return True
        
        if hasattr(request.user, 'userprofile'):
            return request.user.userprofile.can_export_data
        
        return False


class CanImportData(permissions.BasePermission):
    """
    自定义权限：检查用户是否可以导入数据
    """
    
    def has_permission(self, request, view):
        # 所有认证用户都可以导入数据，但会有配额限制
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # 只能操作自己的导入批次
        return obj.uploaded_by == request.user or request.user.is_staff
