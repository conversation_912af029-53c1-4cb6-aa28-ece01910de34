import django_filters
from django.db.models import Q
from .models import DefectRecord, ImportBatch, ClassificationRule


class DefectRecordFilter(django_filters.FilterSet):
    """缺陷记录过滤器"""
    
    # 基础字段过滤
    issue_key = django_filters.CharFilter(lookup_expr='icontains')
    summary = django_filters.CharFilter(lookup_expr='icontains')
    status = django_filters.MultipleChoiceFilter(
        choices=[],  # 动态获取
        widget=django_filters.widgets.CSVWidget
    )
    priority = django_filters.MultipleChoiceFilter(
        choices=[],  # 动态获取
        widget=django_filters.widgets.CSVWidget
    )
    
    # 项目相关过滤
    project_key = django_filters.CharFilter(lookup_expr='icontains')
    project_name = django_filters.CharFilter(lookup_expr='icontains')
    
    # 人员相关过滤
    assignee = django_filters.Char<PERSON>ilter(lookup_expr='icontains')
    reporter = django_filters.CharFilter(lookup_expr='icontains')
    
    # 时间范围过滤
    created_after = django_filters.DateTimeFilter(field_name='created', lookup_expr='gte')
    created_before = django_filters.DateTimeFilter(field_name='created', lookup_expr='lte')
    updated_after = django_filters.DateTimeFilter(field_name='updated', lookup_expr='gte')
    updated_before = django_filters.DateTimeFilter(field_name='updated', lookup_expr='lte')
    
    # 导入相关过滤
    import_batch_id = django_filters.CharFilter()
    import_date_after = django_filters.DateTimeFilter(field_name='import_timestamp', lookup_expr='gte')
    import_date_before = django_filters.DateTimeFilter(field_name='import_timestamp', lookup_expr='lte')
    
    # 分类相关过滤
    device_model = django_filters.CharFilter(lookup_expr='icontains')
    bug_category = django_filters.CharFilter(lookup_expr='icontains')
    commonality = django_filters.CharFilter(lookup_expr='icontains')
    
    # 自定义过滤
    has_device_model = django_filters.BooleanFilter(method='filter_has_device_model')
    has_classification = django_filters.BooleanFilter(method='filter_has_classification')
    has_commonality = django_filters.BooleanFilter(method='filter_has_commonality')
    
    # 复合搜索
    search = django_filters.CharFilter(method='filter_search')
    
    class Meta:
        model = DefectRecord
        fields = [
            'issue_key', 'summary', 'status', 'priority',
            'project_key', 'project_name', 'assignee', 'reporter',
            'device_model', 'bug_category', 'commonality',
            'import_batch_id'
        ]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 动态获取状态选项
        if self.queryset:
            status_choices = self.queryset.values_list('status', 'status').distinct()
            self.filters['status'].extra['choices'] = status_choices
            
            priority_choices = self.queryset.values_list('priority', 'priority').distinct()
            self.filters['priority'].extra['choices'] = priority_choices
    
    def filter_has_device_model(self, queryset, name, value):
        """过滤是否有设备型号"""
        if value:
            return queryset.exclude(device_model__isnull=True).exclude(device_model__exact='')
        else:
            return queryset.filter(Q(device_model__isnull=True) | Q(device_model__exact=''))
    
    def filter_has_classification(self, queryset, name, value):
        """过滤是否已分类"""
        if value:
            return queryset.exclude(bug_category__isnull=True).exclude(bug_category__exact='')
        else:
            return queryset.filter(Q(bug_category__isnull=True) | Q(bug_category__exact=''))
    
    def filter_has_commonality(self, queryset, name, value):
        """过滤是否有共性问题"""
        if value:
            return queryset.exclude(commonality__isnull=True).exclude(commonality__exact='')
        else:
            return queryset.filter(Q(commonality__isnull=True) | Q(commonality__exact=''))
    
    def filter_search(self, queryset, name, value):
        """全文搜索"""
        if not value:
            return queryset
        
        return queryset.filter(
            Q(issue_key__icontains=value) |
            Q(summary__icontains=value) |
            Q(description__icontains=value) |
            Q(project_key__icontains=value) |
            Q(assignee__icontains=value) |
            Q(device_model__icontains=value) |
            Q(bug_category__icontains=value)
        )


class ImportBatchFilter(django_filters.FilterSet):
    """导入批次过滤器"""
    
    batch_name = django_filters.CharFilter(lookup_expr='icontains')
    batch_id = django_filters.CharFilter(lookup_expr='icontains')
    status = django_filters.MultipleChoiceFilter(
        choices=ImportBatch.STATUS_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    data_source = django_filters.CharFilter(lookup_expr='icontains')
    
    # 时间范围过滤
    import_date_after = django_filters.DateTimeFilter(field_name='import_timestamp', lookup_expr='gte')
    import_date_before = django_filters.DateTimeFilter(field_name='import_timestamp', lookup_expr='lte')
    
    # 记录数量过滤
    min_records = django_filters.NumberFilter(field_name='total_records', lookup_expr='gte')
    max_records = django_filters.NumberFilter(field_name='total_records', lookup_expr='lte')
    
    class Meta:
        model = ImportBatch
        fields = ['batch_name', 'batch_id', 'status', 'data_source']


class ClassificationRuleFilter(django_filters.FilterSet):
    """分类规则过滤器"""
    
    rule_name = django_filters.CharFilter(lookup_expr='icontains')
    rule_type = django_filters.MultipleChoiceFilter(
        choices=ClassificationRule.RULE_TYPE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    scope = django_filters.MultipleChoiceFilter(
        choices=ClassificationRule.SCOPE_CHOICES,
        widget=django_filters.widgets.CSVWidget
    )
    pattern = django_filters.CharFilter(lookup_expr='icontains')
    target_value = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    
    # 优先级范围过滤
    min_priority = django_filters.NumberFilter(field_name='priority', lookup_expr='gte')
    max_priority = django_filters.NumberFilter(field_name='priority', lookup_expr='lte')
    
    class Meta:
        model = ClassificationRule
        fields = ['rule_name', 'rule_type', 'scope', 'is_active']
