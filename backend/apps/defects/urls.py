from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    DefectRecordViewSet, FieldMappingViewSet,
    ImportBatchViewSet, ClassificationRuleViewSet
)

# 创建路由器并注册ViewSet
router = DefaultRouter()
router.register(r'records', DefectRecordViewSet, basename='defectrecord')
router.register(r'field-mappings', FieldMappingViewSet, basename='fieldmapping')
router.register(r'import-batches', ImportBatchViewSet, basename='importbatch')
router.register(r'classification-rules', ClassificationRuleViewSet, basename='classificationrule')

# URL模式
urlpatterns = [
    # API路由
    path('', include(router.urls)),

    # 自定义路由（如果需要）
    # path('custom-endpoint/', custom_view, name='custom-endpoint'),
]

# 生成的URL模式：
# GET    /api/defects/records/                     - 获取缺陷记录列表
# POST   /api/defects/records/                     - 创建缺陷记录
# GET    /api/defects/records/{id}/                - 获取单个缺陷记录
# PUT    /api/defects/records/{id}/                - 更新缺陷记录
# PATCH  /api/defects/records/{id}/                - 部分更新缺陷记录
# DELETE /api/defects/records/{id}/                - 删除缺陷记录
# POST   /api/defects/records/import_data/         - 导入数据
# GET    /api/defects/records/export_data/         - 导出数据
# POST   /api/defects/records/batch_classify/      - 批量分类
# DELETE /api/defects/records/batch_delete/        - 批量删除
# GET    /api/defects/records/statistics/          - 统计信息

# GET    /api/defects/field-mappings/              - 获取字段映射列表
# POST   /api/defects/field-mappings/              - 创建字段映射
# GET    /api/defects/field-mappings/{id}/         - 获取单个字段映射
# PUT    /api/defects/field-mappings/{id}/         - 更新字段映射
# DELETE /api/defects/field-mappings/{id}/         - 删除字段映射
# GET    /api/defects/field-mappings/core_fields/  - 获取核心字段
# POST   /api/defects/field-mappings/validate_csv_headers/ - 验证CSV头

# GET    /api/defects/import-batches/              - 获取导入批次列表
# POST   /api/defects/import-batches/              - 创建导入批次
# GET    /api/defects/import-batches/{id}/         - 获取单个导入批次
# PUT    /api/defects/import-batches/{id}/         - 更新导入批次
# DELETE /api/defects/import-batches/{id}/         - 删除导入批次
# POST   /api/defects/import-batches/{id}/cancel/  - 取消导入任务
# GET    /api/defects/import-batches/{id}/progress/ - 获取导入进度

# GET    /api/defects/classification-rules/        - 获取分类规则列表
# POST   /api/defects/classification-rules/        - 创建分类规则
# GET    /api/defects/classification-rules/{id}/   - 获取单个分类规则
# PUT    /api/defects/classification-rules/{id}/   - 更新分类规则
# DELETE /api/defects/classification-rules/{id}/   - 删除分类规则
# POST   /api/defects/classification-rules/{id}/test_rule/ - 测试规则
# POST   /api/defects/classification-rules/import_rules/   - 导入规则
# GET    /api/defects/classification-rules/export_rules/   - 导出规则
