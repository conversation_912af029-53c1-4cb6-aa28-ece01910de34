# Windows专用依赖文件 - 使用预编译包避免编译问题

# Django核心 - 使用稳定版本
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1

# 异步和WebSocket - 简化版本
channels==4.0.0
channels-redis==4.1.0

# 任务队列 - 使用兼容版本
celery==5.3.4
redis==5.0.1
kombu==5.3.4

# 数据处理 - 使用预编译版本
pandas==2.1.4
numpy==1.24.4
openpyxl==3.1.2
python-dateutil==2.8.2

# 数据库 - 使用binary版本避免编译
psycopg2-binary==2.9.9

# 工具库
django-extensions==3.2.3
python-dotenv==1.0.0
django-filter==23.5

# 部署
gunicorn==21.2.0
whitenoise==6.6.0

# Windows特定依赖
pywin32==306; sys_platform == "win32"

# 可选依赖 - 如果安装失败可以跳过
# lxml==4.9.3
# Pillow==10.1.0
