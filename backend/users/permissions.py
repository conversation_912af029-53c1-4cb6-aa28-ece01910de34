from django.db.models import Q
from rest_framework.permissions import BasePermission
from .models import UserProfile


class DataPermissionMixin:
    """数据权限混入类"""
    
    @property
    def is_admin(self):
        """检查是否为管理员"""
        user = self.request.user
        return (user.is_staff or 
                (hasattr(user, 'userprofile') and user.userprofile.can_view_all_data))
    
    def get_accessible_defects(self):
        """获取用户可访问的缺陷数据"""
        from defects.models import DefectRecord
        
        if self.is_admin:
            return DefectRecord.objects.all()
        else:
            return DefectRecord.objects.filter(uploaded_by=self.request.user)
    
    def get_accessible_batches(self):
        """获取用户可访问的批次数据"""
        from defects.models import ImportBatch
        
        if self.is_admin:
            return ImportBatch.objects.all()
        else:
            return ImportBatch.objects.filter(uploaded_by=self.request.user)


class IsAdminOrReadOnly(BasePermission):
    """管理员可写，其他用户只读"""
    
    def has_permission(self, request, view):
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return request.user.is_authenticated
        
        return (request.user.is_staff or 
                (hasattr(request.user, 'userprofile') and 
                 request.user.userprofile.can_manage_rules))


class CanManageRules(BasePermission):
    """可以管理规则的权限"""
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and
                (request.user.is_staff or 
                 (hasattr(request.user, 'userprofile') and 
                  request.user.userprofile.can_manage_rules)))


class CanExportData(BasePermission):
    """可以导出数据的权限"""
    
    def has_permission(self, request, view):
        return (request.user.is_authenticated and
                (request.user.is_staff or 
                 (hasattr(request.user, 'userprofile') and 
                  request.user.userprofile.can_export_data)))


class IsOwnerOrAdmin(BasePermission):
    """是所有者或管理员"""
    
    def has_object_permission(self, request, view, obj):
        # 管理员可以访问所有对象
        if request.user.is_staff:
            return True
        
        # 检查是否有 uploaded_by 或 created_by 字段
        if hasattr(obj, 'uploaded_by'):
            return obj.uploaded_by == request.user
        elif hasattr(obj, 'created_by'):
            return obj.created_by == request.user
        
        return False


class DataPermissionService:
    """数据权限服务"""
    
    def __init__(self, user):
        self.user = user
        self.user_profile = UserProfile.objects.get_or_create(user=user)[0]
    
    def can_view_all_data(self):
        """是否可以查看所有数据"""
        return self.user.is_staff or self.user_profile.can_view_all_data
    
    def can_manage_rules(self):
        """是否可以管理规则"""
        return self.user.is_staff or self.user_profile.can_manage_rules
    
    def can_export_data(self):
        """是否可以导出数据"""
        return self.user.is_staff or self.user_profile.can_export_data
    
    def get_accessible_defects(self):
        """获取可访问的缺陷数据"""
        from defects.models import DefectRecord
        
        if self.can_view_all_data():
            return DefectRecord.objects.all()
        else:
            return DefectRecord.objects.filter(uploaded_by=self.user)
    
    def get_accessible_batches(self):
        """获取可访问的批次数据"""
        from defects.models import ImportBatch
        
        if self.can_view_all_data():
            return ImportBatch.objects.all()
        else:
            return ImportBatch.objects.filter(uploaded_by=self.user)
    
    def get_accessible_rules(self):
        """获取可访问的规则"""
        from defects.models import ClassificationRule
        
        if self.can_manage_rules():
            return ClassificationRule.objects.all()
        else:
            return ClassificationRule.objects.filter(
                Q(scope='global') | Q(scope='personal', created_by=self.user)
            )
    
    def can_edit_rule(self, rule):
        """是否可以编辑规则"""
        if rule.scope == 'global':
            return self.can_manage_rules()
        else:
            return rule.created_by == self.user
    
    def can_delete_rule(self, rule):
        """是否可以删除规则"""
        return self.can_edit_rule(rule)
    
    def can_import_file(self, file_size, record_count=None):
        """是否可以导入文件"""
        return self.user_profile.can_import_file(file_size, record_count)
    
    def get_import_limits(self):
        """获取导入限制"""
        return {
            'max_import_size': self.user_profile.max_import_size,
            'max_records_per_import': self.user_profile.max_records_per_import,
            'max_personal_rules': self.user_profile.max_personal_rules,
            'max_concurrent_tasks': self.user_profile.max_concurrent_tasks,
        }
