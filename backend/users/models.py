from django.db import models
from django.contrib.auth.models import User


class UserProfile(models.Model):
    """用户配置扩展模型"""
    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('user', '普通用户'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='user', verbose_name='角色')
    
    # 权限配置
    can_view_all_data = models.BooleanField(default=False, verbose_name='可查看全部数据')
    can_manage_rules = models.BooleanField(default=False, verbose_name='可管理规则')
    can_export_data = models.BooleanField(default=True, verbose_name='可导出数据')
    
    # 配额设置
    max_import_size = models.BigIntegerField(default=50*1024*1024, verbose_name='最大导入文件大小(字节)')
    max_records_per_import = models.IntegerField(default=10000, verbose_name='单次导入最大记录数')
    max_personal_rules = models.IntegerField(default=500, verbose_name='最大个人规则数')
    max_concurrent_tasks = models.IntegerField(default=2, verbose_name='最大并发任务数')
    
    # 统计信息
    total_imports = models.IntegerField(default=0, verbose_name='总导入次数')
    total_records = models.IntegerField(default=0, verbose_name='总导入记录数')
    last_import_at = models.DateTimeField(null=True, blank=True, verbose_name='最后导入时间')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '用户配置'
        verbose_name_plural = '用户配置'
    
    def __str__(self):
        return f"{self.user.username} ({self.get_role_display()})"
    
    @property
    def is_admin(self):
        return self.role == 'admin' or self.user.is_staff
    
    def can_import_file(self, file_size, record_count=None):
        """检查是否可以导入文件"""
        if file_size > self.max_import_size:
            return False, f"文件大小超过限制 ({file_size} > {self.max_import_size})"
        
        if record_count and record_count > self.max_records_per_import:
            return False, f"记录数超过限制 ({record_count} > {self.max_records_per_import})"
        
        return True, "可以导入"


class OperationLog(models.Model):
    """操作日志模型"""
    OPERATION_TYPE_CHOICES = [
        ('import', '数据导入'),
        ('export', '数据导出'),
        ('delete', '数据删除'),
        ('rule_create', '创建规则'),
        ('rule_update', '更新规则'),
        ('rule_delete', '删除规则'),
        ('login', '用户登录'),
        ('logout', '用户登出'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='操作用户')
    operation_type = models.CharField(max_length=50, choices=OPERATION_TYPE_CHOICES, verbose_name='操作类型')
    target_type = models.CharField(max_length=50, verbose_name='目标类型')
    target_id = models.CharField(max_length=100, verbose_name='目标ID')
    operation_detail = models.JSONField(default=dict, verbose_name='操作详情')
    ip_address = models.GenericIPAddressField(verbose_name='IP地址')
    user_agent = models.TextField(verbose_name='用户代理', blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')
    
    class Meta:
        verbose_name = '操作日志'
        verbose_name_plural = '操作日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'operation_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.get_operation_type_display()} - {self.created_at}"
