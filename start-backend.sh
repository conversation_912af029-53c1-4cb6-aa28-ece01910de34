#!/bin/bash

# 后端启动脚本 (支持Linux/macOS/Windows)

echo "🚀 启动Django后端服务..."

# 检测操作系统
OS="unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    OS="windows"
fi

echo "📱 检测到操作系统: $OS"

# 检查Python环境
PYTHON_CMD="python3"
if [[ "$OS" == "windows" ]]; then
    PYTHON_CMD="python"
fi

if ! command -v $PYTHON_CMD &> /dev/null; then
    echo "❌ Python 未安装，请先安装Python"
    exit 1
fi

# 检查项目结构
if [ ! -d "backend" ]; then
    echo "❌ backend目录不存在，请确保在项目根目录运行"
    exit 1
fi

cd backend

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    $PYTHON_CMD -m venv venv
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
if [[ "$OS" == "windows" ]]; then
    source venv/Scripts/activate
else
    source venv/bin/activate
fi

# 升级pip和安装工具
echo "🔧 升级pip和安装工具..."
pip install --upgrade pip setuptools wheel

# 安装依赖
echo "📦 安装后端依赖..."
if [ -f "requirements-minimal.txt" ]; then
    echo "使用最小化依赖..."
    pip install -r requirements-minimal.txt
elif [ -f "requirements-windows.txt" ] && [[ "$OS" == "windows" ]]; then
    echo "使用Windows专用依赖..."
    pip install -r requirements-windows.txt
elif [ -f "requirements.txt" ]; then
    echo "使用标准依赖..."
    pip install -r requirements.txt
else
    echo "❌ 未找到依赖文件"
    exit 1
fi

# 检查Django是否安装成功
if ! python -c "import django" 2>/dev/null; then
    echo "❌ Django安装失败，尝试手动安装核心包..."
    pip install Django>=4.2,<5.0
    pip install djangorestframework>=3.14.0
    pip install django-cors-headers>=4.0.0
    pip install python-dotenv>=1.0.0
fi

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（如果不存在）
echo "👤 检查超级用户..."
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ 创建超级用户: admin/admin123')
else:
    print('✅ 超级用户已存在')
"

# 初始化字段映射（如果需要）
echo "🔧 初始化字段映射..."
python -c "
import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
import django
django.setup()
try:
    from apps.defects.models import FieldMapping
    if not FieldMapping.objects.exists():
        mappings = [
            {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True},
            {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True},
            {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True},
            {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True},
        ]
        for mapping in mappings:
            FieldMapping.objects.create(**mapping)
        print('✅ 初始化字段映射完成')
    else:
        print('✅ 字段映射已存在')
except ImportError:
    print('⚠️  字段映射模型未找到，跳过初始化')
"

# 收集静态文件
echo "📁 收集静态文件..."
python manage.py collectstatic --noinput

echo ""
echo "✅ 后端环境准备完成！"
echo ""
echo "🌐 启动Django开发服务器..."
echo "   访问地址: http://localhost:8000"
echo "   管理后台: http://localhost:8000/admin"
echo "   API文档: http://localhost:8000/api"
echo ""
echo "👤 管理员账号:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "⏹️  停止服务: Ctrl+C"
echo ""

# 启动Django开发服务器
python manage.py runserver 0.0.0.0:8000
