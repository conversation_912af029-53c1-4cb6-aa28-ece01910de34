@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动缺陷分类工具开发环境 (Windows)...
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
    echo    请从 https://www.python.org/downloads/ 下载并安装Python
    echo    安装时请勾选 "Add Python to PATH"
    pause
    exit /b 1
)

REM 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未添加到PATH
    echo    请从 https://nodejs.org/ 下载并安装Node.js
    pause
    exit /b 1
)

REM 检查Redis（可选）
redis-server --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Redis 未安装或未添加到PATH
    echo    推荐安装方法:
    echo    1. 使用WSL: wsl --install，然后在WSL中安装Redis
    echo    2. 使用Docker: docker run -d -p 6379:6379 redis:alpine
    echo    3. 下载Windows版本: https://github.com/microsoftarchive/redis/releases
    echo.
    set /p continue="是否继续启动(不启动Redis)? (y/N): "
    if /i not "!continue!"=="y" (
        exit /b 1
    )
    set SKIP_REDIS=true
)

REM 启动Redis
if not "!SKIP_REDIS!"=="true" (
    echo 📦 启动Redis服务...
    start /B redis-server
    timeout /t 2 >nul
) else (
    echo ⚠️  跳过Redis启动，请确保Redis服务正在运行
)

REM 后端设置
echo 🔧 设置后端环境...
if not exist "backend" (
    echo ❌ backend目录不存在，请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
cd backend

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📦 安装后端依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 安装Python依赖失败，请检查网络连接和requirements.txt文件
    pause
    exit /b 1
)

REM 数据库迁移
echo 🗄️ 执行数据库迁移...
python manage.py makemigrations
if errorlevel 1 (
    echo ❌ 创建数据库迁移失败
    pause
    exit /b 1
)
python manage.py migrate
if errorlevel 1 (
    echo ❌ 执行数据库迁移失败
    pause
    exit /b 1
)

REM 创建超级用户（如果不存在）
echo 👤 检查超级用户...
echo import os > create_user.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'defect_classification.settings') >> create_user.py
echo import django >> create_user.py
echo django.setup() >> create_user.py
echo from django.contrib.auth.models import User >> create_user.py
echo if not User.objects.filter(username='admin').exists(): >> create_user.py
echo     User.objects.create_superuser('admin', '<EMAIL>', 'admin123') >> create_user.py
echo     print('✅ 创建超级用户: admin/admin123') >> create_user.py
echo else: >> create_user.py
echo     print('✅ 超级用户已存在') >> create_user.py
python create_user.py
del create_user.py

REM 初始化字段映射
echo 🔧 初始化字段映射...
echo import os > init_mapping.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'defect_classification.settings') >> init_mapping.py
echo import django >> init_mapping.py
echo django.setup() >> init_mapping.py
echo from defects.models import FieldMapping >> init_mapping.py
echo if not FieldMapping.objects.exists(): >> init_mapping.py
echo     mappings = [ >> init_mapping.py
echo         {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True}, >> init_mapping.py
echo         {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True}, >> init_mapping.py
echo         {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True}, >> init_mapping.py
echo         {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True}, >> init_mapping.py
echo         {'csv_field_name': 'Assignee', 'db_field_name': 'assignee', 'field_type': 'string'}, >> init_mapping.py
echo         {'csv_field_name': 'Created', 'db_field_name': 'created', 'field_type': 'datetime'}, >> init_mapping.py
echo         {'csv_field_name': 'Project key', 'db_field_name': 'project_key', 'field_type': 'string'}, >> init_mapping.py
echo         {'csv_field_name': 'Description', 'db_field_name': 'description', 'field_type': 'text'}, >> init_mapping.py
echo     ] >> init_mapping.py
echo     for mapping in mappings: >> init_mapping.py
echo         FieldMapping.objects.create(**mapping) >> init_mapping.py
echo     print('✅ 初始化字段映射完成') >> init_mapping.py
echo else: >> init_mapping.py
echo     print('✅ 字段映射已存在') >> init_mapping.py
python init_mapping.py
del init_mapping.py

REM 启动Celery Worker（新窗口）
echo 🔄 启动Celery Worker...
start "Celery Worker" cmd /k "venv\Scripts\activate.bat && celery -A defect_classification worker --loglevel=info --pool=solo"

REM 启动Django开发服务器（新窗口）
echo 🌐 启动Django开发服务器...
start "Django Server" cmd /k "venv\Scripts\activate.bat && python manage.py runserver 0.0.0.0:8000"

cd ..

REM 前端设置
echo 🎨 设置前端环境...
cd ..
if not exist "frontend" (
    echo ❌ frontend目录不存在，请确保项目结构完整
    pause
    exit /b 1
)
cd frontend

REM 安装依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 安装前端依赖失败，请检查网络连接
        pause
        exit /b 1
    )
)

REM 启动前端开发服务器（新窗口）
echo 🌐 启动前端开发服务器...
start "Frontend Server" cmd /k "npm run dev"

cd ..

echo.
echo ✅ 开发环境启动完成！
echo.
echo 🌐 访问地址:
echo    前端: http://localhost:3000
echo    后端API: http://localhost:8000/api
echo    Django管理后台: http://localhost:8000/admin
echo.
echo 👤 管理员账号:
echo    用户名: admin
echo    密码: admin123
echo.
echo 📝 日志文件:
echo    Django: backend\logs\django.log
echo    Celery: backend\celery.log
echo.
echo 🔧 Windows特别说明:
echo    - 各服务在独立的命令行窗口中运行
echo    - 关闭对应窗口即可停止相应服务
echo    - 或运行 stop-dev.bat 停止所有服务
echo    - 如遇权限问题，请以管理员身份运行
echo    - 建议使用Windows Terminal获得更好体验
echo.
echo ⏹️  停止服务: 运行 stop-dev.bat 或关闭各服务窗口
echo.
pause
