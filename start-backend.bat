@echo off
chcp 65001 >nul

echo 🚀 启动Django后端服务 (Windows)...
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

REM 检查项目结构
if not exist "backend" (
    echo ❌ backend目录不存在，请确保在项目根目录运行
    pause
    exit /b 1
)

cd backend

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

REM 升级pip和安装工具
echo 🔧 升级pip和安装工具...
python -m pip install --upgrade pip setuptools wheel

REM 安装依赖
echo 📦 安装后端依赖...
if exist "requirements-minimal.txt" (
    echo 使用最小化依赖...
    pip install -r requirements-minimal.txt --no-cache-dir
) else if exist "requirements-windows.txt" (
    echo 使用Windows专用依赖...
    pip install -r requirements-windows.txt --no-cache-dir
) else if exist "requirements.txt" (
    echo 使用标准依赖...
    pip install -r requirements.txt --no-cache-dir
) else (
    echo ❌ 未找到依赖文件
    pause
    exit /b 1
)

REM 检查Django是否安装成功
python -c "import django" >nul 2>&1
if errorlevel 1 (
    echo ❌ Django安装失败，尝试手动安装核心包...
    pip install Django==4.2.7 --no-cache-dir
    pip install djangorestframework==3.14.0 --no-cache-dir
    pip install django-cors-headers==4.3.1 --no-cache-dir
    pip install python-dotenv==1.0.0 --no-cache-dir
)

REM 数据库迁移
echo 🗄️ 执行数据库迁移...
python manage.py makemigrations
if errorlevel 1 (
    echo ❌ 创建迁移失败
    pause
    exit /b 1
)

python manage.py migrate
if errorlevel 1 (
    echo ❌ 执行迁移失败
    pause
    exit /b 1
)

REM 创建超级用户
echo 👤 检查超级用户...
echo import os > create_admin.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings') >> create_admin.py
echo import django >> create_admin.py
echo django.setup() >> create_admin.py
echo from django.contrib.auth.models import User >> create_admin.py
echo if not User.objects.filter(username='admin').exists(): >> create_admin.py
echo     User.objects.create_superuser('admin', '<EMAIL>', 'admin123') >> create_admin.py
echo     print('✅ 创建超级用户: admin/admin123') >> create_admin.py
echo else: >> create_admin.py
echo     print('✅ 超级用户已存在') >> create_admin.py
python create_admin.py
del create_admin.py

REM 初始化字段映射
echo 🔧 初始化字段映射...
echo import os > init_fields.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings') >> init_fields.py
echo import django >> init_fields.py
echo django.setup() >> init_fields.py
echo try: >> init_fields.py
echo     from apps.defects.models import FieldMapping >> init_fields.py
echo     if not FieldMapping.objects.exists(): >> init_fields.py
echo         mappings = [ >> init_fields.py
echo             {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True}, >> init_fields.py
echo             {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True}, >> init_fields.py
echo             {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True}, >> init_fields.py
echo             {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True}, >> init_fields.py
echo         ] >> init_fields.py
echo         for mapping in mappings: >> init_fields.py
echo             FieldMapping.objects.create(**mapping) >> init_fields.py
echo         print('✅ 初始化字段映射完成') >> init_fields.py
echo     else: >> init_fields.py
echo         print('✅ 字段映射已存在') >> init_fields.py
echo except ImportError: >> init_fields.py
echo     print('⚠️  字段映射模型未找到，跳过初始化') >> init_fields.py
python init_fields.py
del init_fields.py

REM 收集静态文件
echo 📁 收集静态文件...
python manage.py collectstatic --noinput

echo.
echo ✅ 后端环境准备完成！
echo.
echo 🌐 启动Django开发服务器...
echo    访问地址: http://localhost:8000
echo    管理后台: http://localhost:8000/admin
echo    API文档: http://localhost:8000/api
echo.
echo 👤 管理员账号:
echo    用户名: admin
echo    密码: admin123
echo.
echo ⏹️  停止服务: Ctrl+C
echo.

REM 启动Django开发服务器
python manage.py runserver 0.0.0.0:8000
