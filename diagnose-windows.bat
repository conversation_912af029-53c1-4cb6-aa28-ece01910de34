@echo off
chcp 65001 >nul

echo 🔍 Windows环境诊断工具...
echo.

echo 📋 系统信息:
echo ================
systeminfo | findstr /C:"OS Name" /C:"OS Version" /C:"System Type"
echo.

echo 🐍 Python信息:
echo ================
python --version 2>nul
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到PATH
) else (
    echo ✅ Python 已安装
    python -c "import sys; print(f'Python路径: {sys.executable}')"
    python -c "import platform; print(f'架构: {platform.architecture()}')"
)
echo.

echo 📦 pip信息:
echo ================
pip --version 2>nul
if errorlevel 1 (
    echo ❌ pip 不可用
) else (
    echo ✅ pip 可用
    pip config list
)
echo.

echo 🔧 编译工具检查:
echo ================
where cl 2>nul
if errorlevel 1 (
    echo ❌ Microsoft C++ 编译器未找到
    echo 💡 建议安装: Microsoft C++ Build Tools
    echo    下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/
) else (
    echo ✅ Microsoft C++ 编译器已安装
)
echo.

echo 🌐 网络连接检查:
echo ================
ping -n 1 pypi.org >nul 2>&1
if errorlevel 1 (
    echo ❌ 无法连接到 pypi.org
) else (
    echo ✅ 可以连接到 pypi.org
)

ping -n 1 pypi.tuna.tsinghua.edu.cn >nul 2>&1
if errorlevel 1 (
    echo ❌ 无法连接到清华镜像源
) else (
    echo ✅ 可以连接到清华镜像源
)
echo.

echo 📁 项目结构检查:
echo ================
if exist "backend" (
    echo ✅ backend目录存在
    if exist "backend\requirements.txt" (
        echo ✅ requirements.txt 存在
    ) else (
        echo ❌ requirements.txt 不存在
    )
    if exist "backend\requirements-windows.txt" (
        echo ✅ requirements-windows.txt 存在
    ) else (
        echo ⚠️  requirements-windows.txt 不存在
    )
) else (
    echo ❌ backend目录不存在
)

if exist "frontend" (
    echo ✅ frontend目录存在
) else (
    echo ❌ frontend目录不存在
)
echo.

echo 💾 磁盘空间检查:
echo ================
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do echo 可用空间: %%a bytes
echo.

echo 🔒 权限检查:
echo ================
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  当前不是管理员权限
    echo 💡 某些操作可能需要管理员权限
) else (
    echo ✅ 当前具有管理员权限
)
echo.

echo 📊 诊断完成！
echo.
echo 💡 解决建议:
echo ================
echo 1. 如果缺少编译工具，安装 Microsoft C++ Build Tools
echo 2. 如果网络有问题，尝试使用代理或镜像源
echo 3. 如果权限不足，以管理员身份运行
echo 4. 尝试运行 fix-windows-install.bat 自动修复
echo.

pause
