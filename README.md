# 缺陷分类工具

基于Django + Vue3的现代化缺陷数据管理和自动分类系统。

## 🎯 功能特性

### 核心功能
- ✅ **智能字段映射**: 支持部分字段导入，自动字段识别和映射
- ✅ **多用户并发导入**: 异步任务队列，支持多用户同时导入
- ✅ **实时进度监控**: WebSocket实时推送任务状态和进度
- ✅ **权限管理**: 基于角色的数据权限控制
- ✅ **自动分类**: 基于规则的智能缺陷分类
- ✅ **数据分析**: 多维度统计分析和可视化

### 技术特性
- 🚀 **现代化架构**: Django 5.2 + Vue 3 + TypeScript
- 📊 **数据可视化**: ECharts图表库
- 🔄 **异步处理**: Celery + Redis任务队列
- 🌐 **实时通信**: WebSocket推送
- 📱 **响应式设计**: 支持桌面和移动端

## 🏗️ 技术栈

### 后端
- **框架**: Django 5.2 + Django REST Framework
- **数据库**: PostgreSQL / SQLite
- **缓存**: Redis
- **任务队列**: Celery
- **实时通信**: Django Channels + WebSocket

### 前端
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **状态管理**: Pinia
- **构建工具**: Vite
- **图表**: ECharts

## 🚀 快速开始

### 开发环境要求
- Python 3.11+
- Node.js 18+
- Redis 6+
- PostgreSQL 13+ (可选，默认使用SQLite)

### 启动方式

#### 方式1: 分别启动 (推荐开发)

**后端启动:**
```bash
# Linux/macOS
./start-backend.sh

# Windows
start-backend.bat
```

**前端启动:**
```bash
# Linux/macOS
./start-frontend.sh

# Windows
start-frontend.bat
```

**Celery启动 (可选):**
```bash
# Linux/macOS
./start-celery.sh

# Windows
start-celery.bat
```

#### 方式2: 一键启动全部服务

**Linux/macOS:**
```bash
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 给脚本执行权限
chmod +x *.sh

# 启动完整环境
./start-dev.sh
```

**Windows:**
```cmd
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 启动完整环境
start-dev.bat
```

启动后访问：
- 前端: http://localhost:3000
- 后端API: http://localhost:8000/api
- 管理后台: http://localhost:8000/admin

默认管理员账号: `admin` / `admin123`

#### Windows 特别说明
- **推荐使用**: Windows Terminal 或 PowerShell 获得更好的体验
- **权限问题**: 如遇权限问题，请以管理员身份运行
- **Redis安装**: Windows下推荐使用WSL或Docker运行Redis
- **服务管理**: 各服务在独立窗口运行，关闭窗口即停止服务
- **停止服务**: 运行 `stop-dev.bat` 或 `stop-dev.ps1`

### 手动启动

#### 后端启动
```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动Redis
redis-server

# 启动Celery Worker
celery -A defect_classification worker --loglevel=info

# 启动Django服务器
python manage.py runserver
```

#### 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 🐳 Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📁 项目结构

```
defect-classification-tool/
├── backend/                    # Django后端
│   ├── config/                # 项目配置
│   │   ├── settings.py        # Django设置
│   │   ├── urls.py           # URL路由
│   │   ├── wsgi.py           # WSGI配置
│   │   ├── asgi.py           # ASGI配置
│   │   └── celery.py         # Celery配置
│   ├── apps/                 # 应用目录
│   │   ├── defects/          # 缺陷管理应用
│   │   ├── tasks/            # 任务管理应用
│   │   ├── users/            # 用户管理应用
│   │   └── notifications/    # 通知应用
│   ├── manage.py             # Django管理脚本
│   ├── requirements.txt      # Python依赖
│   ├── requirements-windows.txt  # Windows专用依赖
│   └── requirements-minimal.txt  # 最小化依赖
├── frontend/                 # Vue3前端
│   ├── src/
│   │   ├── api/             # API接口
│   │   ├── components/      # Vue组件
│   │   ├── views/           # 页面组件
│   │   ├── stores/          # Pinia状态管理
│   │   ├── composables/     # 组合式函数
│   │   ├── types/           # TypeScript类型
│   │   └── utils/           # 工具函数
│   ├── package.json         # Node.js依赖
│   └── vite.config.ts       # Vite配置
├── docker-compose.yml       # Docker编排
├── start-backend.sh/.bat    # 后端启动脚本
├── start-frontend.sh/.bat   # 前端启动脚本
├── start-celery.sh/.bat     # Celery启动脚本(可选)
├── start-dev.sh/.bat        # 完整环境启动脚本
├── stop-dev.sh/.bat         # 环境停止脚本
└── README.md               # 项目文档
```

## 📖 使用指南

### 数据导入
1. 点击"导入数据"按钮
2. 选择Excel或CSV文件
3. 系统自动验证字段映射
4. 确认导入信息
5. 提交任务，系统异步处理

### 字段映射管理
- 管理员可配置CSV字段到数据库字段的映射关系
- 支持多种数据类型和验证规则
- 自动识别和建议字段映射

### 分类规则管理
- 支持正则表达式规则
- 全局规则和个人规则
- 规则优先级和测试功能

### 数据分析
- A/B/C类问题统计
- 设备型号缺陷分布
- 时间趋势分析
- 共性问题识别

## 🔧 配置说明

### 环境变量
```bash
# Django配置
DEBUG=True
SECRET_KEY=your-secret-key
DB_HOST=localhost
DB_NAME=defect_classification
DB_USER=postgres
DB_PASSWORD=password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB
MAX_RECORDS_PER_IMPORT=10000
```

### 字段映射配置
系统支持以下字段类型的自动映射：
- 核心字段: Issue key, Summary, Status, Priority
- 项目字段: Project key, Project name, Assignee
- 时间字段: Created, Updated, Resolved
- 自定义字段: 设备型号, 缺陷分类, 共性问题

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 问题反馈

如果您遇到问题或有建议，请：
1. 查看 [Issues](../../issues) 是否已有相关问题
2. 创建新的 Issue 描述问题
3. 提供详细的错误信息和复现步骤

## 📞 联系我们

- 项目维护者: [Your Name]
- 邮箱: <EMAIL>
- 项目地址: [GitHub Repository]
