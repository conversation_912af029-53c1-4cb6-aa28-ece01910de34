#!/bin/bash

# 缺陷分类工具开发环境启动脚本

echo "🚀 启动缺陷分类工具开发环境..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    exit 1
fi

# 检查Redis
if ! command -v redis-server &> /dev/null; then
    echo "❌ Redis 未安装，请先安装Redis"
    exit 1
fi

# 启动Redis
echo "📦 启动Redis服务..."
redis-server --daemonize yes

# 后端设置
echo "🔧 设置后端环境..."
cd backend

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
echo "📦 安装后端依赖..."
pip install -r requirements.txt

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（如果不存在）
echo "👤 检查超级用户..."
python manage.py shell -c "
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ 创建超级用户: admin/admin123')
else:
    print('✅ 超级用户已存在')
"

# 初始化字段映射
echo "🔧 初始化字段映射..."
python manage.py shell -c "
from defects.models import FieldMapping
from django.conf import settings

# 检查是否已有字段映射
if not FieldMapping.objects.exists():
    # 创建基础字段映射
    mappings = [
        {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True},
        {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True},
        {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True},
        {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True},
        {'csv_field_name': 'Assignee', 'db_field_name': 'assignee', 'field_type': 'string'},
        {'csv_field_name': 'Created', 'db_field_name': 'created', 'field_type': 'datetime'},
        {'csv_field_name': 'Project key', 'db_field_name': 'project_key', 'field_type': 'string'},
        {'csv_field_name': 'Description', 'db_field_name': 'description', 'field_type': 'text'},
    ]
    
    for mapping in mappings:
        FieldMapping.objects.create(**mapping)
    
    print('✅ 初始化字段映射完成')
else:
    print('✅ 字段映射已存在')
"

# 启动Celery Worker（后台）
echo "🔄 启动Celery Worker..."
celery -A defect_classification worker --loglevel=info --detach

# 启动Django开发服务器（后台）
echo "🌐 启动Django开发服务器..."
python manage.py runserver 0.0.0.0:8000 &
DJANGO_PID=$!

cd ..

# 前端设置
echo "🎨 设置前端环境..."
cd frontend

# 安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端开发服务器
echo "🌐 启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!

cd ..

echo "✅ 开发环境启动完成！"
echo ""
echo "🌐 访问地址:"
echo "   前端: http://localhost:3000"
echo "   后端API: http://localhost:8000/api"
echo "   Django管理后台: http://localhost:8000/admin"
echo ""
echo "👤 管理员账号:"
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📝 日志文件:"
echo "   Django: backend/logs/django.log"
echo "   Celery: backend/celery.log"
echo ""
echo "⏹️  停止服务: Ctrl+C 或运行 ./stop-dev.sh"

# 等待用户中断
wait
