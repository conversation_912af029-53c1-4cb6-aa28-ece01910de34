{"name": "defect-classification-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.0", "dayjs": "^1.11.0", "echarts": "^5.6.0", "element-plus": "^2.4.0", "lodash-es": "^4.17.0", "pinia": "^2.3.1", "vue": "^3.4.0", "vue-echarts": "^6.6.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/lodash-es": "^4.17.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^3.0.0", "sass": "^1.69.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vue-tsc": "^1.8.0"}}