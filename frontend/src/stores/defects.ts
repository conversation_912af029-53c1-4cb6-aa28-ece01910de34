import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { defectApi } from '@/api/defects'
import type { Defect, DefectFilters, PaginationParams } from '@/types/defect'

export const useDefectStore = defineStore('defects', () => {
  // 状态
  const defects = ref<Defect[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentDefect = ref<Defect | null>(null)

  // 计算属性
  const defectCount = computed(() => defects.value.length)
  const hasDefects = computed(() => defects.value.length > 0)

  // 按状态分组的缺陷
  const defectsByStatus = computed(() => {
    const groups: Record<string, Defect[]> = {}
    defects.value.forEach(defect => {
      const status = defect.status || 'Unknown'
      if (!groups[status]) {
        groups[status] = []
      }
      groups[status].push(defect)
    })
    return groups
  })

  // 按优先级分组的缺陷
  const defectsByPriority = computed(() => {
    const groups: Record<string, Defect[]> = {}
    defects.value.forEach(defect => {
      const priority = defect.priority || 'Unknown'
      if (!groups[priority]) {
        groups[priority] = []
      }
      groups[priority].push(defect)
    })
    return groups
  })

  // Actions
  const fetchDefects = async (params: DefectFilters & PaginationParams) => {
    loading.value = true
    try {
      const response = await defectApi.getDefects(params)
      defects.value = response.data.results
      total.value = response.data.count
      return response.data
    } catch (error) {
      console.error('Failed to fetch defects:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchDefectById = async (id: string) => {
    try {
      const response = await defectApi.getDefectById(id)
      currentDefect.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch defect:', error)
      throw error
    }
  }

  const updateDefect = async (id: string, data: Partial<Defect>) => {
    try {
      const response = await defectApi.updateDefect(id, data)
      const index = defects.value.findIndex(d => d.id === id)
      if (index !== -1) {
        defects.value[index] = response.data
      }
      if (currentDefect.value?.id === id) {
        currentDefect.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('Failed to update defect:', error)
      throw error
    }
  }

  const deleteDefect = async (id: string) => {
    try {
      await defectApi.deleteDefect(id)
      const index = defects.value.findIndex(d => d.id === id)
      if (index !== -1) {
        defects.value.splice(index, 1)
        total.value--
      }
      if (currentDefect.value?.id === id) {
        currentDefect.value = null
      }
    } catch (error) {
      console.error('Failed to delete defect:', error)
      throw error
    }
  }

  const exportDefects = async (filters: DefectFilters, format: 'excel' | 'csv' = 'excel') => {
    try {
      const response = await defectApi.exportDefects(filters, format)
      
      // 创建下载链接
      const blob = new Blob([response.data], {
        type: format === 'excel' 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `defects_export_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`
      link.click()
      
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export defects:', error)
      throw error
    }
  }

  const batchClassify = async (defectIds: string[]) => {
    try {
      const response = await defectApi.batchClassify(defectIds)
      // 更新本地数据
      response.data.defects?.forEach((updatedDefect: Defect) => {
        const index = defects.value.findIndex(d => d.id === updatedDefect.id)
        if (index !== -1) {
          defects.value[index] = updatedDefect
        }
      })
      return response.data
    } catch (error) {
      console.error('Failed to batch classify:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    defects.value = []
    total.value = 0
    currentDefect.value = null
    loading.value = false
  }

  return {
    // 状态
    defects,
    total,
    loading,
    currentDefect,
    
    // 计算属性
    defectCount,
    hasDefects,
    defectsByStatus,
    defectsByPriority,
    
    // Actions
    fetchDefects,
    fetchDefectById,
    updateDefect,
    deleteDefect,
    exportDefects,
    batchClassify,
    resetState
  }
})
