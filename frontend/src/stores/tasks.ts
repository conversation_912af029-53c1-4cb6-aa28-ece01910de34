import { define<PERSON>tore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi } from '@/api/tasks'
import type { Task, TaskFilters } from '@/types/task'

export const useTaskStore = defineStore('tasks', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)

  // 计算属性
  const runningTasks = computed(() => 
    tasks.value.filter(task => task.status === 'running')
  )

  const pendingTasks = computed(() => 
    tasks.value.filter(task => task.status === 'pending')
  )

  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed')
  )

  const failedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'failed')
  )

  const taskStats = computed(() => ({
    total: tasks.value.length,
    running: runningTasks.value.length,
    pending: pendingTasks.value.length,
    completed: completedTasks.value.length,
    failed: failedTasks.value.length
  }))

  // Actions
  const fetchTasks = async (filters?: TaskFilters) => {
    loading.value = true
    try {
      const response = await taskApi.getTasks(filters)
      tasks.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (taskId: string) => {
    try {
      const response = await taskApi.getTaskById(taskId)
      currentTask.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch task:', error)
      throw error
    }
  }

  const cancelTask = async (taskId: string) => {
    try {
      await taskApi.cancelTask(taskId)
      const task = tasks.value.find(t => t.task_id === taskId)
      if (task) {
        task.status = 'cancelled'
      }
    } catch (error) {
      console.error('Failed to cancel task:', error)
      throw error
    }
  }

  const updateTaskProgress = (taskId: string, progress: number, step: string) => {
    const task = tasks.value.find(t => t.task_id === taskId)
    if (task) {
      task.progress = progress
      task.current_step = step
    }
    
    if (currentTask.value?.task_id === taskId) {
      currentTask.value.progress = progress
      currentTask.value.current_step = step
    }
  }

  const updateTaskStatus = (taskId: string, status: string, result?: any) => {
    const task = tasks.value.find(t => t.task_id === taskId)
    if (task) {
      task.status = status as any
      if (result) {
        task.result_data = result
      }
      if (status === 'completed' || status === 'failed') {
        task.completed_at = new Date().toISOString()
      }
    }
    
    if (currentTask.value?.task_id === taskId) {
      currentTask.value.status = status as any
      if (result) {
        currentTask.value.result_data = result
      }
    }
  }

  const addTask = (task: Task) => {
    tasks.value.unshift(task)
  }

  const removeTask = (taskId: string) => {
    const index = tasks.value.findIndex(t => t.task_id === taskId)
    if (index !== -1) {
      tasks.value.splice(index, 1)
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    
    // 计算属性
    runningTasks,
    pendingTasks,
    completedTasks,
    failedTasks,
    taskStats,
    
    // Actions
    fetchTasks,
    fetchTaskById,
    cancelTask,
    updateTaskProgress,
    updateTaskStatus,
    addTask,
    removeTask
  }
})
