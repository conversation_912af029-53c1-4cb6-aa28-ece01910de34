import request from '@/utils/request'

export interface DashboardStats {
  defect_stats: {
    total_defects: number
    open_defects: number
    resolved_defects: number
    high_priority: number
    recent_defects: number
    classified_defects: number
  }
  batch_stats: {
    total_batches: number
    completed_batches: number
    failed_batches: number
    processing_batches: number
    recent_batches: number
  }
  task_stats: {
    total_tasks: number
    pending_tasks: number
    running_tasks: number
    completed_tasks: number
    failed_tasks: number
    recent_tasks: number
  }
  notification_stats: {
    total_notifications: number
    unread_notifications: number
    recent_notifications: number
  }
  rule_stats: {
    total_rules: number
    active_rules: number
    personal_rules: number
    global_rules: number
  }
  distributions: {
    status: Array<{ status: string; count: number }>
    priority: Array<{ priority: string; count: number }>
    project: Array<{ project_key: string; count: number }>
    device: Array<{ device_model: string; count: number }>
  }
  trends: {
    daily: Array<{ date: string; defects: number }>
  }
  summary: {
    total_records: number
    success_rate: number
    classification_rate: number
  }
  user_stats?: {
    total_users: number
    active_users: number
    admin_users: number
    recent_logins: number
  }
}

export const dashboardApi = {
  // 获取仪表板统计数据
  getStats(): Promise<{ data: DashboardStats }> {
    return request({
      url: '/dashboard/stats/',
      method: 'get'
    })
  }
}
