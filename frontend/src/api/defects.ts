import request from '@/utils/request'
import type { 
  Defect, 
  DefectFilters, 
  PaginationParams, 
  DefectListResponse,
  ImportBatch,
  ClassificationRule,
  FieldMapping,
  ValidationResult
} from '@/types/defect'

export const defectApi = {
  // 获取缺陷列表
  getDefects(params: DefectFilters & PaginationParams) {
    return request.get<DefectListResponse>('/defects/records/', { params })
  },

  // 获取单个缺陷
  getDefectById(id: string) {
    return request.get<Defect>(`/defects/records/${id}/`)
  },

  // 更新缺陷
  updateDefect(id: string, data: Partial<Defect>) {
    return request.put<Defect>(`/defects/records/${id}/`, data)
  },

  // 删除缺陷
  deleteDefect(id: string) {
    return request.delete(`/defects/records/${id}/`)
  },

  // 导入缺陷数据
  importDefects(formData: FormData) {
    return request.post('/defects/records/import_data/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 验证CSV表头
  validateHeaders(formData: FormData) {
    return request.post<ValidationResult>('/defects/records/validate_headers/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出缺陷数据
  exportDefects(filters: DefectFilters, format: 'excel' | 'csv' = 'excel') {
    return request.get('/defects/records/export_data/', {
      params: { ...filters, format },
      responseType: 'blob'
    })
  },

  // 批量分类
  batchClassify(defectIds: string[]) {
    return request.post('/defects/records/batch_classify/', { defect_ids: defectIds })
  },

  // 获取支持的字段列表
  getSupportedFields() {
    return request.get('/defects/records/supported_fields/')
  },

  // 获取导入批次列表
  getBatches(params?: any) {
    return request.get<ImportBatch[]>('/defects/batches/', { params })
  },

  // 获取批次详情
  getBatchById(id: string) {
    return request.get<ImportBatch>(`/defects/batches/${id}/`)
  },

  // 获取批次下的缺陷数据
  getBatchDefects(batchId: string, params?: PaginationParams) {
    return request.get<DefectListResponse>(`/defects/batches/${batchId}/defects/`, { params })
  },

  // 导出批次数据
  exportBatchData(batchId: string, format: 'excel' | 'csv' = 'excel') {
    return request.get(`/defects/batches/${batchId}/export/`, {
      params: { format },
      responseType: 'blob'
    })
  },

  // 获取分类规则
  getRules(params?: any) {
    return request.get<ClassificationRule[]>('/defects/rules/', { params })
  },

  // 创建分类规则
  createRule(data: Partial<ClassificationRule>) {
    return request.post<ClassificationRule>('/defects/rules/', data)
  },

  // 更新分类规则
  updateRule(id: string, data: Partial<ClassificationRule>) {
    return request.put<ClassificationRule>(`/defects/rules/${id}/`, data)
  },

  // 删除分类规则
  deleteRule(id: string) {
    return request.delete(`/defects/rules/${id}/`)
  },

  // 测试分类规则
  testRule(pattern: string, testText: string) {
    return request.post('/defects/rules/test_rule/', {
      pattern,
      test_text: testText
    })
  },

  // 获取字段映射
  getFieldMappings(params?: any) {
    return request.get<FieldMapping[]>('/defects/field-mappings/', { params })
  },

  // 创建字段映射
  createFieldMapping(data: Partial<FieldMapping>) {
    return request.post<FieldMapping>('/defects/field-mappings/', data)
  },

  // 更新字段映射
  updateFieldMapping(id: string, data: Partial<FieldMapping>) {
    return request.put<FieldMapping>(`/defects/field-mappings/${id}/`, data)
  },

  // 删除字段映射
  deleteFieldMapping(id: string) {
    return request.delete(`/defects/field-mappings/${id}/`)
  }
}
