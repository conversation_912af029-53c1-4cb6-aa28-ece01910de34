import request from '@/utils/request'

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
    is_staff: boolean
    is_active: boolean
    date_joined: string
    last_login: string
  }
}

export interface UserInfo {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  is_staff: boolean
  is_active: boolean
  profile?: {
    role: string
    can_view_all_data: boolean
    can_manage_rules: boolean
    can_export_data: boolean
  }
}

export const authApi = {
  // 用户登录
  login(data: LoginRequest): Promise<{ data: LoginResponse }> {
    return request({
      url: '/auth/login/',
      method: 'post',
      data
    })
  },

  // 用户登出
  logout(): Promise<void> {
    return request({
      url: '/auth/logout/',
      method: 'post'
    })
  },

  // 获取用户信息
  getUserInfo(): Promise<{ data: UserInfo }> {
    return request({
      url: '/auth/user/',
      method: 'get'
    })
  },

  // 刷新Token
  refreshToken(refresh: string): Promise<{ data: { access: string } }> {
    return request({
      url: '/auth/refresh/',
      method: 'post',
      data: { refresh }
    })
  }
}
