import request from '@/utils/request'
import type { Task, TaskFilters, TaskSubmitResult } from '@/types/task'

export const taskApi = {
  // 获取任务列表
  getTasks(filters?: TaskFilters) {
    return request.get<Task[]>('/tasks/', { params: filters })
  },

  // 获取单个任务
  getTaskById(taskId: string) {
    return request.get<Task>(`/tasks/${taskId}/`)
  },

  // 取消任务
  cancelTask(taskId: string) {
    return request.delete(`/tasks/${taskId}/`)
  },

  // 获取任务进度
  getTaskProgress(taskId: string) {
    return request.get(`/tasks/${taskId}/progress/`)
  },

  // 获取队列状态
  getQueueStatus() {
    return request.get('/tasks/queue-status/')
  },

  // 提交任务
  submitTask(taskData: any) {
    return request.post<TaskSubmitResult>('/tasks/submit/', taskData)
  }
}
