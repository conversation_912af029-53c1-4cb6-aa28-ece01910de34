export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  is_staff: boolean
  is_superuser: boolean
  date_joined: string
  last_login?: string
}

export interface UserProfile {
  user: User
  role: 'admin' | 'user'
  can_view_all_data: boolean
  can_manage_rules: boolean
  can_export_data: boolean
  max_import_size: number
  max_records_per_import: number
  max_personal_rules: number
  max_concurrent_tasks: number
  
  // 统计信息
  total_imports: number
  total_records: number
  last_import_at?: string
}

export interface Permission {
  id: string
  name: string
  codename: string
  content_type: string
}

export interface Notification {
  id: string
  notification_type: string
  title: string
  message: string
  data: Record<string, any>
  is_read: boolean
  created_at: string
  read_at?: string
}
