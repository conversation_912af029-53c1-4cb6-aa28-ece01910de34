export interface Defect {
  id: string
  issue_key: string
  issue_id?: string
  summary: string
  issue_type?: string
  status: string
  priority: string
  resolution?: string
  
  // 项目相关
  project_key?: string
  project_name?: string
  project_type?: string
  project_lead?: string
  
  // 人员相关
  assignee?: string
  reporter?: string
  creator?: string
  rd_owner?: string
  
  // 时间相关
  created?: string
  updated?: string
  resolved?: string
  due_date?: string
  
  // 版本和组件
  affects_versions?: string
  components?: string
  
  // 描述
  description?: string
  environment?: string
  
  // 自定义字段
  device_model?: string
  bug_category_custom?: string
  common_issue?: string
  root_cause?: string
  solution?: string
  
  // 分析结果
  project_extracted?: string
  summary_text?: string
  bug_category?: string
  commonality?: string
  
  // 系统字段
  import_batch_id: string
  import_timestamp: string
  uploaded_by: string
  created_at: string
  updated_at: string
}

export interface DefectFilters {
  batchId?: string
  status?: string
  priority?: string
  assignee?: string
  project?: string
  keyword?: string
  dateRange?: [string, string]
}

export interface PaginationParams {
  page: number
  size: number
}

export interface DefectListResponse {
  results: Defect[]
  count: number
  next?: string
  previous?: string
}

export interface ImportBatch {
  id: string
  batch_id: string
  batch_name: string
  import_timestamp: string
  uploaded_by: string
  uploaded_by_name: string
  
  // 统计信息
  total_records: number
  success_records: number
  failed_records: number
  processed_records: number
  
  // 文件信息
  data_source: string
  source_file_name: string
  source_file_size: number
  
  // 状态信息
  status: 'uploaded' | 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress_percentage: number
  current_step: string
  
  // 时间信息
  processing_started_at?: string
  processing_completed_at?: string
  
  // 错误信息
  error_message?: string
  error_details?: Record<string, any>
  
  task_id?: string
}

export interface ClassificationRule {
  id: string
  rule_type: 'project' | 'category' | 'commonality'
  rule_name: string
  pattern: string
  target_value: string
  priority: number
  is_active: boolean
  description?: string
  scope: 'global' | 'personal'
  created_by: string
  created_by_name: string
  can_edit: boolean
  created_at: string
  updated_at: string
}

export interface FieldMapping {
  id: string
  csv_field_name: string
  db_field_name: string
  field_type: 'string' | 'text' | 'integer' | 'float' | 'datetime' | 'date' | 'boolean' | 'json'
  is_required: boolean
  is_core_field: boolean
  default_value?: string
  date_format?: string
  max_length?: number
  validation_regex?: string
  description?: string
  example_value?: string
}

export interface ValidationResult {
  valid: boolean
  missing_required_fields: string[]
  unknown_fields: string[]
  supported_fields: string[]
  total_supported: number
  total_fields: number
}
