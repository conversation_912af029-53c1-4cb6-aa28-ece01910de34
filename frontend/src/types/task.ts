export interface Task {
  id: string
  task_id: string
  task_type: 'data_import' | 'data_analysis' | 'rule_import' | 'batch_export'
  task_name: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  current_step: string
  
  // 队列信息
  queue_position: number
  estimated_wait_time: number
  priority: number
  
  // 时间信息
  created_at: string
  started_at?: string
  completed_at?: string
  estimated_duration: number
  
  // 任务参数和结果
  task_params: Record<string, any>
  result_data: Record<string, any>
  error_message?: string
  
  // 关联信息
  created_by: string
  related_batch_id?: string
}

export interface TaskFilters {
  status?: string
  task_type?: string
  created_by?: string
}

export interface TaskStats {
  total: number
  running: number
  pending: number
  completed: number
  failed: number
}

export interface TaskSubmitResult {
  task_id: string
  batch_id: string
  queue_position: number
  estimated_wait_time: number
}
