<template>
  <el-dialog
    v-model="visible"
    title="缺陷详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="defect-detail" v-if="defect">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="Issue Key">
          {{ defect.issue_key }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(defect.status)">
            {{ defect.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <el-tag :type="getPriorityType(defect.priority)">
            {{ defect.priority }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="指派人">
          {{ defect.assignee || '未指派' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目">
          {{ defect.project_key }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(defect.created) }}
        </el-descriptions-item>
        <el-descriptions-item label="设备型号">
          {{ defect.device_model || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="缺陷分类">
          {{ defect.bug_category || '未分类' }}
        </el-descriptions-item>
        <el-descriptions-item label="共性问题" :span="2">
          {{ defect.commonality || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="摘要" :span="2">
          {{ defect.summary }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          <div class="description">
            {{ defect.description || '无描述' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Defect {
  id: number
  issue_key: string
  summary: string
  description?: string
  status: string
  priority: string
  assignee?: string
  project_key: string
  created: string
  device_model?: string
  bug_category?: string
  commonality?: string
}

interface Props {
  modelValue: boolean
  defect: Defect | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', defect: Defect): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  if (props.defect) {
    emit('edit', props.defect)
  }
  handleClose()
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'Open': 'danger',
    'In Progress': 'warning',
    'Resolved': 'success',
    'Closed': 'info'
  }
  return statusMap[status] || 'info'
}

const getPriorityType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success'
  }
  return priorityMap[priority] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.defect-detail {
  max-height: 500px;
  overflow-y: auto;
}

.description {
  max-height: 100px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
