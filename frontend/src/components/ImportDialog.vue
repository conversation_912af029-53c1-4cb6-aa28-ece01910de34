<template>
  <el-dialog
    v-model="visible"
    title="数据导入"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="import-dialog">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center>
        <el-step title="选择文件" />
        <el-step title="字段验证" />
        <el-step title="确认导入" />
        <el-step title="导入完成" />
      </el-steps>

      <!-- 步骤1: 文件选择 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          :accept="'.xlsx,.xls,.csv'"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .xlsx, .xls, .csv 格式，文件大小不超过50MB
            </div>
          </template>
        </el-upload>

        <el-form :model="importForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="批次名称">
            <el-input
              v-model="importForm.batchName"
              placeholder="可选，默认使用文件名"
            />
          </el-form-item>
        </el-form>

        <div class="template-download">
          <el-button @click="downloadTemplate('excel')">
            <el-icon><Download /></el-icon>
            下载Excel模板
          </el-button>
          <el-button @click="downloadTemplate('csv')">
            <el-icon><Download /></el-icon>
            下载CSV模板
          </el-button>
        </div>
      </div>

      <!-- 步骤2: 字段验证 -->
      <div v-if="currentStep === 1" class="step-content">
        <div v-loading="validating" class="field-validation">
          <el-alert
            v-if="validationResult"
            :title="getValidationTitle()"
            :type="validationResult.valid ? 'success' : 'warning'"
            :closable="false"
            show-icon
          />

          <div v-if="validationResult" class="validation-details">
            <!-- 支持的字段 -->
            <el-card header="✅ 支持的字段" class="field-card">
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.supported_fields"
                  :key="field"
                  type="success"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
            </el-card>

            <!-- 未知字段 -->
            <el-card
              v-if="validationResult.unknown_fields?.length"
              header="⚠️ 未知字段"
              class="field-card"
            >
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.unknown_fields"
                  :key="field"
                  type="warning"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
              <p class="field-note">这些字段将被忽略</p>
            </el-card>

            <!-- 缺失字段 -->
            <el-card
              v-if="validationResult.missing_required_fields?.length"
              header="❌ 缺失必需字段"
              class="field-card"
            >
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.missing_required_fields"
                  :key="field"
                  type="danger"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
              <p class="field-note">必须包含这些字段才能导入</p>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 步骤3: 确认导入 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-descriptions title="导入信息确认" :column="2">
          <el-descriptions-item label="文件名">
            {{ selectedFile?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(selectedFile?.size || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="批次名称">
            {{ importForm.batchName || selectedFile?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="支持字段数">
            {{ validationResult?.total_supported || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <el-alert
          title="确认导入后，系统将异步处理数据，您可以在任务管理页面查看进度"
          type="info"
          :closable="false"
        />
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <el-result
          icon="success"
          title="导入任务已提交"
          :sub-title="`任务ID: ${taskResult?.task_id}`"
        >
          <template #extra>
            <el-descriptions :column="2">
              <el-descriptions-item label="批次ID">
                {{ taskResult?.batch_id }}
              </el-descriptions-item>
              <el-descriptions-item label="队列位置">
                {{ taskResult?.queue_position }}
              </el-descriptions-item>
              <el-descriptions-item label="预估等待时间">
                {{ formatWaitTime(taskResult?.estimated_wait_time || 0) }}
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          v-if="currentStep > 0 && currentStep < 3"
          @click="prevStep"
        >
          上一步
        </el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!selectedFile"
          @click="validateFields"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="!validationResult?.valid"
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2"
          type="primary"
          :loading="importing"
          @click="submitImport"
        >
          确认导入
        </el-button>
        <el-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleComplete"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Download } from '@element-plus/icons-vue'
import { useFileUpload } from '@/composables/useFileUpload'
import type { UploadFile } from 'element-plus'
import type { ValidationResult } from '@/types/defect'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', result: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { validateFile, submitImportTask, downloadTemplate, formatFileSize, formatWaitTime } = useFileUpload()

// 响应式数据
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const validating = ref(false)
const importing = ref(false)
const validationResult = ref<ValidationResult | null>(null)
const taskResult = ref<any>(null)

const importForm = reactive({
  batchName: ''
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null
  importForm.batchName = file.name?.replace(/\.[^/.]+$/, '') || ''
}

const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

const validateFields = async () => {
  if (!selectedFile.value) return

  validating.value = true
  try {
    validationResult.value = await validateFile(selectedFile.value)
    currentStep.value = 1
  } catch (error) {
    ElMessage.error('字段验证失败')
  } finally {
    validating.value = false
  }
}

const submitImport = async () => {
  if (!selectedFile.value) return

  importing.value = true
  try {
    taskResult.value = await submitImportTask(
      selectedFile.value,
      importForm.batchName
    )
    currentStep.value = 3
    emit('success', taskResult.value)
  } catch (error) {
    ElMessage.error('提交导入任务失败')
  } finally {
    importing.value = false
  }
}

const nextStep = () => {
  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const handleCancel = () => {
  resetDialog()
  visible.value = false
}

const handleComplete = () => {
  resetDialog()
  visible.value = false
}

const resetDialog = () => {
  currentStep.value = 0
  selectedFile.value = null
  validationResult.value = null
  taskResult.value = null
  importForm.batchName = ''
}

// 工具方法
const getValidationTitle = () => {
  if (!validationResult.value) return ''
  
  if (validationResult.value.valid) {
    return `字段验证通过 (${validationResult.value.total_supported}/${validationResult.value.total_fields})`
  } else {
    return `字段验证失败，缺少必需字段`
  }
}
</script>

<style scoped>
.import-dialog {
  min-height: 400px;
}

.step-content {
  margin-top: 30px;
  min-height: 300px;
}

.template-download {
  margin-top: 20px;
  text-align: center;
}

.template-download .el-button {
  margin: 0 10px;
}

.field-validation {
  margin-top: 20px;
}

.validation-details {
  margin-top: 20px;
}

.field-card {
  margin-bottom: 15px;
}

.field-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.field-tag {
  margin: 2px;
}

.field-note {
  margin-top: 10px;
  color: #666;
  font-size: 12px;
}
</style>
