<template>
  <div id="app">
    <el-container>
      <!-- 顶部导航 -->
      <el-header>
        <div class="header-content">
          <div class="logo">
            <h2>缺陷分类工具</h2>
          </div>
          <div class="nav-menu">
            <el-menu
              :default-active="activeIndex"
              mode="horizontal"
              @select="handleSelect"
            >
              <el-menu-item index="/defects">缺陷管理</el-menu-item>
              <el-menu-item index="/tasks">任务管理</el-menu-item>
              <el-menu-item index="/batches">批次管理</el-menu-item>
              <el-menu-item index="/rules">分类规则</el-menu-item>
              <el-menu-item index="/analysis">数据分析</el-menu-item>
            </el-menu>
          </div>
          <div class="user-info">
            <el-dropdown>
              <span class="el-dropdown-link">
                用户名
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人设置</el-dropdown-item>
                  <el-dropdown-item divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main>
        <router-view />
      </el-main>
    </el-container>

    <!-- 全局通知 -->
    <NotificationCenter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowDown } from '@element-plus/icons-vue'
import { useWebSocket } from '@/composables/useWebSocket'
import NotificationCenter from '@/components/NotificationCenter.vue'

const router = useRouter()
const route = useRoute()
const { connect } = useWebSocket()

const activeIndex = ref('/')

const handleSelect = (key: string) => {
  router.push(key)
}

onMounted(() => {
  // 建立WebSocket连接
  connect()
  
  // 设置当前激活的菜单项
  activeIndex.value = route.path
})
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

.el-header {
  background-color: #545c64;
  color: #fff;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.logo {
  margin-right: 50px;
}

.logo h2 {
  margin: 0;
  color: #fff;
}

.nav-menu {
  flex: 1;
}

.nav-menu .el-menu {
  background-color: transparent;
  border-bottom: none;
}

.nav-menu .el-menu-item {
  color: #fff;
  border-bottom: 2px solid transparent;
}

.nav-menu .el-menu-item:hover,
.nav-menu .el-menu-item.is-active {
  background-color: #434a50;
  border-bottom-color: #409eff;
}

.user-info {
  color: #fff;
}

.el-dropdown-link {
  cursor: pointer;
  color: #fff;
  display: flex;
  align-items: center;
}

.el-main {
  padding: 0;
  background-color: #f0f2f5;
}
</style>
