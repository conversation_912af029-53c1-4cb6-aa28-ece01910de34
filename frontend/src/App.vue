<template>
  <div id="app">
    <!-- 主布局 -->
    <div v-if="showLayout" class="app-layout">
      <!-- 顶部导航栏 -->
      <el-header class="app-header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleSidebar"
            class="sidebar-toggle"
          >
            <el-icon><Menu /></el-icon>
          </el-button>
          <router-link to="/" class="app-logo">
            <el-icon :size="24" color="#409EFF"><Tools /></el-icon>
            <span class="logo-text">缺陷分类工具</span>
          </router-link>
        </div>

        <div class="header-right">
          <!-- 通知 -->
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-button type="text" @click="showNotifications">
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <span class="user-dropdown">
              <el-avatar :size="32" :src="userAvatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ currentUser?.username || '用户' }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>个人设置
                </el-dropdown-item>
                <el-dropdown-item command="admin" v-if="isAdmin">
                  <el-icon><Setting /></el-icon>系统管理
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-container class="app-container">
        <!-- 侧边栏 -->
        <el-aside :width="sidebarWidth" class="app-sidebar">
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapse"
            :unique-opened="true"
            router
            class="sidebar-menu"
          >
            <el-menu-item index="/dashboard">
              <el-icon><Monitor /></el-icon>
              <template #title>仪表板</template>
            </el-menu-item>

            <el-menu-item index="/defects">
              <el-icon><Tools /></el-icon>
              <template #title>缺陷管理</template>
            </el-menu-item>

            <el-menu-item index="/import">
              <el-icon><Upload /></el-icon>
              <template #title>数据导入</template>
            </el-menu-item>

            <el-menu-item index="/rules">
              <el-icon><Setting /></el-icon>
              <template #title>分类规则</template>
            </el-menu-item>

            <el-menu-item index="/tasks">
              <el-icon><Management /></el-icon>
              <template #title>任务管理</template>
            </el-menu-item>

            <el-menu-item index="/notifications">
              <el-icon><Bell /></el-icon>
              <template #title>通知中心</template>
            </el-menu-item>

            <el-sub-menu index="admin" v-if="isAdmin">
              <template #title>
                <el-icon><UserFilled /></el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/users">用户管理</el-menu-item>
              <el-menu-item index="/logs">操作日志</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="app-main">
          <router-view />
        </el-main>
      </el-container>
    </div>

    <!-- 无布局页面 -->
    <router-view v-else />

    <!-- 全局通知 -->
    <NotificationCenter v-if="showLayout" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Menu, Bell, User, ArrowDown, Setting, SwitchButton,
  Monitor, Upload, Management, UserFilled, Tools
} from '@element-plus/icons-vue'
import { useWebSocket } from '@/composables/useWebSocket'
import NotificationCenter from '@/components/NotificationCenter.vue'

const route = useRoute()
const router = useRouter()
const { connect } = useWebSocket()

// 侧边栏状态
const isCollapse = ref(false)
const unreadCount = ref(0)

// 用户信息
const currentUser = ref<any>(null)
const userAvatar = ref('')

// 计算属性
const showLayout = computed(() => {
  // 不显示布局的页面
  const noLayoutPages = ['/', '/login']
  return !noLayoutPages.includes(route.path)
})

const sidebarWidth = computed(() => {
  return isCollapse.value ? '64px' : '200px'
})

const activeMenu = computed(() => {
  return route.path
})

const isAdmin = computed(() => {
  return currentUser.value?.is_staff || false
})

// 方法
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const showNotifications = () => {
  router.push('/notifications')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人设置
      break
    case 'admin':
      window.open('/admin/', '_blank')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('userRole')

    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消
  }
}

// 初始化用户信息
const initUserInfo = () => {
  const userStr = localStorage.getItem('user')
  if (userStr) {
    currentUser.value = JSON.parse(userStr)
  }
}

onMounted(() => {
  // 建立WebSocket连接
  if (showLayout.value) {
    connect()
  }

  initUserInfo()
})
</script>

<style>
#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
}

.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px !important;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sidebar-toggle {
  font-size: 18px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #333;
  font-weight: 600;
  font-size: 16px;
}

.logo-text {
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
}

.app-container {
  flex: 1;
  height: calc(100vh - 60px);
}

.app-sidebar {
  background: white;
  border-right: 1px solid #e6e6e6;
  transition: width 0.3s;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.app-main {
  background: #f5f5f5;
  padding: 0;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s;
  }

  .app-sidebar.show {
    transform: translateX(0);
  }

  .app-main {
    margin-left: 0 !important;
  }
}
</style>
