import { createRouter, createWebHistory } from 'vue-router'
import DefectManagement from '@/views/DefectManagement.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/defects'
    },
    {
      path: '/defects',
      name: 'DefectManagement',
      component: DefectManagement,
      meta: {
        title: '缺陷管理'
      }
    },
    {
      path: '/tasks',
      name: 'TaskManagement',
      component: () => import('@/views/TaskManagement.vue'),
      meta: {
        title: '任务管理'
      }
    },
    {
      path: '/batches',
      name: 'BatchManagement',
      component: () => import('@/views/BatchManagement.vue'),
      meta: {
        title: '批次管理'
      }
    },
    {
      path: '/rules',
      name: 'RuleManagement',
      component: () => import('@/views/RuleManagement.vue'),
      meta: {
        title: '分类规则'
      }
    },
    {
      path: '/analysis',
      name: 'AnalysisDashboard',
      component: () => import('@/views/AnalysisDashboard.vue'),
      meta: {
        title: '数据分析'
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 缺陷分类工具`
  }
  next()
})

export default router
