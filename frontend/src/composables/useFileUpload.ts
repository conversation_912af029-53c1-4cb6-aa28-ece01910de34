import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { defectApi } from '@/api/defects'

export function useFileUpload() {
  const uploading = ref(false)
  const validating = ref(false)

  const validateFile = async (file: File) => {
    if (!file) {
      throw new Error('请选择文件')
    }

    // 文件大小检查
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过50MB')
    }

    // 文件类型检查
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('只支持Excel和CSV文件')
    }

    validating.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await defectApi.validateHeaders(formData)
      return response.data
    } catch (error) {
      console.error('字段验证失败:', error)
      throw error
    } finally {
      validating.value = false
    }
  }

  const submitImportTask = async (file: File, batchName?: string) => {
    uploading.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)
      if (batchName) {
        formData.append('batch_name', batchName)
      }

      const response = await defectApi.importDefects(formData)
      return response.data
    } catch (error) {
      console.error('提交导入任务失败:', error)
      throw error
    } finally {
      uploading.value = false
    }
  }

  const downloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {
    try {
      // 这里应该调用下载模板的API
      // const response = await defectApi.downloadTemplate(format)
      
      // 临时实现：创建一个示例模板
      const headers = [
        'Issue key', 'Summary', 'Status', 'Priority', 'Assignee',
        'Created', 'Project key', 'Issue Type', 'Description'
      ]
      
      let content = ''
      if (format === 'csv') {
        content = headers.join(',') + '\n'
        content += 'DEMO-001,示例缺陷,Open,Major,张三,2024-01-01,PROJECT1,Bug,这是一个示例缺陷\n'
      } else {
        // 对于Excel，这里简化处理，实际应该生成真正的Excel文件
        content = headers.join(',') + '\n'
        content += 'DEMO-001,示例缺陷,Open,Major,张三,2024-01-01,PROJECT1,Bug,这是一个示例缺陷\n'
      }
      
      const blob = new Blob([content], {
        type: format === 'excel' 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `defect_import_template.${format === 'excel' ? 'xlsx' : 'csv'}`
      link.click()
      
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
    }
  }

  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
    return `${(size / (1024 * 1024)).toFixed(1)} MB`
  }

  const formatWaitTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.ceil(seconds / 60)}分钟`
    return `${Math.ceil(seconds / 3600)}小时`
  }

  return {
    uploading,
    validating,
    validateFile,
    submitImportTask,
    downloadTemplate,
    formatFileSize,
    formatWaitTime
  }
}
