import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTaskStore } from '@/stores/tasks'
import { useNotificationStore } from '@/stores/notifications'

export function useWebSocket() {
  const socket = ref<WebSocket | null>(null)
  const connected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5

  const taskStore = useTaskStore()
  const notificationStore = useNotificationStore()

  const connect = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`

    socket.value = new WebSocket(wsUrl)

    socket.value.onopen = () => {
      console.log('WebSocket连接已建立')
      connected.value = true
      reconnectAttempts.value = 0
    }

    socket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    socket.value.onclose = () => {
      console.log('WebSocket连接已关闭')
      connected.value = false
      attemptReconnect()
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    connected.value = false
  }

  const handleMessage = (data: any) => {
    switch (data.type) {
      case 'notification':
        handleNotification(data.data)
        break
      case 'progress':
        handleProgressUpdate(data.data)
        break
      case 'task_status':
        handleTaskStatusUpdate(data.data)
        break
      case 'connection_established':
        console.log('WebSocket连接确认:', data.message)
        break
      case 'pong':
        // 心跳响应
        break
      default:
        console.log('未知消息类型:', data.type)
    }
  }

  const handleNotification = (notification: any) => {
    // 添加到通知存储
    notificationStore.addNotification(notification)

    // 显示Toast通知
    const messageType = getMessageType(notification.notification_type)
    ElMessage({
      type: messageType,
      title: notification.title,
      message: notification.message,
      duration: 5000,
      showClose: true
    })

    // 处理特定类型的通知
    if (notification.notification_type === 'import_completed') {
      handleImportCompleted(notification)
    } else if (notification.notification_type === 'import_failed') {
      handleImportFailed(notification)
    }
  }

  const handleProgressUpdate = (progress: any) => {
    taskStore.updateTaskProgress(
      progress.task_id,
      progress.progress,
      progress.step_description
    )
  }

  const handleTaskStatusUpdate = (taskUpdate: any) => {
    taskStore.updateTask(taskUpdate.task_id, {
      status: taskUpdate.status,
      result_data: taskUpdate.result
    })
  }

  const handleImportCompleted = (notification: any) => {
    // 可以触发数据刷新等操作
    console.log('导入完成:', notification.data)
  }

  const handleImportFailed = (notification: any) => {
    console.error('导入失败:', notification.data)
  }

  const getMessageType = (notificationType: string) => {
    const typeMap: Record<string, string> = {
      'import_completed': 'success',
      'import_failed': 'error',
      'import_started': 'info',
      'task_queued': 'info',
      'task_cancelled': 'warning'
    }
    return typeMap[notificationType] || 'info'
  }

  const attemptReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts) {
      reconnectAttempts.value++
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000)
      
      setTimeout(() => {
        console.log(`尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)
        connect()
      }, delay)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
      ElMessage.error('网络连接断开，请刷新页面重试')
    }
  }

  const sendMessage = (message: any) => {
    if (socket.value && connected.value) {
      socket.value.send(JSON.stringify(message))
    }
  }

  const sendPing = () => {
    sendMessage({
      type: 'ping',
      timestamp: Date.now()
    })
  }

  const markNotificationRead = (notificationId: string) => {
    sendMessage({
      type: 'mark_notification_read',
      notification_id: notificationId
    })
  }

  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect()
  })

  return {
    connected,
    connect,
    disconnect,
    reconnectAttempts,
    sendMessage,
    sendPing,
    markNotificationRead
  }
}
