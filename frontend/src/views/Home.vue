<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-card">
        <div class="logo">
          <el-icon :size="80" color="#409EFF">
            <Tools />
          </el-icon>
        </div>
        <h1 class="welcome-title">缺陷分类工具</h1>
        <p class="welcome-subtitle">
          智能化缺陷管理与分析平台，提升测试效率，优化质量管控
        </p>
        
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="large" 
            @click="goToDashboard"
            :icon="Monitor"
          >
            进入仪表板
          </el-button>
          <el-button 
            size="large" 
            @click="goToLogin"
            :icon="User"
            v-if="!isAuthenticated"
          >
            用户登录
          </el-button>
          <el-button 
            size="large" 
            @click="goToAdmin"
            :icon="Setting"
          >
            系统管理
          </el-button>
        </div>
      </div>
    </div>

    <!-- 功能特性 -->
    <div class="features-section">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">
              <el-icon :size="40" :color="feature.color">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section" v-if="isAuthenticated">
      <div class="container">
        <h2 class="section-title">数据概览</h2>
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in stats" :key="stat.label">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <el-icon :size="24" color="white">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer-section">
      <div class="container">
        <p class="footer-text">
          <el-icon><Code /></el-icon>
          基于 Django + Vue3 + Element Plus 构建
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Tools, Monitor, User, Setting, Upload,
  Management, Bell,
  Document, Warning, Check, TrendCharts
} from '@element-plus/icons-vue'
import { dashboardApi } from '@/api/dashboard'

const router = useRouter()

// 认证状态
const isAuthenticated = computed(() => {
  return localStorage.getItem('token') !== null
})

// 统计数据
const stats = ref([
  { label: '总缺陷数', value: 0, icon: Tools, color: '#409EFF' },
  { label: '待处理', value: 0, icon: Warning, color: '#E6A23C' },
  { label: '已解决', value: 0, icon: Check, color: '#67C23A' },
  { label: '导入批次', value: 0, icon: Upload, color: '#909399' }
])

// 功能特性
const features = [
  {
    id: 1,
    title: '数据导入',
    description: '支持CSV、Excel文件导入，智能字段映射，批量数据处理',
    icon: Upload,
    color: '#409EFF'
  },
  {
    id: 2,
    title: '智能分类',
    description: '基于规则引擎的自动分类，支持自定义规则，提升分类准确性',
    icon: Setting,
    color: '#67C23A'
  },
  {
    id: 3,
    title: '数据分析',
    description: '多维度统计分析，趋势图表展示，支持数据导出',
    icon: TrendCharts,
    color: '#E6A23C'
  },
  {
    id: 4,
    title: '多用户管理',
    description: '支持多用户并发，权限分级管理，数据安全隔离',
    icon: User,
    color: '#F56C6C'
  },
  {
    id: 5,
    title: '任务队列',
    description: '异步任务处理，进度实时跟踪，支持任务取消和重试',
    icon: Management,
    color: '#909399'
  },
  {
    id: 6,
    title: '实时通知',
    description: 'WebSocket实时通知，任务状态推送，操作结果反馈',
    icon: Bell,
    color: '#9C27B0'
  }
]

// 页面方法
const goToDashboard = () => {
  if (isAuthenticated.value) {
    router.push('/dashboard')
  } else {
    router.push('/login')
  }
}

const goToLogin = () => {
  router.push('/login')
}

const goToAdmin = () => {
  window.open('/admin/', '_blank')
}

// 获取统计数据
const fetchStats = async () => {
  if (!isAuthenticated.value) return
  
  try {
    const response = await dashboardApi.getStats()
    const data = response.data
    
    stats.value[0].value = data.defect_stats?.total_defects || 0
    stats.value[1].value = data.defect_stats?.open_defects || 0
    stats.value[2].value = data.defect_stats?.resolved_defects || 0
    stats.value[3].value = data.batch_stats?.total_batches || 0
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.welcome-section {
  padding: 80px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  padding: 60px;
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.logo {
  margin-bottom: 30px;
}

.welcome-title {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.6;
}

.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.features-section {
  padding: 80px 20px;
  background: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.feature-card {
  background: white;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

.stats-section {
  padding: 80px 20px;
  background: #f8f9fa;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  display: flex;
  align-items: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}

.footer-section {
  padding: 40px 20px;
  background: rgba(0,0,0,0.1);
}

.footer-text {
  text-align: center;
  color: rgba(255,255,255,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

@media (max-width: 768px) {
  .welcome-card {
    padding: 40px 30px;
  }
  
  .welcome-title {
    font-size: 2rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
