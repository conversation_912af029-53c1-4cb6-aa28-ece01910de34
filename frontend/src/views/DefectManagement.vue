<template>
  <div class="defect-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>缺陷数据管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          导入数据
        </el-button>
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-form :model="filters" inline>
        <el-form-item label="批次">
          <el-select v-model="filters.batchId" placeholder="选择批次" clearable>
            <el-option
              v-for="batch in batches"
              :key="batch.id"
              :label="batch.batch_name"
              :value="batch.batch_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="打开" value="Open" />
            <el-option label="已解决" value="Resolved" />
            <el-option label="已关闭" value="Closed" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="filters.priority" placeholder="选择优先级" clearable>
            <el-option label="Blocker" value="Blocker" />
            <el-option label="Critical" value="Critical" />
            <el-option label="Major" value="Major" />
            <el-option label="Minor" value="Minor" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词">
          <el-input 
            v-model="filters.keyword" 
            placeholder="搜索缺陷编号或摘要"
            clearable
            @keyup.enter="loadDefects"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadDefects">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedDefects.length > 0" class="batch-actions">
      <el-alert
        :title="`已选择 ${selectedDefects.length} 条记录`"
        type="info"
        :closable="false"
      />
      <div class="actions">
        <el-button @click="batchClassify">批量分类</el-button>
        <el-button type="danger" @click="batchDelete">批量删除</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="defects"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="issue_key" label="缺陷编号" width="120" />
      <el-table-column prop="summary" label="摘要" min-width="200" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="{ row }">
          <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="指派人" width="120" />
      <el-table-column prop="created" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created) }}
        </template>
      </el-table-column>
      <el-table-column prop="bug_category" label="缺陷分类" width="120" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
          <el-button size="small" type="primary" @click="editDefect(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="loadDefects"
      @current-change="loadDefects"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="showImportDialog"
      @success="handleImportSuccess"
    />

    <!-- 缺陷详情对话框 -->
    <DefectDetailDialog
      v-model:visible="showDetailDialog"
      :defect="selectedDefect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'
import { useDefectStore } from '@/stores/defects'
import { defectApi } from '@/api/defects'
import type { Defect, DefectFilters, ImportBatch } from '@/types/defect'
import ImportDialog from '@/components/ImportDialog.vue'
import DefectDetailDialog from '@/components/DefectDetailDialog.vue'

// 状态管理
const defectStore = useDefectStore()

// 响应式数据
const loading = ref(false)
const showImportDialog = ref(false)
const showDetailDialog = ref(false)
const selectedDefect = ref<Defect | null>(null)
const selectedDefects = ref<Defect[]>([])
const batches = ref<ImportBatch[]>([])

const filters = reactive<DefectFilters>({
  batchId: '',
  status: '',
  priority: '',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const defects = computed(() => defectStore.defects)

// 方法
const loadDefects = async () => {
  loading.value = true
  try {
    await defectStore.fetchDefects({
      ...filters,
      page: pagination.page,
      size: pagination.size
    })
    pagination.total = defectStore.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadBatches = async () => {
  try {
    const response = await defectApi.getBatches()
    batches.value = response.data
  } catch (error) {
    console.error('加载批次失败:', error)
  }
}

const resetFilters = () => {
  Object.assign(filters, {
    batchId: '',
    status: '',
    priority: '',
    keyword: ''
  })
  pagination.page = 1
  loadDefects()
}

const handleSelectionChange = (selection: Defect[]) => {
  selectedDefects.value = selection
}

const viewDetail = (defect: Defect) => {
  selectedDefect.value = defect
  showDetailDialog.value = true
}

const editDefect = (defect: Defect) => {
  // 编辑逻辑
  selectedDefect.value = defect
  showDetailDialog.value = true
}

const exportData = async () => {
  try {
    await defectStore.exportDefects(filters)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const batchClassify = async () => {
  try {
    const defectIds = selectedDefects.value.map(d => d.id)
    await defectStore.batchClassify(defectIds)
    ElMessage.success('批量分类成功')
    selectedDefects.value = []
  } catch (error) {
    ElMessage.error('批量分类失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的缺陷吗？', '确认删除', {
      type: 'warning'
    })
    
    for (const defect of selectedDefects.value) {
      await defectStore.deleteDefect(defect.id)
    }
    
    ElMessage.success('批量删除成功')
    selectedDefects.value = []
    loadDefects()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleImportSuccess = () => {
  ElMessage.success('导入任务已提交')
  loadDefects()
  loadBatches()
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'Open': 'warning',
    'In Progress': 'primary',
    'Resolved': 'success',
    'Closed': 'info'
  }
  return typeMap[status] || 'default'
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    'Blocker': 'danger',
    'Critical': 'danger',
    'Major': 'warning',
    'Minor': 'info'
  }
  return typeMap[priority] || 'default'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadDefects()
  loadBatches()
})
</script>

<style scoped>
.defect-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filters {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px;
  background: #e6f7ff;
  border-radius: 4px;
}

.batch-actions .actions {
  display: flex;
  gap: 10px;
}
</style>
