<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <h1>仪表板</h1>
      <p class="header-subtitle">数据概览与统计分析</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="card in statCards" :key="card.key">
          <div class="stat-card" :style="{ borderLeft: `4px solid ${card.color}` }">
            <div class="stat-content">
              <div class="stat-value">{{ card.value }}</div>
              <div class="stat-label">{{ card.label }}</div>
              <div class="stat-change" :class="card.trend">
                <el-icon><component :is="card.trendIcon" /></el-icon>
                {{ card.change }}
              </div>
            </div>
            <div class="stat-icon" :style="{ backgroundColor: card.color }">
              <el-icon :size="24" color="white">
                <component :is="card.icon" />
              </el-icon>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 缺陷趋势图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>缺陷趋势</span>
                <el-button link @click="refreshTrends">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div ref="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 状态分布图 -->
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>状态分布</span>
                <el-button link @click="refreshDistribution">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </template>
            <div ref="statusChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 优先级分布 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>优先级分布</span>
            </template>
            <div ref="priorityChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 项目分布 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>项目分布</span>
            </template>
            <div ref="projectChart" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 设备分布 -->
        <el-col :xs="24" :lg="8">
          <el-card class="chart-card">
            <template #header>
              <span>设备分布</span>
            </template>
            <div ref="deviceChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <span>快速操作</span>
        </template>
        <div class="action-buttons">
          <el-button type="primary" @click="goToImport" :icon="Upload">
            数据导入
          </el-button>
          <el-button type="success" @click="goToDefects" :icon="Tools">
            缺陷管理
          </el-button>
          <el-button type="warning" @click="goToRules" :icon="Setting">
            分类规则
          </el-button>
          <el-button type="info" @click="goToTasks" :icon="Management">
            任务管理
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
            <el-button link @click="refreshActivity">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
          >
            {{ activity.description }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Tools, Upload, Setting, Management, Refresh,
  TrendCharts, ArrowUp, ArrowDown, Minus
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { dashboardApi } from '@/api/dashboard'

const router = useRouter()

// 图表引用
const trendChart = ref<HTMLElement>()
const statusChart = ref<HTMLElement>()
const priorityChart = ref<HTMLElement>()
const projectChart = ref<HTMLElement>()
const deviceChart = ref<HTMLElement>()

// 统计卡片数据
const statCards = ref([
  {
    key: 'total',
    label: '总缺陷数',
    value: 0,
    change: '+0%',
    trend: 'positive',
    trendIcon: ArrowUp,
    color: '#409EFF',
    icon: Tools
  },
  {
    key: 'open',
    label: '待处理',
    value: 0,
    change: '+0%',
    trend: 'negative',
    trendIcon: ArrowUp,
    color: '#E6A23C',
    icon: TrendCharts
  },
  {
    key: 'resolved',
    label: '已解决',
    value: 0,
    change: '+0%',
    trend: 'positive',
    trendIcon: ArrowUp,
    color: '#67C23A',
    icon: TrendCharts
  },
  {
    key: 'batches',
    label: '导入批次',
    value: 0,
    change: '+0%',
    trend: 'neutral',
    trendIcon: Minus,
    color: '#909399',
    icon: Upload
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    description: '导入了新的缺陷数据批次',
    timestamp: '2024-01-15 10:30',
    type: 'primary'
  },
  {
    id: 2,
    description: '完成了100条缺陷的自动分类',
    timestamp: '2024-01-15 09:15',
    type: 'success'
  },
  {
    id: 3,
    description: '创建了新的分类规则',
    timestamp: '2024-01-15 08:45',
    type: 'warning'
  }
])

// 页面方法
const goToImport = () => router.push('/import')
const goToDefects = () => router.push('/defects')
const goToRules = () => router.push('/rules')
const goToTasks = () => router.push('/tasks')

// 刷新方法
const refreshTrends = () => {
  ElMessage.success('趋势数据已刷新')
  initTrendChart()
}

const refreshDistribution = () => {
  ElMessage.success('分布数据已刷新')
  initStatusChart()
}

const refreshActivity = () => {
  ElMessage.success('活动数据已刷新')
  // 这里可以重新获取活动数据
}

// 初始化图表
const initTrendChart = () => {
  if (!trendChart.value) return
  
  const chart = echarts.init(trendChart.value)
  const option = {
    title: {
      text: '最近7天缺陷趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [12, 19, 3, 5, 2, 3, 8],
      type: 'line',
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
  chart.setOption(option)
}

const initStatusChart = () => {
  if (!statusChart.value) return
  
  const chart = echarts.init(statusChart.value)
  const option = {
    title: {
      text: '缺陷状态分布'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: '50%',
      data: [
        { value: 35, name: 'Open' },
        { value: 25, name: 'In Progress' },
        { value: 40, name: 'Resolved' }
      ]
    }]
  }
  chart.setOption(option)
}

const initPriorityChart = () => {
  if (!priorityChart.value) return
  
  const chart = echarts.init(priorityChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      data: [
        { value: 15, name: 'High' },
        { value: 45, name: 'Medium' },
        { value: 40, name: 'Low' }
      ]
    }]
  }
  chart.setOption(option)
}

const initProjectChart = () => {
  if (!projectChart.value) return
  
  const chart = echarts.init(projectChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['项目A', '项目B', '项目C', '项目D']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [23, 45, 12, 67],
      type: 'bar',
      itemStyle: {
        color: '#67C23A'
      }
    }]
  }
  chart.setOption(option)
}

const initDeviceChart = () => {
  if (!deviceChart.value) return
  
  const chart = echarts.init(deviceChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['iPhone', 'Samsung', 'Huawei', 'Xiaomi']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [34, 28, 19, 15],
      type: 'bar',
      itemStyle: {
        color: '#E6A23C'
      }
    }]
  }
  chart.setOption(option)
}

// 获取仪表板数据
const fetchDashboardData = async () => {
  try {
    const response = await dashboardApi.getStats()
    const data = response.data
    
    // 更新统计卡片
    statCards.value[0].value = data.defect_stats?.total_defects || 0
    statCards.value[1].value = data.defect_stats?.open_defects || 0
    statCards.value[2].value = data.defect_stats?.resolved_defects || 0
    statCards.value[3].value = data.batch_stats?.total_batches || 0
    
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

onMounted(async () => {
  await fetchDashboardData()
  
  await nextTick()
  
  // 初始化所有图表
  initTrendChart()
  initStatusChart()
  initPriorityChart()
  initProjectChart()
  initDeviceChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    // 重新调整图表大小
    setTimeout(() => {
      if (trendChart.value) echarts.getInstanceByDom(trendChart.value)?.resize()
      if (statusChart.value) echarts.getInstanceByDom(statusChart.value)?.resize()
      if (priorityChart.value) echarts.getInstanceByDom(priorityChart.value)?.resize()
      if (projectChart.value) echarts.getInstanceByDom(projectChart.value)?.resize()
      if (deviceChart.value) echarts.getInstanceByDom(deviceChart.value)?.resize()
    }, 100)
  })
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
}

.header-subtitle {
  color: #666;
  margin: 0;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.stat-change {
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: #67C23A;
}

.stat-change.negative {
  color: #F56C6C;
}

.stat-change.neutral {
  color: #909399;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.recent-activity {
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .chart-card {
    height: 300px;
  }
  
  .chart-container {
    height: 200px;
  }
}
</style>
