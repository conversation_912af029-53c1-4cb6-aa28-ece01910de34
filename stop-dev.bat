@echo off
chcp 65001 >nul

echo 🛑 停止缺陷分类工具开发环境 (Windows)...
echo.

REM 停止Django开发服务器
echo 🌐 停止Django开发服务器...
taskkill /F /IM python.exe /FI "WINDOWTITLE eq Django Server*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Django Server*" >nul 2>&1

REM 停止前端开发服务器
echo 🎨 停止前端开发服务器...
taskkill /F /IM node.exe /FI "WINDOWTITLE eq Frontend Server*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Frontend Server*" >nul 2>&1

REM 停止Celery Worker
echo 🔄 停止Celery Worker...
taskkill /F /IM python.exe /FI "WINDOWTITLE eq Celery Worker*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Celery Worker*" >nul 2>&1

REM 备用方法：通过进程命令行停止
echo 🔄 清理残留进程...
wmic process where "name='python.exe' and commandline like '%%manage.py runserver%%'" delete >nul 2>&1
wmic process where "name='python.exe' and commandline like '%%celery%%worker%%'" delete >nul 2>&1
wmic process where "name='node.exe' and commandline like '%%vite%%'" delete >nul 2>&1
wmic process where "name='node.exe' and commandline like '%%npm run dev%%'" delete >nul 2>&1

REM 停止可能的端口占用
echo 🔌 检查端口占用...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000') do (
    taskkill /F /PID %%a >nul 2>&1
)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000') do (
    taskkill /F /PID %%a >nul 2>&1
)

REM 可选：停止Redis
REM echo 📦 停止Redis服务...
REM taskkill /F /IM redis-server.exe >nul 2>&1

echo.
echo ✅ 开发环境已停止
echo.
echo 💡 提示:
echo    - 如果某些进程未能停止，请手动关闭对应的命令行窗口
echo    - 或者重启计算机以确保完全清理
echo    - 下次启动请运行 start-dev.bat
echo.
pause
