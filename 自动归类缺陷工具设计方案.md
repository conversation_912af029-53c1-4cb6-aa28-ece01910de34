# 自动归类缺陷工具设计方案

## 🚀 快速体验

**一键启动开发环境**：
```bash
git clone <repository-url>
cd defect-classification-tool
chmod +x start-dev.sh
./start-dev.sh
```

**访问地址**：
- 前端: http://localhost:3000
- 管理后台: http://localhost:8000/admin (admin/admin123)

---

## 1. 项目概述

### 1.1 项目背景
作为三级部门测开提效工具负责人，需要开发一款自动归类缺陷工具，用于解决日常交付中的缺陷分析痛点。

### 1.2 核心功能
- **数据汇总**: A、B、C类问题个数统计
- **机型缺陷汇总**: 各机型缺陷量分析
- **每日缺陷提报趋势**: 时间维度趋势分析
- **缺陷归类**: 自动分类缺陷类型
- **共性问题判断**: 识别和分析共性问题

### 1.3 核心字段
- Priority（优先级）
- Status（状态）
- Resolution（解决方案）
- Created（创建日期，转化为天格式：2025/4/7）
- stage（阶段）
- project（根据标题提取）
- summary_text（根据标题提取）
- bug_category（根据标题匹配）
- commonality（根据bug_category匹配）

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端界面层 (UI Layer)"
        A1[缺陷数据管理页面]
        A2[分类规则配置页面]
        A3[统计分析仪表板]
    end

    subgraph "API层 (API Layer)"
        B1[缺陷管理接口]
        B2[分类规则接口]
        B3[统计分析接口]
        B4[数据导入接口]
    end

    subgraph "业务逻辑层 (Business Layer)"
        C1[数据导入服务<br/>DataImportService]
        C2[自动分类服务<br/>AutoClassificationService]
        C3[统计分析服务<br/>StatisticsService]
        C4[规则管理服务<br/>RuleManagementService]
    end

    subgraph "数据层 (Data Layer)"
        D1[缺陷数据模型<br/>DefectRecord]
        D2[分类规则模型<br/>ClassificationRule]
        D3[统计分析模型<br/>DefectAnalysis]
        D4[SQLite数据库]
    end

    subgraph "外部数据源"
        E1[Excel文件]
        E2[CSV文件]
        E3[JIRA API]
        E4[其他数据源]
    end

    subgraph "工具组件"
        F1[分类算法引擎]
        F2[数据处理器]
        F3[报告生成器]
        F4[规则引擎]
    end

    %% 连接关系
    A1 --> B1
    A2 --> B2
    A3 --> B3

    B1 --> C1
    B1 --> C2
    B2 --> C4
    B3 --> C3
    B4 --> C1

    C1 --> D1
    C2 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D2

    D1 --> D4
    D2 --> D4
    D3 --> D4

    E1 --> C1
    E2 --> C1
    E3 --> C1
    E4 --> C1

    C2 --> F1
    C1 --> F2
    C3 --> F3
    C4 --> F4
```

### 2.2 架构说明
- **前端界面层**：提供用户交互界面，包括数据管理、规则配置和分析展示
- **API层**：RESTful API接口，实现前后端分离
- **业务逻辑层**：核心业务逻辑处理，包括数据导入、自动分类、统计分析等
- **数据层**：数据模型定义和数据库操作
- **外部数据源**：支持多种数据源导入
- **工具组件**：可复用的工具模块

### 2.3 技术栈
- **后端框架**: Django 5.2 + Django REST Framework
- **数据库**: SQLite3（开发）/ PostgreSQL（生产）
- **前端框架**: Vue 3 + TypeScript
- **前端UI库**: Element Plus / Ant Design Vue
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **图表库**: ECharts / Chart.js
- **构建工具**: Vite
- **数据处理**: Pandas + NumPy
- **异步任务**: Celery + Redis
- **实时通信**: Django Channels + WebSocket
- **任务调度**: Django APScheduler

### 2.4 多用户并发导入流程图

```mermaid
sequenceDiagram
    participant U1 as 用户1
    participant U2 as 用户2
    participant UI as 前端界面
    participant API as API服务
    participant Queue as 任务队列
    participant Celery as Celery Worker
    participant WS as WebSocket
    participant DB as 数据库

    Note over U1,U2: 多用户同时上传文件

    U1->>UI: 上传文件A
    U2->>UI: 上传文件B

    UI->>API: POST /api/defects/import/ (文件A)
    API->>DB: 创建ImportBatch记录
    API->>Queue: 提交任务到队列
    API->>UI: 返回任务ID和队列位置

    UI->>API: POST /api/defects/import/ (文件B)
    API->>DB: 创建ImportBatch记录
    API->>Queue: 提交任务到队列
    API->>UI: 返回任务ID和队列位置

    Note over UI: 建立WebSocket连接
    UI->>WS: 连接WebSocket
    WS->>UI: 连接成功

    Note over Queue,Celery: 任务队列处理
    Queue->>Celery: 分配任务A给Worker1
    Queue->>Celery: 任务B排队等待

    Note over Celery: Worker1处理任务A
    Celery->>DB: 更新状态为"处理中"
    Celery->>WS: 发送进度更新
    WS->>UI: 推送进度到用户1

    loop 处理过程
        Celery->>Celery: 数据验证和转换
        Celery->>DB: 更新进度
        Celery->>WS: 发送进度更新
        WS->>UI: 实时进度推送
    end

    Celery->>DB: 任务A完成，保存结果
    Celery->>WS: 发送完成通知
    WS->>UI: Toast提示"导入完成"

    Note over Queue: 任务B开始处理
    Queue->>Celery: 分配任务B给Worker2
    Celery->>DB: 更新状态为"处理中"
    Celery->>WS: 发送进度更新
    WS->>UI: 推送进度到用户2

    loop 处理过程
        Celery->>Celery: 数据验证和转换
        Celery->>DB: 更新进度
        Celery->>WS: 发送进度更新
        WS->>UI: 实时进度推送
    end

    Celery->>DB: 任务B完成，保存结果
    Celery->>WS: 发送完成通知
    WS->>UI: Toast提示"导入完成"

    Note over U1,U2: 用户查看结果
    U1->>UI: 查看导入结果
    U2->>UI: 查看导入结果
    UI->>API: 获取批次数据
    API->>DB: 查询用户数据
    DB->>API: 返回结果
    API->>UI: 返回数据
    UI->>U1: 显示用户1的数据
    UI->>U2: 显示用户2的数据
```

### 2.5 数据处理流程图

```mermaid
flowchart TD
    A[开始] --> B{数据来源}

    B -->|Excel文件| C1[Excel数据读取]
    B -->|CSV文件| C2[CSV数据读取]
    B -->|JIRA API| C3[JIRA数据获取]
    B -->|其他来源| C4[其他数据读取]

    C1 --> D[数据验证]
    C2 --> D
    C3 --> D
    C4 --> D

    D --> E{验证通过?}
    E -->|否| F[错误处理<br/>返回错误信息]
    E -->|是| G[数据清洗]

    G --> H[数据转换<br/>标准化格式]

    H --> I[项目信息提取<br/>extract_project]
    I --> J[摘要文本提取<br/>extract_summary_text]
    J --> K[缺陷分类<br/>classify_bug_category]
    K --> L[共性问题识别<br/>identify_commonality]

    L --> M[保存到数据库<br/>DefectRecord]

    M --> N[触发统计分析]

    N --> O[生成A/B/C类汇总]
    N --> P[生成机型缺陷汇总]
    N --> Q[生成每日趋势]
    N --> R[生成分类分布]
    N --> S[生成共性问题分析]

    O --> T[保存分析结果<br/>DefectAnalysis]
    P --> T
    Q --> T
    R --> T
    S --> T

    T --> U[更新仪表板数据]
    U --> V[结束]

    F --> V

    %% 分类规则更新流程
    W[分类规则管理] --> X[规则配置]
    X --> Y[规则测试]
    Y --> Z{测试通过?}
    Z -->|是| AA[保存规则<br/>ClassificationRule]
    Z -->|否| AB[规则调整]
    AB --> Y
    AA --> AC[重新分类现有数据]
    AC --> N
```

### 2.6 统计分析维度关系图

```mermaid
graph LR
    subgraph "数据输入"
        A1[Priority<br/>优先级]
        A2[Status<br/>状态]
        A3[Resolution<br/>解决方案]
        A4[Created<br/>创建日期]
        A5[Stage<br/>阶段]
        A6[Original Title<br/>原始标题]
    end

    subgraph "数据提取"
        B1[Project<br/>项目]
        B2[Summary Text<br/>摘要文本]
        B3[Bug Category<br/>缺陷分类]
        B4[Commonality<br/>共性问题]
    end

    subgraph "分析维度"
        C1[A/B/C类问题汇总<br/>基于Priority]
        C2[机型缺陷汇总<br/>基于Project]
        C3[每日缺陷趋势<br/>基于Created]
        C4[缺陷归类分析<br/>基于Bug Category]
        C5[共性问题判断<br/>基于Commonality]
    end

    subgraph "输出结果"
        D1[统计图表]
        D2[趋势分析]
        D3[分布分析]
        D4[汇总报告]
        D5[导出数据]
    end

    %% 数据流向
    A6 --> B1
    A6 --> B2
    A6 --> B3
    B3 --> B4

    A1 --> C1
    B1 --> C2
    A4 --> C3
    B3 --> C4
    B4 --> C5

    C1 --> D1
    C2 --> D1
    C3 --> D2
    C4 --> D3
    C5 --> D3

    C1 --> D4
    C2 --> D4
    C3 --> D4
    C4 --> D4
    C5 --> D4

    D1 --> D5
    D2 --> D5
    D3 --> D5
    D4 --> D5
```

## 3. 数据模型设计

### 3.1 缺陷数据模型
```python
class DefectRecord(models.Model):
    """
    缺陷记录模型 - 基于实际CSV文件字段设计
    支持部分字段导入，空字段默认为空值
    导出时按照全量字段导出
    """

    # === 核心标识字段 ===
    issue_key = models.CharField(max_length=50, unique=True, verbose_name='Issue key')
    issue_id = models.CharField(max_length=50, verbose_name='Issue id', blank=True)
    summary = models.TextField(verbose_name='Summary', blank=True)

    # === 基础状态字段 ===
    issue_type = models.CharField(max_length=50, verbose_name='Issue Type', blank=True)
    status = models.CharField(max_length=50, verbose_name='Status', blank=True)
    priority = models.CharField(max_length=20, verbose_name='Priority', blank=True)
    resolution = models.CharField(max_length=50, verbose_name='Resolution', blank=True)

    # === 项目相关字段 ===
    project_key = models.CharField(max_length=50, verbose_name='Project key', blank=True)
    project_name = models.CharField(max_length=200, verbose_name='Project name', blank=True)
    project_type = models.CharField(max_length=50, verbose_name='Project type', blank=True)
    project_lead = models.CharField(max_length=100, verbose_name='Project lead', blank=True)
    project_description = models.TextField(verbose_name='Project description', blank=True)
    project_url = models.URLField(verbose_name='Project url', blank=True)

    # === 人员相关字段 ===
    assignee = models.CharField(max_length=100, verbose_name='Assignee', blank=True)
    reporter = models.CharField(max_length=100, verbose_name='Reporter', blank=True)
    creator = models.CharField(max_length=100, verbose_name='Creator', blank=True)

    # === 时间相关字段 ===
    created = models.DateTimeField(verbose_name='Created', null=True, blank=True)
    updated = models.DateTimeField(verbose_name='Updated', null=True, blank=True)
    last_viewed = models.DateTimeField(verbose_name='Last Viewed', null=True, blank=True)
    resolved = models.DateTimeField(verbose_name='Resolved', null=True, blank=True)
    due_date = models.DateTimeField(verbose_name='Due Date', null=True, blank=True)

    # === 版本相关字段 ===
    affects_versions = models.TextField(verbose_name='Affects Version/s', blank=True)
    components = models.TextField(verbose_name='Component/s', blank=True)

    # === 描述和环境字段 ===
    description = models.TextField(verbose_name='Description', blank=True)
    environment = models.TextField(verbose_name='Environment', blank=True)

    # === 工作量相关字段 ===
    original_estimate = models.CharField(max_length=50, verbose_name='Original Estimate', blank=True)
    remaining_estimate = models.CharField(max_length=50, verbose_name='Remaining Estimate', blank=True)
    time_spent = models.CharField(max_length=50, verbose_name='Time Spent', blank=True)
    work_ratio = models.FloatField(verbose_name='Work Ratio', null=True, blank=True)

    # === 其他基础字段 ===
    votes = models.IntegerField(verbose_name='Votes', default=0)
    labels = models.TextField(verbose_name='Labels', blank=True)
    watchers = models.TextField(verbose_name='Watchers', blank=True)
    security_level = models.CharField(max_length=50, verbose_name='Security Level', blank=True)
    attachments = models.TextField(verbose_name='Attachments', blank=True)

    # === 自定义字段（基于CSV中的Custom field字段） ===
    # 版本相关
    affect_apk_versions = models.TextField(verbose_name='Affect Apk Version/s', blank=True)
    fix_apk_versions = models.TextField(verbose_name='Fix Apk Version/s', blank=True)
    android_version = models.CharField(max_length=100, verbose_name='Android版本', blank=True)
    os_version = models.CharField(max_length=100, verbose_name='OS版本', blank=True)

    # 项目和模块相关
    affect_project = models.CharField(max_length=200, verbose_name='Affect Project', blank=True)
    module = models.CharField(max_length=100, verbose_name='模块', blank=True)
    component_custom = models.CharField(max_length=100, verbose_name='Change Component', blank=True)

    # 缺陷分类相关
    bug_source = models.CharField(max_length=100, verbose_name='BUG来源', blank=True)
    bug_category_custom = models.CharField(max_length=100, verbose_name='Bug category', blank=True)
    issue_category = models.CharField(max_length=100, verbose_name='Issue Category', blank=True)
    issue_nature = models.CharField(max_length=100, verbose_name='Issue Nature', blank=True)
    issue_source = models.CharField(max_length=100, verbose_name='Issue Source', blank=True)
    issue_stage = models.CharField(max_length=100, verbose_name='Issue Stage', blank=True)
    common_issue = models.CharField(max_length=100, verbose_name='CommonIssue', blank=True)

    # 设备和环境相关
    device_model = models.CharField(max_length=100, verbose_name='机型', blank=True)
    country_code = models.CharField(max_length=10, verbose_name='CountryCode', blank=True)
    country = models.CharField(max_length=50, verbose_name='国家', blank=True)
    imei = models.CharField(max_length=50, verbose_name='IMEI', blank=True)
    sn = models.CharField(max_length=50, verbose_name='SN', blank=True)

    # 责任人相关
    rd_owner = models.CharField(max_length=100, verbose_name='RD owner', blank=True)
    pm = models.CharField(max_length=100, verbose_name='PM', blank=True)
    opener = models.CharField(max_length=100, verbose_name='Opener', blank=True)
    fixer = models.CharField(max_length=100, verbose_name='Fixer', blank=True)

    # 分析和解决相关
    root_cause = models.TextField(verbose_name='根因', blank=True)
    solution = models.TextField(verbose_name='解决方案', blank=True)
    fix_way = models.TextField(verbose_name='Fix Way', blank=True)
    risk = models.CharField(max_length=100, verbose_name='Risk', blank=True)

    # 测试相关
    test_suggestion = models.TextField(verbose_name='测试建议', blank=True)
    test_owner = models.CharField(max_length=100, verbose_name='测试负责人', blank=True)
    need_retest = models.CharField(max_length=10, verbose_name='需要Retest', blank=True)
    retest_reason = models.TextField(verbose_name='Retest原因', blank=True)
    retest_suggestion = models.TextField(verbose_name='Retest建议', blank=True)

    # === 数据处理字段（自动生成） ===
    # 提取和分类字段
    project_extracted = models.CharField(max_length=100, verbose_name='提取的项目', blank=True)
    summary_text = models.TextField(verbose_name='摘要文本', blank=True)
    bug_category = models.CharField(max_length=100, verbose_name='缺陷分类', blank=True)
    commonality = models.CharField(max_length=100, verbose_name='共性问题', blank=True)

    # 原始数据存储
    original_data = models.JSONField(default=dict, verbose_name='原始导入数据')

    # === 数据权限和导入标识 ===
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', db_index=True)
    import_timestamp = models.DateTimeField(verbose_name='导入时间戳', db_index=True)
    uploaded_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='上传用户',
        related_name='uploaded_defects'
    )

    # === 系统字段 ===
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='记录更新时间')

    class Meta:
        verbose_name = '缺陷记录'
        verbose_name_plural = '缺陷记录'
        ordering = ['-import_timestamp', '-created']
        indexes = [
            models.Index(fields=['import_batch_id', 'uploaded_by']),
            models.Index(fields=['import_timestamp', 'uploaded_by']),
            models.Index(fields=['issue_key']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['project_key', 'uploaded_by']),
            models.Index(fields=['assignee', 'uploaded_by']),
        ]

    def __str__(self):
        return f"{self.issue_key} - {self.summary[:50]}"

    @property
    def created_date(self):
        """兼容性属性，返回创建日期"""
        return self.created.date() if self.created else None
```

### 3.2 分类规则模型
```python
class ClassificationRule(models.Model):
    RULE_TYPE_CHOICES = [
        ('project', '项目提取'),
        ('category', '缺陷分类'),
        ('commonality', '共性问题'),
    ]

    SCOPE_CHOICES = [
        ('global', '全局规则'),
        ('personal', '个人规则'),
    ]

    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    rule_name = models.CharField(max_length=100, verbose_name='规则名称')
    pattern = models.TextField(verbose_name='匹配模式')
    target_value = models.CharField(max_length=100, verbose_name='目标值')
    priority = models.IntegerField(default=0, verbose_name='优先级')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, verbose_name='规则描述')

    # 权限和作用域控制
    scope = models.CharField(max_length=20, choices=SCOPE_CHOICES, default='personal', verbose_name='规则作用域')
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='创建用户',
        related_name='created_rules'
    )

    # 规则来源追踪
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', blank=True, db_index=True)
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '分类规则'
        verbose_name_plural = '分类规则'
        ordering = ['scope', '-priority', 'rule_type']
        indexes = [
            models.Index(fields=['scope', 'created_by', 'is_active']),
            models.Index(fields=['rule_type', 'scope', 'priority']),
            models.Index(fields=['import_batch_id']),
        ]

    def can_edit(self, user):
        """检查用户是否可以编辑此规则"""
        if self.scope == 'global':
            # 全局规则只有管理员可以编辑
            user_profile = UserProfile.objects.get_or_create(user=user)[0]
            return user_profile.role == 'admin' or user_profile.can_manage_rules
        else:
            # 个人规则只有创建者可以编辑
            return self.created_by == user
```

### 3.3 字段映射配置模型
```python
class FieldMapping(models.Model):
    """
    字段映射配置模型
    用于管理CSV字段到数据库字段的映射关系
    """
    FIELD_TYPE_CHOICES = [
        ('string', '字符串'),
        ('text', '文本'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('datetime', '日期时间'),
        ('date', '日期'),
        ('boolean', '布尔值'),
        ('json', 'JSON'),
    ]

    # 映射信息
    csv_field_name = models.CharField(max_length=200, verbose_name='CSV字段名')
    db_field_name = models.CharField(max_length=100, verbose_name='数据库字段名')
    field_type = models.CharField(max_length=20, choices=FIELD_TYPE_CHOICES, verbose_name='字段类型')

    # 映射配置
    is_required = models.BooleanField(default=False, verbose_name='是否必需')
    is_core_field = models.BooleanField(default=False, verbose_name='是否核心字段')
    default_value = models.TextField(verbose_name='默认值', blank=True)

    # 数据处理配置
    date_format = models.CharField(max_length=50, verbose_name='日期格式', blank=True)
    max_length = models.IntegerField(verbose_name='最大长度', null=True, blank=True)
    validation_regex = models.TextField(verbose_name='验证正则表达式', blank=True)

    # 描述信息
    description = models.TextField(verbose_name='字段描述', blank=True)
    example_value = models.CharField(max_length=200, verbose_name='示例值', blank=True)

    # 系统字段
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '字段映射'
        verbose_name_plural = '字段映射'
        unique_together = ['csv_field_name', 'db_field_name']
        ordering = ['is_core_field', 'is_required', 'csv_field_name']

    def __str__(self):
        return f"{self.csv_field_name} -> {self.db_field_name}"

### 3.4 导入批次管理模型
```python
class ImportBatch(models.Model):
    """导入批次管理模型"""
    batch_id = models.CharField(max_length=50, unique=True, verbose_name='批次ID')
    batch_name = models.CharField(max_length=200, verbose_name='批次名称', blank=True)
    import_timestamp = models.DateTimeField(auto_now_add=True, verbose_name='导入时间')
    uploaded_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='上传用户'
    )

    # 导入统计
    total_records = models.IntegerField(default=0, verbose_name='总记录数')
    success_records = models.IntegerField(default=0, verbose_name='成功记录数')
    failed_records = models.IntegerField(default=0, verbose_name='失败记录数')
    processed_records = models.IntegerField(default=0, verbose_name='已处理记录数')

    # 数据源信息
    data_source = models.CharField(max_length=50, verbose_name='数据源类型')  # excel, csv, jira_api
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)
    source_file_size = models.BigIntegerField(verbose_name='文件大小(字节)', null=True, blank=True)

    # 处理状态和进度
    STATUS_CHOICES = [
        ('uploaded', '已上传'),
        ('queued', '排队中'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded', verbose_name='处理状态')
    progress_percentage = models.FloatField(default=0.0, verbose_name='处理进度百分比')
    current_step = models.CharField(max_length=100, verbose_name='当前处理步骤', blank=True)

    # 队列信息
    queue_position = models.IntegerField(default=0, verbose_name='队列位置')
    estimated_wait_time = models.IntegerField(default=0, verbose_name='预估等待时间(秒)')

    # 处理时间
    processing_started_at = models.DateTimeField(verbose_name='开始处理时间', null=True, blank=True)
    processing_completed_at = models.DateTimeField(verbose_name='处理完成时间', null=True, blank=True)

    # 错误信息
    error_message = models.TextField(verbose_name='错误信息', blank=True)
    error_details = models.JSONField(default=dict, verbose_name='详细错误信息')

    # 任务ID（用于异步任务追踪）
    task_id = models.CharField(max_length=100, verbose_name='任务ID', blank=True, db_index=True)

    class Meta:
        verbose_name = '导入批次'
        verbose_name_plural = '导入批次'
        ordering = ['-import_timestamp']
        indexes = [
            models.Index(fields=['status', 'uploaded_by']),
            models.Index(fields=['task_id']),
            models.Index(fields=['queue_position', 'status']),
        ]

### 3.5 任务队列管理模型
```python
class TaskQueue(models.Model):
    """任务队列管理模型"""
    TASK_TYPE_CHOICES = [
        ('data_import', '数据导入'),
        ('data_analysis', '数据分析'),
        ('rule_import', '规则导入'),
        ('batch_export', '批量导出'),
    ]

    PRIORITY_CHOICES = [
        (1, '低优先级'),
        (2, '普通优先级'),
        (3, '高优先级'),
        (4, '紧急优先级'),
    ]

    task_id = models.CharField(max_length=100, unique=True, verbose_name='任务ID')
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES, verbose_name='任务类型')
    task_name = models.CharField(max_length=200, verbose_name='任务名称')

    # 用户信息
    created_by = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name='创建用户')

    # 任务参数
    task_params = models.JSONField(default=dict, verbose_name='任务参数')

    # 优先级和队列
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2, verbose_name='优先级')
    queue_position = models.IntegerField(default=0, verbose_name='队列位置')

    # 状态和进度
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='任务状态')
    progress = models.FloatField(default=0.0, verbose_name='执行进度')
    current_step = models.CharField(max_length=100, verbose_name='当前步骤', blank=True)

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    started_at = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    completed_at = models.DateTimeField(verbose_name='完成时间', null=True, blank=True)
    estimated_duration = models.IntegerField(default=0, verbose_name='预估执行时间(秒)')

    # 结果和错误
    result_data = models.JSONField(default=dict, verbose_name='执行结果')
    error_message = models.TextField(verbose_name='错误信息', blank=True)

    # 关联对象
    related_batch_id = models.CharField(max_length=50, verbose_name='关联批次ID', blank=True, db_index=True)

    class Meta:
        verbose_name = '任务队列'
        verbose_name_plural = '任务队列'
        ordering = ['priority', 'queue_position', 'created_at']
        indexes = [
            models.Index(fields=['status', 'priority', 'queue_position']),
            models.Index(fields=['created_by', 'status']),
            models.Index(fields=['task_type', 'status']),
        ]

### 3.6 统计分析模型
```python
class DefectAnalysis(models.Model):
    ANALYSIS_TYPE_CHOICES = [
        ('abc_summary', 'A/B/C类问题汇总'),
        ('model_summary', '机型缺陷汇总'),
        ('daily_trend', '每日趋势'),
        ('category_distribution', '分类分布'),
        ('commonality_analysis', '共性问题分析'),
    ]

    analysis_date = models.DateField(verbose_name='分析日期')
    analysis_type = models.CharField(max_length=50, choices=ANALYSIS_TYPE_CHOICES, verbose_name='分析类型')
    analysis_data = models.JSONField(verbose_name='分析数据')
    date_range_start = models.DateField(verbose_name='数据范围开始', null=True, blank=True)
    date_range_end = models.DateField(verbose_name='数据范围结束', null=True, blank=True)

    # 权限控制
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='创建用户',
        null=True, blank=True
    )
    is_global = models.BooleanField(default=False, verbose_name='是否全局分析')
    import_batch = models.ForeignKey(
        ImportBatch,
        on_delete=models.CASCADE,
        verbose_name='关联导入批次',
        null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '缺陷分析'
        verbose_name_plural = '缺陷分析'
        indexes = [
            models.Index(fields=['analysis_date', 'analysis_type', 'created_by']),
            models.Index(fields=['import_batch', 'analysis_type']),
        ]

### 3.7 用户权限扩展模型
```python
class UserProfile(models.Model):
    """用户权限扩展模型"""
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, verbose_name='用户')

    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('user', '普通用户'),
    ]
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='user', verbose_name='用户角色')

    # 权限设置
    can_view_all_data = models.BooleanField(default=False, verbose_name='可查看全部数据')
    can_manage_rules = models.BooleanField(default=False, verbose_name='可管理分类规则')
    can_export_data = models.BooleanField(default=True, verbose_name='可导出数据')

    # 配额限制
    max_import_size = models.BigIntegerField(default=10*1024*1024, verbose_name='最大导入文件大小(字节)')  # 10MB
    max_records_per_import = models.IntegerField(default=10000, verbose_name='单次导入最大记录数')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '用户配置'
        verbose_name_plural = '用户配置'
```

## 4. 核心服务设计

### 4.1 异步任务队列服务
```python
class TaskQueueService:
    """异步任务队列服务"""

    def __init__(self):
        self.redis_client = self._get_redis_client()

    def submit_import_task(self, user, file_path, batch_name=None, priority=2):
        """提交数据导入任务"""
        import uuid
        from datetime import datetime

        # 生成任务ID
        task_id = f"import_{user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 创建导入批次
        batch_id = f"{user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 获取文件信息
        import os
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)

        # 创建批次记录
        batch = ImportBatch.objects.create(
            batch_id=batch_id,
            batch_name=batch_name or f"导入_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            uploaded_by=user,
            data_source=self._get_file_type(file_path),
            source_file_name=file_name,
            source_file_size=file_size,
            status='queued',
            task_id=task_id
        )

        # 创建任务记录
        task = TaskQueue.objects.create(
            task_id=task_id,
            task_type='data_import',
            task_name=f"导入数据 - {file_name}",
            created_by=user,
            priority=priority,
            task_params={
                'file_path': file_path,
                'batch_id': batch_id,
                'batch_name': batch_name
            },
            related_batch_id=batch_id,
            estimated_duration=self._estimate_import_duration(file_size)
        )

        # 更新队列位置
        self._update_queue_positions()

        # 提交到Celery队列
        from .tasks import process_data_import
        process_data_import.delay(task_id)

        return {
            'task_id': task_id,
            'batch_id': batch_id,
            'queue_position': task.queue_position,
            'estimated_wait_time': self._calculate_wait_time(task.queue_position)
        }

    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            task = TaskQueue.objects.get(task_id=task_id)
            batch = ImportBatch.objects.get(task_id=task_id)

            return {
                'task_id': task_id,
                'status': task.status,
                'progress': task.progress,
                'current_step': task.current_step,
                'queue_position': task.queue_position,
                'estimated_wait_time': self._calculate_wait_time(task.queue_position),
                'batch_info': {
                    'batch_id': batch.batch_id,
                    'total_records': batch.total_records,
                    'processed_records': batch.processed_records,
                    'success_records': batch.success_records,
                    'failed_records': batch.failed_records,
                    'progress_percentage': batch.progress_percentage
                },
                'error_message': task.error_message
            }
        except (TaskQueue.DoesNotExist, ImportBatch.DoesNotExist):
            return {'error': '任务不存在'}

    def cancel_task(self, task_id, user):
        """取消任务"""
        try:
            task = TaskQueue.objects.get(task_id=task_id, created_by=user)
            if task.status in ['pending', 'running']:
                task.status = 'cancelled'
                task.save()

                # 更新批次状态
                ImportBatch.objects.filter(task_id=task_id).update(status='cancelled')

                # 撤销Celery任务
                from celery import current_app
                current_app.control.revoke(task_id, terminate=True)

                return True
        except TaskQueue.DoesNotExist:
            pass
        return False

    def get_user_tasks(self, user, status=None):
        """获取用户任务列表"""
        queryset = TaskQueue.objects.filter(created_by=user)
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')

    def _update_queue_positions(self):
        """更新队列位置"""
        pending_tasks = TaskQueue.objects.filter(status='pending').order_by('priority', 'created_at')
        for i, task in enumerate(pending_tasks):
            task.queue_position = i + 1
            task.save(update_fields=['queue_position'])

    def _calculate_wait_time(self, queue_position):
        """计算预估等待时间"""
        if queue_position <= 1:
            return 0

        # 基于历史数据计算平均处理时间
        avg_processing_time = 300  # 5分钟默认值
        return (queue_position - 1) * avg_processing_time

    def _estimate_import_duration(self, file_size):
        """预估导入时间"""
        # 基于文件大小预估处理时间（每MB约30秒）
        mb_size = file_size / (1024 * 1024)
        return int(mb_size * 30)

    def _get_file_type(self, file_path):
        """获取文件类型"""
        import os
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.xlsx', '.xls']:
            return 'excel'
        elif ext == '.csv':
            return 'csv'
        else:
            return 'unknown'

    def _get_redis_client(self):
        """获取Redis客户端"""
        import redis
        return redis.Redis(host='localhost', port=6379, db=0)

### 4.2 字段映射服务
```python
class FieldMappingService:
    """字段映射服务"""

    def __init__(self):
        self.field_mappings = self._load_field_mappings()

    def _load_field_mappings(self):
        """加载字段映射配置"""
        mappings = {}
        for mapping in FieldMapping.objects.all():
            mappings[mapping.csv_field_name] = {
                'db_field': mapping.db_field_name,
                'field_type': mapping.field_type,
                'is_required': mapping.is_required,
                'default_value': mapping.default_value,
                'date_format': mapping.date_format,
                'max_length': mapping.max_length,
                'validation_regex': mapping.validation_regex
            }
        return mappings

    def get_supported_fields(self):
        """获取支持的字段列表"""
        return list(self.field_mappings.keys())

    def map_csv_row_to_model_data(self, csv_row):
        """将CSV行数据映射为模型数据"""
        model_data = {}
        original_data = {}

        for csv_field, value in csv_row.items():
            # 保存原始数据
            original_data[csv_field] = value

            # 检查是否有映射配置
            if csv_field in self.field_mappings:
                mapping = self.field_mappings[csv_field]
                db_field = mapping['db_field']

                # 数据转换和验证
                converted_value = self._convert_field_value(
                    value,
                    mapping['field_type'],
                    mapping.get('date_format'),
                    mapping.get('default_value')
                )

                # 长度验证
                if mapping.get('max_length') and converted_value:
                    if isinstance(converted_value, str) and len(converted_value) > mapping['max_length']:
                        converted_value = converted_value[:mapping['max_length']]

                # 正则验证
                if mapping.get('validation_regex') and converted_value:
                    import re
                    if not re.match(mapping['validation_regex'], str(converted_value)):
                        converted_value = mapping.get('default_value', '')

                model_data[db_field] = converted_value

        # 添加原始数据
        model_data['original_data'] = original_data

        return model_data

    def _convert_field_value(self, value, field_type, date_format=None, default_value=None):
        """转换字段值"""
        # 处理空值
        if value is None or value == '' or str(value).strip() == '':
            return default_value if default_value else None

        try:
            if field_type == 'string':
                return str(value).strip()
            elif field_type == 'text':
                return str(value).strip()
            elif field_type == 'integer':
                return int(float(value)) if value else None
            elif field_type == 'float':
                return float(value) if value else None
            elif field_type == 'boolean':
                return str(value).lower() in ['true', '1', 'yes', 'on', '是']
            elif field_type == 'datetime':
                return self._parse_datetime(value, date_format)
            elif field_type == 'date':
                return self._parse_date(value, date_format)
            elif field_type == 'json':
                import json
                return json.loads(value) if isinstance(value, str) else value
            else:
                return str(value).strip()
        except (ValueError, TypeError):
            return default_value if default_value else None

    def _parse_datetime(self, value, date_format=None):
        """解析日期时间"""
        if not value:
            return None

        from datetime import datetime
        import dateutil.parser

        # 尝试使用指定格式
        if date_format:
            try:
                return datetime.strptime(str(value), date_format)
            except ValueError:
                pass

        # 尝试常见格式
        common_formats = [
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d',
            '%Y-%m-%d',
            '%d/%m/%Y %H:%M:%S',
            '%d-%m-%Y %H:%M:%S',
            '%d/%m/%Y',
            '%d-%m-%Y',
        ]

        for fmt in common_formats:
            try:
                return datetime.strptime(str(value), fmt)
            except ValueError:
                continue

        # 使用dateutil解析
        try:
            return dateutil.parser.parse(str(value))
        except:
            return None

    def _parse_date(self, value, date_format=None):
        """解析日期"""
        dt = self._parse_datetime(value, date_format)
        return dt.date() if dt else None

    def validate_csv_headers(self, csv_headers):
        """验证CSV表头"""
        supported_fields = set(self.get_supported_fields())
        csv_fields = set(csv_headers)

        # 检查必需字段
        required_fields = {
            field for field, mapping in self.field_mappings.items()
            if mapping['is_required']
        }
        missing_required = required_fields - csv_fields

        # 检查未知字段
        unknown_fields = csv_fields - supported_fields

        return {
            'valid': len(missing_required) == 0,
            'missing_required_fields': list(missing_required),
            'unknown_fields': list(unknown_fields),
            'supported_fields': list(csv_fields & supported_fields),
            'total_supported': len(csv_fields & supported_fields),
            'total_fields': len(csv_fields)
        }

### 4.3 数据导入服务
```python
class DefectDataImportService:
    """缺陷数据导入服务"""

    def __init__(self, user, task_id=None):
        self.user = user
        self.user_profile = UserProfile.objects.get_or_create(user=user)[0]
        self.task_id = task_id
        self.field_mapping_service = FieldMappingService()

    def update_progress(self, progress, step_description):
        """更新任务进度"""
        if self.task_id:
            TaskQueue.objects.filter(task_id=self.task_id).update(
                progress=progress,
                current_step=step_description
            )

            # 同时更新批次进度
            ImportBatch.objects.filter(task_id=self.task_id).update(
                progress_percentage=progress,
                current_step=step_description
            )

    def process_import_async(self, file_path, batch_id):
        """异步处理数据导入"""
        try:
            # 更新状态为处理中
            batch = ImportBatch.objects.get(batch_id=batch_id)
            batch.status = 'processing'
            batch.processing_started_at = timezone.now()
            batch.save()

            self.update_progress(5, "开始处理数据")

            # 检查权限和配额
            self._check_import_permissions(file_path)
            self.update_progress(10, "权限检查完成")

            # 执行导入逻辑
            result = self._process_import(file_path, batch, batch.data_source)

            # 更新完成状态
            batch.status = 'completed'
            batch.processing_completed_at = timezone.now()
            batch.save()

            self.update_progress(100, "处理完成")

            # 发送完成通知
            self._send_completion_notification(batch, result)

            return result

        except Exception as e:
            # 更新失败状态
            batch.status = 'failed'
            batch.error_message = str(e)
            batch.processing_completed_at = timezone.now()
            batch.save()

            if self.task_id:
                TaskQueue.objects.filter(task_id=self.task_id).update(
                    status='failed',
                    error_message=str(e)
                )

            # 发送失败通知
            self._send_failure_notification(batch, str(e))
            raise

    def import_from_csv(self, file_path, batch_name=None):
        """从CSV导入缺陷数据"""
        # 类似Excel导入逻辑
        pass

    def import_from_jira_api(self, jql_query, batch_name=None):
        """从JIRA API导入缺陷数据"""
        # 创建API导入批次
        batch = self.create_import_batch(
            batch_name=batch_name or f"JIRA查询_{jql_query[:50]}",
            data_source='jira_api'
        )
        # 执行API导入逻辑
        pass

    def _check_import_permissions(self, file_path):
        """检查导入权限和配额"""
        file_size = os.path.getsize(file_path)

        if file_size > self.user_profile.max_import_size:
            raise PermissionError(f"文件大小超过限制: {file_size} > {self.user_profile.max_import_size}")

    def _process_import(self, file_path, batch, data_source):
        """处理导入数据"""
        # 读取和验证数据
        raw_data = self._read_data(file_path, data_source)

        if len(raw_data) > self.user_profile.max_records_per_import:
            raise PermissionError(f"记录数超过限制: {len(raw_data)} > {self.user_profile.max_records_per_import}")

        self.update_progress(15, "验证CSV表头字段")

        # 验证CSV表头
        if raw_data:
            headers = list(raw_data[0].keys())
            validation_result = self.field_mapping_service.validate_csv_headers(headers)

            if not validation_result['valid']:
                raise ValueError(f"缺少必需字段: {', '.join(validation_result['missing_required_fields'])}")

            # 记录字段映射信息
            batch.error_details = {
                'field_validation': validation_result,
                'supported_fields_count': validation_result['total_supported'],
                'total_fields_count': validation_result['total_fields'],
                'unknown_fields': validation_result['unknown_fields']
            }
            batch.save()

        self.update_progress(20, "开始处理数据记录")

        # 数据转换和分类
        processed_data = []
        success_count = 0
        failed_count = 0
        error_details = []

        total_records = len(raw_data)

        for i, row_data in enumerate(raw_data):
            try:
                # 更新进度
                if i % 100 == 0:  # 每100条记录更新一次进度
                    progress = 20 + (i / total_records) * 60  # 20-80%的进度用于数据处理
                    self.update_progress(progress, f"处理记录 {i+1}/{total_records}")

                # 使用字段映射服务转换数据
                defect_data = self.field_mapping_service.map_csv_row_to_model_data(row_data)

                # 添加批次信息
                defect_data.update({
                    'import_batch_id': batch.batch_id,
                    'import_timestamp': batch.import_timestamp,
                    'uploaded_by': self.user
                })

                # 自动分类
                self.update_progress(progress + 0.1, f"分类记录 {i+1}")
                classification_result = self.auto_classify(defect_data)
                defect_data.update(classification_result)

                # 数据验证
                validated_data = self._validate_defect_data(defect_data)

                # 保存记录
                DefectRecord.objects.create(**validated_data)
                success_count += 1

            except Exception as e:
                failed_count += 1
                error_details.append({
                    'row': i + 1,
                    'error': str(e),
                    'data': row_data
                })

                # 记录错误日志
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"导入第{i+1}行数据失败: {str(e)}")

        self.update_progress(85, "更新批次统计信息")

        # 更新批次统计
        batch.total_records = len(raw_data)
        batch.success_records = success_count
        batch.failed_records = failed_count
        batch.processed_records = success_count + failed_count

        # 保存错误详情
        if error_details:
            batch.error_details.update({
                'import_errors': error_details[:100]  # 只保存前100个错误
            })

        batch.save()

        self.update_progress(90, "生成导入报告")

        return {
            'batch_id': batch.batch_id,
            'total': len(raw_data),
            'success': success_count,
            'failed': failed_count,
            'field_mapping_info': validation_result if raw_data else {},
            'error_summary': {
                'total_errors': len(error_details),
                'sample_errors': error_details[:5]  # 返回前5个错误作为示例
            }
        }

    def _validate_defect_data(self, defect_data):
        """验证缺陷数据"""
        validated_data = {}

        # 获取模型字段
        model_fields = {field.name: field for field in DefectRecord._meta.get_fields()}

        for field_name, value in defect_data.items():
            if field_name in model_fields:
                field = model_fields[field_name]

                # 字符串字段长度检查
                if hasattr(field, 'max_length') and field.max_length and value:
                    if isinstance(value, str) and len(value) > field.max_length:
                        value = value[:field.max_length]

                # 必需字段检查
                if not field.null and not field.blank and not value:
                    if field.default != models.NOT_PROVIDED:
                        value = field.default
                    elif field_name == 'issue_key':
                        # issue_key是必需的，生成一个临时值
                        import uuid
                        value = f"TEMP_{uuid.uuid4().hex[:8]}"

                validated_data[field_name] = value

        return validated_data

    def _read_data(self, file_path, data_source):
        """读取数据文件"""
        import pandas as pd

        try:
            if data_source == 'excel':
                df = pd.read_excel(file_path)
            elif data_source == 'csv':
                # 尝试不同的编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                df = None
                for encoding in encodings:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue

                if df is None:
                    raise ValueError("无法读取CSV文件，请检查文件编码")
            else:
                raise ValueError(f"不支持的数据源类型: {data_source}")

            # 处理空值
            df = df.fillna('')

            # 转换为字典列表
            return df.to_dict('records')

        except Exception as e:
            raise ValueError(f"读取文件失败: {str(e)}")

    def _send_completion_notification(self, batch, result):
        """发送完成通知"""
        from .notification_service import NotificationService
        notification_service = NotificationService()

        notification_service.send_user_notification(
            user=batch.uploaded_by,
            notification_type='import_completed',
            title='数据导入完成',
            message=f"批次 {batch.batch_name} 导入完成，成功处理 {result['success']} 条记录",
            data={
                'batch_id': batch.batch_id,
                'result': result
            }
        )

    def _send_failure_notification(self, batch, error_message):
        """发送失败通知"""
        from .notification_service import NotificationService
        notification_service = NotificationService()

        notification_service.send_user_notification(
            user=batch.uploaded_by,
            notification_type='import_failed',
            title='数据导入失败',
            message=f"批次 {batch.batch_name} 导入失败：{error_message}",
            data={
                'batch_id': batch.batch_id,
                'error': error_message
            }
        )

    def validate_data(self, data):
        """数据验证"""
        pass

    def transform_data(self, raw_data):
        """数据转换"""
        pass

    def auto_classify(self, defect_data):
        """自动分类"""
        from .classification_service import AutoClassificationService
        classification_service = AutoClassificationService(self.user)

        # 获取标题用于分类
        title = defect_data.get('summary', '') or defect_data.get('original_data', {}).get('Summary', '')
        description = defect_data.get('description', '')

        return {
            'project_extracted': classification_service.extract_project(title),
            'summary_text': classification_service.extract_summary_text(title),
            'bug_category': classification_service.classify_bug_category(title, description),
            'commonality': classification_service.identify_commonality(
                defect_data.get('bug_category_custom', '') or
                classification_service.classify_bug_category(title, description)
            )
        }

### 4.4 数据导出服务
```python
class DefectDataExportService:
    """缺陷数据导出服务"""

    def __init__(self, user):
        self.user = user
        self.permission_service = DataPermissionService(user)

    def export_defects_to_excel(self, queryset=None, include_all_fields=True):
        """导出缺陷数据到Excel"""
        import pandas as pd
        from io import BytesIO

        if queryset is None:
            queryset = self.permission_service.get_accessible_defects()

        # 获取所有字段定义
        all_fields = self._get_all_export_fields() if include_all_fields else self._get_basic_export_fields()

        # 准备数据
        data = []
        for defect in queryset:
            row_data = {}
            for field_info in all_fields:
                field_name = field_info['field_name']
                csv_name = field_info['csv_name']
                field_type = field_info['field_type']

                # 获取字段值
                value = self._get_field_value(defect, field_name, field_type)
                row_data[csv_name] = value

            data.append(row_data)

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 导出到Excel
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='缺陷数据', index=False)

            # 添加字段说明sheet
            field_info_df = pd.DataFrame([
                {
                    'CSV字段名': field['csv_name'],
                    '数据库字段名': field['field_name'],
                    '字段类型': field['field_type'],
                    '字段描述': field.get('description', '')
                }
                for field in all_fields
            ])
            field_info_df.to_excel(writer, sheet_name='字段说明', index=False)

        output.seek(0)
        return output

    def export_defects_to_csv(self, queryset=None, include_all_fields=True):
        """导出缺陷数据到CSV"""
        import pandas as pd
        from io import StringIO

        if queryset is None:
            queryset = self.permission_service.get_accessible_defects()

        # 获取所有字段定义
        all_fields = self._get_all_export_fields() if include_all_fields else self._get_basic_export_fields()

        # 准备数据
        data = []
        for defect in queryset:
            row_data = {}
            for field_info in all_fields:
                field_name = field_info['field_name']
                csv_name = field_info['csv_name']
                field_type = field_info['field_type']

                # 获取字段值
                value = self._get_field_value(defect, field_name, field_type)
                row_data[csv_name] = value

            data.append(row_data)

        # 创建DataFrame并导出
        df = pd.DataFrame(data)
        output = StringIO()
        df.to_csv(output, index=False, encoding='utf-8-sig')
        output.seek(0)
        return output

    def _get_all_export_fields(self):
        """获取所有导出字段定义"""
        # 基于FieldMapping和DefectRecord模型定义完整的字段列表
        fields = [
            # 核心标识字段
            {'field_name': 'issue_key', 'csv_name': 'Issue key', 'field_type': 'string', 'description': '缺陷编号'},
            {'field_name': 'issue_id', 'csv_name': 'Issue id', 'field_type': 'string', 'description': '缺陷ID'},
            {'field_name': 'summary', 'csv_name': 'Summary', 'field_type': 'text', 'description': '摘要'},

            # 基础状态字段
            {'field_name': 'issue_type', 'csv_name': 'Issue Type', 'field_type': 'string', 'description': '问题类型'},
            {'field_name': 'status', 'csv_name': 'Status', 'field_type': 'string', 'description': '状态'},
            {'field_name': 'priority', 'csv_name': 'Priority', 'field_type': 'string', 'description': '优先级'},
            {'field_name': 'resolution', 'csv_name': 'Resolution', 'field_type': 'string', 'description': '解决方案'},

            # 项目相关字段
            {'field_name': 'project_key', 'csv_name': 'Project key', 'field_type': 'string', 'description': '项目键'},
            {'field_name': 'project_name', 'csv_name': 'Project name', 'field_type': 'string', 'description': '项目名称'},
            {'field_name': 'project_type', 'csv_name': 'Project type', 'field_type': 'string', 'description': '项目类型'},
            {'field_name': 'project_lead', 'csv_name': 'Project lead', 'field_type': 'string', 'description': '项目负责人'},

            # 人员相关字段
            {'field_name': 'assignee', 'csv_name': 'Assignee', 'field_type': 'string', 'description': '指派人'},
            {'field_name': 'reporter', 'csv_name': 'Reporter', 'field_type': 'string', 'description': '报告人'},
            {'field_name': 'creator', 'csv_name': 'Creator', 'field_type': 'string', 'description': '创建人'},

            # 时间相关字段
            {'field_name': 'created', 'csv_name': 'Created', 'field_type': 'datetime', 'description': '创建时间'},
            {'field_name': 'updated', 'csv_name': 'Updated', 'field_type': 'datetime', 'description': '更新时间'},
            {'field_name': 'resolved', 'csv_name': 'Resolved', 'field_type': 'datetime', 'description': '解决时间'},

            # 版本和组件
            {'field_name': 'affects_versions', 'csv_name': 'Affects Version/s', 'field_type': 'text', 'description': '影响版本'},
            {'field_name': 'components', 'csv_name': 'Component/s', 'field_type': 'text', 'description': '组件'},

            # 描述字段
            {'field_name': 'description', 'csv_name': 'Description', 'field_type': 'text', 'description': '描述'},
            {'field_name': 'environment', 'csv_name': 'Environment', 'field_type': 'text', 'description': '环境'},

            # 自定义字段
            {'field_name': 'device_model', 'csv_name': '机型', 'field_type': 'string', 'description': '设备机型'},
            {'field_name': 'bug_category_custom', 'csv_name': 'Bug category', 'field_type': 'string', 'description': '缺陷分类'},
            {'field_name': 'common_issue', 'csv_name': 'CommonIssue', 'field_type': 'string', 'description': '共性问题'},
            {'field_name': 'rd_owner', 'csv_name': 'RD owner', 'field_type': 'string', 'description': 'RD负责人'},
            {'field_name': 'root_cause', 'csv_name': '根因', 'field_type': 'text', 'description': '根本原因'},
            {'field_name': 'solution', 'csv_name': '解决方案', 'field_type': 'text', 'description': '解决方案'},

            # 分析结果字段
            {'field_name': 'project_extracted', 'csv_name': 'project', 'field_type': 'string', 'description': '提取的项目'},
            {'field_name': 'summary_text', 'csv_name': 'summary_text', 'field_type': 'text', 'description': '摘要文本'},
            {'field_name': 'bug_category', 'csv_name': 'bug_category', 'field_type': 'string', 'description': '自动分类结果'},
            {'field_name': 'commonality', 'csv_name': 'commonality', 'field_type': 'string', 'description': '共性问题分析'},

            # 系统字段
            {'field_name': 'import_batch_id', 'csv_name': 'import_batch_id', 'field_type': 'string', 'description': '导入批次ID'},
            {'field_name': 'import_timestamp', 'csv_name': 'import_timestamp', 'field_type': 'datetime', 'description': '导入时间'},
        ]

        return fields

    def _get_basic_export_fields(self):
        """获取基础导出字段"""
        return [field for field in self._get_all_export_fields() if field['field_name'] in [
            'issue_key', 'summary', 'status', 'priority', 'assignee', 'created',
            'project_extracted', 'bug_category', 'commonality'
        ]]

    def _get_field_value(self, defect, field_name, field_type):
        """获取字段值并格式化"""
        try:
            value = getattr(defect, field_name, '')

            if value is None:
                return ''

            if field_type == 'datetime' and hasattr(value, 'strftime'):
                return value.strftime('%Y/%m/%d %H:%M:%S')
            elif field_type == 'date' and hasattr(value, 'strftime'):
                return value.strftime('%Y/%m/%d')
            else:
                return str(value) if value else ''

        except Exception:
            return ''

### 4.3 Celery异步任务
```python
# tasks.py
from celery import shared_task
from django.contrib.auth.models import User
from .models import TaskQueue, ImportBatch
from .services import DefectDataImportService
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True)
def process_data_import(self, task_id):
    """处理数据导入任务"""
    try:
        # 获取任务信息
        task = TaskQueue.objects.get(task_id=task_id)
        task.status = 'running'
        task.started_at = timezone.now()
        task.save()

        # 获取任务参数
        file_path = task.task_params['file_path']
        batch_id = task.task_params['batch_id']
        user = task.created_by

        # 创建导入服务实例
        import_service = DefectDataImportService(user, task_id)

        # 执行导入
        result = import_service.process_import_async(file_path, batch_id)

        # 更新任务状态
        task.status = 'completed'
        task.completed_at = timezone.now()
        task.result_data = result
        task.save()

        logger.info(f"数据导入任务 {task_id} 完成")
        return result

    except Exception as e:
        logger.error(f"数据导入任务 {task_id} 失败: {str(e)}")

        # 更新任务状态
        TaskQueue.objects.filter(task_id=task_id).update(
            status='failed',
            error_message=str(e),
            completed_at=timezone.now()
        )

        raise

@shared_task
def cleanup_completed_tasks():
    """清理已完成的任务（定期执行）"""
    from datetime import timedelta
    from django.utils import timezone

    # 删除7天前的已完成任务
    cutoff_date = timezone.now() - timedelta(days=7)

    completed_tasks = TaskQueue.objects.filter(
        status__in=['completed', 'failed', 'cancelled'],
        completed_at__lt=cutoff_date
    )

    count = completed_tasks.count()
    completed_tasks.delete()

    logger.info(f"清理了 {count} 个已完成的任务")
    return count

### 4.4 WebSocket通知服务
```python
class NotificationService:
    """实时通知服务"""

    def __init__(self):
        self.channel_layer = get_channel_layer()

    def send_user_notification(self, user, notification_type, title, message, data=None):
        """发送用户通知"""
        notification_data = {
            'type': 'user_notification',
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'timestamp': timezone.now().isoformat(),
            'data': data or {}
        }

        # 发送到用户专用频道
        user_channel = f"user_{user.id}"

        async_to_sync(self.channel_layer.group_send)(
            user_channel,
            {
                'type': 'send_notification',
                'notification': notification_data
            }
        )

        # 同时保存到数据库（可选）
        self._save_notification_to_db(user, notification_data)

    def send_progress_update(self, user, task_id, progress, step_description):
        """发送进度更新"""
        progress_data = {
            'type': 'progress_update',
            'task_id': task_id,
            'progress': progress,
            'step_description': step_description,
            'timestamp': timezone.now().isoformat()
        }

        user_channel = f"user_{user.id}"

        async_to_sync(self.channel_layer.group_send)(
            user_channel,
            {
                'type': 'send_progress',
                'progress': progress_data
            }
        )

    def _save_notification_to_db(self, user, notification_data):
        """保存通知到数据库"""
        # 可以创建一个Notification模型来保存历史通知
        pass

# WebSocket消费者
class NotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket通知消费者"""

    async def connect(self):
        self.user = self.scope["user"]
        if self.user.is_authenticated:
            self.user_group_name = f"user_{self.user.id}"

            # 加入用户组
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )

            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

    async def send_notification(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'data': event['notification']
        }))

    async def send_progress(self, event):
        """发送进度更新"""
        await self.send(text_data=json.dumps({
            'type': 'progress',
            'data': event['progress']
        }))
```
```

### 4.2 分类规则管理服务
```python
class ClassificationRuleService:
    """分类规则管理服务"""

    def __init__(self, user):
        self.user = user
        self.permission_service = DataPermissionService(user)

    def import_rules_from_file(self, file_path, batch_name=None):
        """从文件导入分类规则"""
        import uuid
        from datetime import datetime

        # 创建规则导入批次ID
        batch_id = f"rules_{self.user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        try:
            # 读取规则文件（支持Excel、CSV、JSON格式）
            rules_data = self._read_rules_file(file_path)

            # 验证规则数据
            validated_rules = self._validate_rules_data(rules_data)

            # 保存规则
            created_rules = []
            for rule_data in validated_rules:
                rule = ClassificationRule.objects.create(
                    rule_type=rule_data['rule_type'],
                    rule_name=rule_data['rule_name'],
                    pattern=rule_data['pattern'],
                    target_value=rule_data['target_value'],
                    priority=rule_data.get('priority', 0),
                    description=rule_data.get('description', ''),
                    scope='personal',  # 普通用户导入的规则默认为个人规则
                    created_by=self.user,
                    import_batch_id=batch_id,
                    source_file_name=os.path.basename(file_path)
                )
                created_rules.append(rule)

            return {
                'batch_id': batch_id,
                'total_rules': len(created_rules),
                'success_count': len(created_rules),
                'rules': created_rules
            }

        except Exception as e:
            raise ValueError(f"规则导入失败: {str(e)}")

    def export_rules_to_file(self, file_format='excel', rule_scope='personal'):
        """导出分类规则到文件"""
        # 获取用户可访问的规则
        rules = self.get_accessible_rules(rule_scope)

        if file_format == 'excel':
            return self._export_rules_to_excel(rules)
        elif file_format == 'csv':
            return self._export_rules_to_csv(rules)
        elif file_format == 'json':
            return self._export_rules_to_json(rules)
        else:
            raise ValueError(f"不支持的导出格式: {file_format}")

    def get_accessible_rules(self, scope=None):
        """获取用户可访问的规则"""
        queryset = ClassificationRule.objects.filter(is_active=True)

        if self.permission_service.is_admin:
            # 管理员可以看到所有规则
            if scope:
                queryset = queryset.filter(scope=scope)
        else:
            # 普通用户只能看到全局规则和自己的个人规则
            queryset = queryset.filter(
                models.Q(scope='global') |
                models.Q(scope='personal', created_by=self.user)
            )
            if scope == 'personal':
                queryset = queryset.filter(scope='personal', created_by=self.user)
            elif scope == 'global':
                queryset = queryset.filter(scope='global')

        return queryset.order_by('scope', '-priority', 'rule_type')

    def create_rule(self, rule_data):
        """创建分类规则"""
        # 检查权限
        scope = rule_data.get('scope', 'personal')
        if scope == 'global' and not self.permission_service.can_manage_rules():
            raise PermissionError("没有创建全局规则的权限")

        rule = ClassificationRule.objects.create(
            **rule_data,
            created_by=self.user,
            scope=scope
        )
        return rule

    def update_rule(self, rule_id, rule_data):
        """更新分类规则"""
        try:
            rule = ClassificationRule.objects.get(id=rule_id)
            if not rule.can_edit(self.user):
                raise PermissionError("没有编辑此规则的权限")

            for key, value in rule_data.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            rule.save()
            return rule

        except ClassificationRule.DoesNotExist:
            raise ValueError("规则不存在")

    def delete_rule(self, rule_id):
        """删除分类规则"""
        try:
            rule = ClassificationRule.objects.get(id=rule_id)
            if not rule.can_edit(self.user):
                raise PermissionError("没有删除此规则的权限")

            rule.delete()
            return True

        except ClassificationRule.DoesNotExist:
            raise ValueError("规则不存在")

    def test_rule(self, pattern, test_text):
        """测试分类规则"""
        import re
        try:
            if re.search(pattern, test_text, re.IGNORECASE):
                return {'match': True, 'message': '匹配成功'}
            else:
                return {'match': False, 'message': '未匹配'}
        except re.error as e:
            return {'match': False, 'message': f'正则表达式错误: {str(e)}'}

    def _read_rules_file(self, file_path):
        """读取规则文件"""
        import pandas as pd
        import json

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
            return df.to_dict('records')
        elif file_ext == '.csv':
            df = pd.read_csv(file_path)
            return df.to_dict('records')
        elif file_ext == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _validate_rules_data(self, rules_data):
        """验证规则数据"""
        required_fields = ['rule_type', 'rule_name', 'pattern', 'target_value']
        validated_rules = []

        for i, rule_data in enumerate(rules_data):
            # 检查必需字段
            for field in required_fields:
                if field not in rule_data or not rule_data[field]:
                    raise ValueError(f"第{i+1}行缺少必需字段: {field}")

            # 验证规则类型
            if rule_data['rule_type'] not in ['project', 'category', 'commonality']:
                raise ValueError(f"第{i+1}行规则类型无效: {rule_data['rule_type']}")

            # 验证正则表达式
            try:
                import re
                re.compile(rule_data['pattern'])
            except re.error:
                raise ValueError(f"第{i+1}行正则表达式无效: {rule_data['pattern']}")

            validated_rules.append(rule_data)

        return validated_rules

### 4.3 自动分类服务
```python
class AutoClassificationService:
    """自动分类服务"""

    def __init__(self, user):
        self.user = user
        self.rule_service = ClassificationRuleService(user)

    def extract_project(self, title):
        """从标题提取项目信息"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='project')

        for rule in rules:
            if self._match_pattern(rule.pattern, title):
                return rule.target_value

        return "未知项目"

    def extract_summary_text(self, title):
        """从标题提取摘要文本"""
        # 简单的摘要提取逻辑，可以根据需要扩展
        return title[:100] if len(title) > 100 else title

    def classify_bug_category(self, title, description=""):
        """缺陷分类"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='category')

        text_to_match = f"{title} {description}".strip()

        for rule in rules:
            if self._match_pattern(rule.pattern, text_to_match):
                return rule.target_value

        return "未分类"

    def identify_commonality(self, bug_category):
        """共性问题识别"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='commonality')

        for rule in rules:
            if self._match_pattern(rule.pattern, bug_category):
                return rule.target_value

        return "无共性"

    def batch_classify(self, defect_records):
        """批量分类"""
        results = []
        for record in defect_records:
            classification = {
                'project': self.extract_project(record.original_title),
                'summary_text': self.extract_summary_text(record.original_title),
                'bug_category': self.classify_bug_category(record.original_title),
            }
            classification['commonality'] = self.identify_commonality(classification['bug_category'])
            results.append(classification)

        return results

    def _match_pattern(self, pattern, text):
        """匹配模式"""
        import re
        try:
            return bool(re.search(pattern, text, re.IGNORECASE))
        except re.error:
            return False
```

### 4.4 数据权限管理服务
```python
class DataPermissionService:
    """数据权限管理服务"""

    def __init__(self, user):
        self.user = user
        self.user_profile = UserProfile.objects.get_or_create(user=user)[0]
        self.is_admin = self.user_profile.role == 'admin' or self.user_profile.can_view_all_data

    def get_accessible_defects(self, queryset=None):
        """获取用户可访问的缺陷数据"""
        if queryset is None:
            queryset = DefectRecord.objects.all()

        if self.is_admin:
            return queryset
        else:
            return queryset.filter(uploaded_by=self.user)

    def get_accessible_batches(self, queryset=None):
        """获取用户可访问的导入批次"""
        if queryset is None:
            queryset = ImportBatch.objects.all()

        if self.is_admin:
            return queryset
        else:
            return queryset.filter(uploaded_by=self.user)

    def can_view_defect(self, defect_record):
        """检查是否可以查看特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_edit_defect(self, defect_record):
        """检查是否可以编辑特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_delete_defect(self, defect_record):
        """检查是否可以删除特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_manage_rules(self):
        """检查是否可以管理分类规则"""
        return self.is_admin or self.user_profile.can_manage_rules

    def can_export_data(self):
        """检查是否可以导出数据"""
        return self.user_profile.can_export_data

### 4.5 统计分析服务
```python
class DefectStatisticsService:
    """缺陷统计分析服务"""

    def __init__(self, user):
        self.user = user
        self.permission_service = DataPermissionService(user)

    def get_abc_summary(self, date_range=None, import_batch_id=None):
        """A、B、C类问题汇总"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 统计逻辑
        summary = {
            'A类问题': queryset.filter(priority__in=['Blocker', 'Critical']).count(),
            'B类问题': queryset.filter(priority='Major').count(),
            'C类问题': queryset.filter(priority__in=['Minor', 'Trivial']).count(),
        }
        return summary

    def get_model_defect_summary(self, date_range=None, import_batch_id=None):
        """机型缺陷汇总"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按项目统计
        from django.db.models import Count
        summary = queryset.values('project').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(summary)

    def get_daily_trend(self, date_range=None, import_batch_id=None):
        """每日缺陷提报趋势"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按日期统计
        from django.db.models import Count
        trend = queryset.values('created_date').annotate(
            defect_count=Count('id')
        ).order_by('created_date')

        return list(trend)

    def get_category_distribution(self, date_range=None, import_batch_id=None):
        """缺陷分类分布"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按分类统计
        from django.db.models import Count
        distribution = queryset.values('bug_category').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(distribution)

    def get_commonality_analysis(self, date_range=None, import_batch_id=None):
        """共性问题分析"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按共性问题统计
        from django.db.models import Count
        analysis = queryset.values('commonality').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(analysis)

    def export_analysis_report(self, analysis_type, date_range=None, import_batch_id=None):
        """导出分析报告"""
        if not self.permission_service.can_export_data():
            raise PermissionError("没有数据导出权限")

        # 根据分析类型生成报告
        if analysis_type == 'abc_summary':
            data = self.get_abc_summary(date_range, import_batch_id)
        elif analysis_type == 'model_summary':
            data = self.get_model_defect_summary(date_range, import_batch_id)
        elif analysis_type == 'daily_trend':
            data = self.get_daily_trend(date_range, import_batch_id)
        elif analysis_type == 'category_distribution':
            data = self.get_category_distribution(date_range, import_batch_id)
        elif analysis_type == 'commonality_analysis':
            data = self.get_commonality_analysis(date_range, import_batch_id)
        else:
            raise ValueError(f"不支持的分析类型: {analysis_type}")

        return data

    def export_batch_data(self, import_batch_id):
        """导出指定批次的原始数据"""
        if not self.permission_service.can_export_data():
            raise PermissionError("没有数据导出权限")

        # 检查批次权限
        try:
            batch = ImportBatch.objects.get(batch_id=import_batch_id)
            if not self.permission_service.is_admin and batch.uploaded_by != self.user:
                raise PermissionError("没有权限访问该批次数据")
        except ImportBatch.DoesNotExist:
            raise ValueError("批次不存在")

        # 获取批次数据
        queryset = DefectRecord.objects.filter(import_batch_id=import_batch_id)
        return queryset
```

## 5. API接口设计

### 5.1 缺陷管理接口
```python
# 缺陷数据管理
POST /api/defects/import/          # 异步导入缺陷数据（返回任务ID）
    # 支持部分字段导入，自动字段映射
GET  /api/defects/list/            # 获取缺陷列表（支持权限过滤）
PUT  /api/defects/<id>/            # 更新缺陷信息（权限检查）
DELETE /api/defects/<id>/          # 删除缺陷（权限检查）
POST /api/defects/batch-classify/  # 批量分类
GET  /api/defects/export/          # 导出缺陷数据（全量字段导出）
    # 参数: format=excel|csv, include_all_fields=true|false
GET  /api/defects/batch/<batch_id>/export/ # 导出指定批次数据（全量字段）
GET  /api/defects/fields/          # 获取支持的字段列表
POST /api/defects/validate-headers/ # 验证CSV表头字段

# 导入批次管理
GET  /api/import-batches/          # 获取导入批次列表（权限过滤）
GET  /api/import-batches/<batch_id>/ # 获取批次详情
DELETE /api/import-batches/<batch_id>/ # 删除批次（权限检查）
GET  /api/import-batches/<batch_id>/defects/ # 获取批次下的缺陷数据

# 任务队列管理
POST /api/tasks/submit/            # 提交异步任务
GET  /api/tasks/                   # 获取用户任务列表
GET  /api/tasks/<task_id>/         # 获取任务详情和状态
DELETE /api/tasks/<task_id>/       # 取消任务
GET  /api/tasks/<task_id>/progress/ # 获取任务进度
GET  /api/tasks/queue-status/      # 获取队列状态

# 分类规则管理
GET  /api/classification-rules/    # 获取分类规则（权限过滤）
POST /api/classification-rules/    # 创建分类规则（权限检查）
PUT  /api/classification-rules/<id>/ # 更新分类规则（权限检查）
DELETE /api/classification-rules/<id>/ # 删除分类规则（权限检查）
POST /api/classification-rules/test/ # 测试分类规则
POST /api/classification-rules/import/ # 导入分类规则（普通用户可用）
GET  /api/classification-rules/export/ # 导出分类规则
GET  /api/classification-rules/template/ # 下载规则模板文件

# 实时通知
WebSocket /ws/notifications/       # WebSocket连接获取实时通知
GET  /api/notifications/           # 获取历史通知列表
PUT  /api/notifications/<id>/read/ # 标记通知为已读

# 字段映射管理
GET  /api/field-mappings/          # 获取字段映射配置
POST /api/field-mappings/          # 创建字段映射（管理员）
PUT  /api/field-mappings/<id>/     # 更新字段映射（管理员）
DELETE /api/field-mappings/<id>/   # 删除字段映射（管理员）
POST /api/field-mappings/init/     # 初始化默认字段映射（管理员）

# 用户权限管理（仅管理员）
GET  /api/users/                   # 获取用户列表
PUT  /api/users/<id>/permissions/  # 更新用户权限
GET  /api/users/profile/           # 获取当前用户配置
PUT  /api/users/profile/           # 更新当前用户配置
```

### 5.2 统计分析接口
```python
# 统计分析（支持权限过滤和批次筛选）
GET /api/analysis/abc-summary/         # A、B、C类问题汇总
    # 参数: date_range, import_batch_id
GET /api/analysis/model-summary/       # 机型缺陷汇总
    # 参数: date_range, import_batch_id
GET /api/analysis/daily-trend/         # 每日趋势
    # 参数: date_range, import_batch_id
GET /api/analysis/category-distribution/ # 分类分布
    # 参数: date_range, import_batch_id
GET /api/analysis/commonality/         # 共性问题分析
    # 参数: date_range, import_batch_id
GET /api/analysis/export/              # 导出报告（权限检查）
    # 参数: analysis_type, date_range, import_batch_id
GET /api/analysis/dashboard/           # 仪表板数据（权限过滤）
    # 参数: import_batch_id（可选，用于查看特定批次分析）

# 批次分析
GET /api/analysis/batch/<batch_id>/    # 获取指定批次的完整分析报告
```

### 5.3 权限控制说明
```python
# API权限控制装饰器示例
from functools import wraps
from django.http import JsonResponse

def permission_required(permission_type):
    """权限检查装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({'error': '需要登录'}, status=401)

            permission_service = DataPermissionService(request.user)

            if permission_type == 'manage_rules' and not permission_service.can_manage_rules():
                return JsonResponse({'error': '没有管理规则权限'}, status=403)

            if permission_type == 'export_data' and not permission_service.can_export_data():
                return JsonResponse({'error': '没有数据导出权限'}, status=403)

            # 将权限服务添加到request中，供视图使用
            request.permission_service = permission_service
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@permission_required('export_data')
def export_defects(request):
    """导出缺陷数据"""
    queryset = request.permission_service.get_accessible_defects()
    # 导出逻辑...

@permission_required('manage_rules')
def create_classification_rule(request):
    """创建分类规则"""
    # 创建规则逻辑...
```

## 6. 数据权限管理设计

### 6.1 权限模型设计

#### 6.1.1 用户角色
- **管理员(admin)**: 可以查看全量数据，管理所有用户和规则
- **普通用户(user)**: 只能查看自己上传的数据，有限的操作权限

#### 6.1.2 权限矩阵
| 功能 | 管理员 | 普通用户 | 说明 |
|------|--------|----------|------|
| 查看全部缺陷数据 | ✅ | ❌ | 普通用户只能看自己上传的数据 |
| 导入缺陷数据 | ✅ | ✅ | 都可以导入，但有配额限制 |
| 编辑缺陷数据 | ✅ | ✅* | 普通用户只能编辑自己上传的数据 |
| 删除缺陷数据 | ✅ | ✅* | 普通用户只能删除自己上传的数据 |
| 管理全局分类规则 | ✅ | ❌ | 只有管理员可以管理全局规则 |
| 导入个人分类规则 | ✅ | ✅ | 普通用户可以导入自己的分类规则 |
| 管理个人分类规则 | ✅ | ✅ | 普通用户可以管理自己的分类规则 |
| 导出数据 | ✅ | ✅* | 可配置，普通用户只能导出自己的数据 |
| 查看统计分析 | ✅ | ✅* | 普通用户只能看基于自己数据的分析 |
| 用户管理 | ✅ | ❌ | 只有管理员可以管理用户 |

### 6.2 导入批次标识设计

#### 6.2.1 批次ID生成规则
```python
# 批次ID格式: {用户名}_{日期时间}_{随机字符}
# 示例: zhangsan_20250702_143025_a1b2c3d4
batch_id = f"{username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
```

#### 6.2.2 批次管理功能
- **批次列表**: 显示用户的所有导入批次
- **批次详情**: 显示批次的导入统计和处理状态
- **批次数据**: 查看和导出特定批次的数据
- **批次分析**: 基于特定批次生成分析报告

### 6.3 数据隔离机制

#### 6.3.1 数据库层隔离
```python
# 在所有查询中自动添加用户过滤条件
class DefectQuerySet(models.QuerySet):
    def for_user(self, user):
        """根据用户权限过滤数据"""
        user_profile = UserProfile.objects.get_or_create(user=user)[0]
        if user_profile.role == 'admin' or user_profile.can_view_all_data:
            return self
        return self.filter(uploaded_by=user)

class DefectRecord(models.Model):
    # ... 字段定义 ...

    objects = DefectQuerySet.as_manager()
```

#### 6.3.2 API层权限控制
```python
# 在视图中自动应用权限过滤
class DefectListView(APIView):
    def get(self, request):
        permission_service = DataPermissionService(request.user)
        queryset = permission_service.get_accessible_defects()

        # 支持按批次筛选
        batch_id = request.GET.get('batch_id')
        if batch_id:
            queryset = queryset.filter(import_batch_id=batch_id)

        # 序列化和返回数据
        serializer = DefectSerializer(queryset, many=True)
        return Response(serializer.data)
```

### 6.4 配额和限制管理

#### 6.4.1 导入配额
- **文件大小限制**: 默认10MB，管理员可调整
- **记录数限制**: 默认10,000条，管理员可调整
- **并发导入限制**: 同时只能有一个导入任务

#### 6.4.2 存储配额
- **用户数据总量**: 可设置用户最大存储空间
- **批次数量限制**: 限制用户最大批次数量
- **数据保留期**: 可设置数据自动清理策略

### 6.5 分类规则导入功能设计

#### 6.5.1 规则导入格式支持

**Excel格式模板**:
| rule_type | rule_name | pattern | target_value | priority | description |
|-----------|-----------|---------|--------------|----------|-------------|
| project | Weather项目识别 | Weather\|天气 | Weather | 10 | 识别天气相关项目 |
| category | UI问题分类 | 界面\|显示\|布局\|UI | UI问题 | 5 | 识别界面显示类问题 |
| commonality | 界面共性问题 | UI问题 | 界面显示类 | 1 | UI问题的共性分类 |

**CSV格式示例**:
```csv
rule_type,rule_name,pattern,target_value,priority,description
project,Weather项目识别,Weather|天气,Weather,10,识别天气相关项目
category,UI问题分类,界面|显示|布局|UI,UI问题,5,识别界面显示类问题
commonality,界面共性问题,UI问题,界面显示类,1,UI问题的共性分类
```

**JSON格式示例**:
```json
[
  {
    "rule_type": "project",
    "rule_name": "Weather项目识别",
    "pattern": "Weather|天气",
    "target_value": "Weather",
    "priority": 10,
    "description": "识别天气相关项目"
  },
  {
    "rule_type": "category",
    "rule_name": "UI问题分类",
    "pattern": "界面|显示|布局|UI",
    "target_value": "UI问题",
    "priority": 5,
    "description": "识别界面显示类问题"
  }
]
```

#### 6.5.2 规则导入流程

```mermaid
flowchart TD
    A[用户选择规则文件] --> B{文件格式检查}

    B -->|Excel| C1[读取Excel文件]
    B -->|CSV| C2[读取CSV文件]
    B -->|JSON| C3[读取JSON文件]
    B -->|不支持| D[返回格式错误]

    C1 --> E[解析文件内容]
    C2 --> E
    C3 --> E

    E --> F[验证必需字段]
    F --> G{字段完整?}
    G -->|否| H[返回字段缺失错误]
    G -->|是| I[验证规则类型]

    I --> J{规则类型有效?}
    J -->|否| K[返回类型错误]
    J -->|是| L[验证正则表达式]

    L --> M{正则表达式有效?}
    M -->|否| N[返回正则错误]
    M -->|是| O[检查规则数量限制]

    O --> P{超过限制?}
    P -->|是| Q[返回数量超限错误]
    P -->|否| R[检查重复规则]

    R --> S{存在重复?}
    S -->|是| T[提示重复规则<br/>用户选择覆盖或跳过]
    S -->|否| U[生成导入批次ID]

    T --> V{用户选择}
    V -->|覆盖| W[标记覆盖现有规则]
    V -->|跳过| X[标记跳过重复规则]
    V -->|取消| Y[取消导入]

    W --> U
    X --> U

    U --> Z[批量创建规则记录]
    Z --> AA[更新规则统计]
    AA --> BB[生成导入报告]
    BB --> CC[导入完成]

    D --> DD[结束]
    H --> DD
    K --> DD
    N --> DD
    Q --> DD
    Y --> DD
    CC --> DD
```

**流程说明**:
1. **文件上传**: 用户选择规则文件（Excel/CSV/JSON）
2. **格式验证**: 检查文件格式和必需字段
3. **数据验证**: 验证规则类型、正则表达式有效性
4. **配额检查**: 检查规则数量是否超过用户限制
5. **冲突检查**: 检查是否与现有规则冲突
6. **用户确认**: 对于重复规则，用户选择处理方式
7. **批量导入**: 创建个人规则记录
8. **导入报告**: 显示导入结果和错误信息

#### 6.5.3 规则作用域和优先级
- **全局规则**: 管理员创建，对所有用户生效
- **个人规则**: 用户创建，只对自己生效
- **优先级顺序**: 个人规则 > 全局规则 > 系统默认规则
- **规则合并**: 同类型规则按优先级排序执行

#### 6.5.4 规则管理界面设计
```html
<!-- 规则导入界面 -->
<div class="rule-import-section">
    <h3>导入分类规则</h3>

    <!-- 模板下载 -->
    <div class="template-download">
        <label>下载模板:</label>
        <button onclick="downloadTemplate('excel')">Excel模板</button>
        <button onclick="downloadTemplate('csv')">CSV模板</button>
        <button onclick="downloadTemplate('json')">JSON模板</button>
    </div>

    <!-- 文件上传 -->
    <div class="file-upload">
        <input type="file" id="rule-file" accept=".xlsx,.xls,.csv,.json">
        <button onclick="importRules()">导入规则</button>
    </div>

    <!-- 导入预览 -->
    <div id="import-preview" style="display:none;">
        <h4>导入预览</h4>
        <table id="preview-table">
            <!-- 预览数据 -->
        </table>
        <button onclick="confirmImport()">确认导入</button>
        <button onclick="cancelImport()">取消</button>
    </div>
</div>

<!-- 规则列表 -->
<div class="rule-list-section">
    <h3>我的分类规则</h3>

    <!-- 规则筛选 -->
    <div class="rule-filter">
        <select id="scope-filter">
            <option value="all">全部规则</option>
            <option value="personal">个人规则</option>
            <option value="global">全局规则</option>
        </select>
        <select id="type-filter">
            <option value="all">全部类型</option>
            <option value="project">项目提取</option>
            <option value="category">缺陷分类</option>
            <option value="commonality">共性问题</option>
        </select>
    </div>

    <!-- 规则表格 -->
    <table id="rules-table">
        <thead>
            <tr>
                <th>规则名称</th>
                <th>类型</th>
                <th>匹配模式</th>
                <th>目标值</th>
                <th>优先级</th>
                <th>作用域</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 规则数据 -->
        </tbody>
    </table>
</div>
```

### 6.6 审计和日志

#### 6.5.1 操作日志
```python
class OperationLog(models.Model):
    """操作日志模型"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    operation_type = models.CharField(max_length=50)  # import, export, delete, etc.
    target_type = models.CharField(max_length=50)     # defect, batch, rule, etc.
    target_id = models.CharField(max_length=100)
    operation_detail = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
```

#### 6.5.2 数据访问日志
- 记录用户的数据访问行为
- 监控异常访问模式
- 支持数据访问审计

## 7. Vue3前端设计方案

### 7.1 项目结构
```
frontend/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── api/                    # API接口
│   │   ├── defects.ts
│   │   ├── tasks.ts
│   │   ├── rules.ts
│   │   ├── users.ts
│   │   └── index.ts
│   ├── components/             # 通用组件
│   │   ├── common/
│   │   │   ├── LoadingSpinner.vue
│   │   │   ├── ToastNotification.vue
│   │   │   └── ProgressBar.vue
│   │   ├── charts/
│   │   │   ├── DefectTrendChart.vue
│   │   │   ├── CategoryPieChart.vue
│   │   │   └── ModelBarChart.vue
│   │   └── upload/
│   │       ├── FileUpload.vue
│   │       ├── FieldMapping.vue
│   │       └── ImportProgress.vue
│   ├── views/                  # 页面组件
│   │   ├── DefectManagement.vue
│   │   ├── TaskManagement.vue
│   │   ├── BatchManagement.vue
│   │   ├── RuleManagement.vue
│   │   ├── AnalysisDashboard.vue
│   │   ├── FieldMapping.vue
│   │   └── UserManagement.vue
│   ├── stores/                 # Pinia状态管理
│   │   ├── defects.ts
│   │   ├── tasks.ts
│   │   ├── user.ts
│   │   ├── notifications.ts
│   │   └── index.ts
│   ├── composables/            # 组合式函数
│   │   ├── useWebSocket.ts
│   │   ├── useNotification.ts
│   │   ├── usePermission.ts
│   │   └── useFileUpload.ts
│   ├── types/                  # TypeScript类型定义
│   │   ├── defect.ts
│   │   ├── task.ts
│   │   ├── user.ts
│   │   └── api.ts
│   ├── utils/                  # 工具函数
│   │   ├── request.ts
│   │   ├── format.ts
│   │   ├── validation.ts
│   │   └── constants.ts
│   ├── router/                 # 路由配置
│   │   └── index.ts
│   ├── styles/                 # 样式文件
│   │   ├── global.scss
│   │   ├── variables.scss
│   │   └── components.scss
│   ├── App.vue
│   └── main.ts
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

### 7.2 Vue3组件设计

#### 7.2.1 缺陷数据管理页面 (DefectManagement.vue)
```vue
<template>
  <div class="defect-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>缺陷数据管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showImportDialog = true">
          <el-icon><Upload /></el-icon>
          导入数据
        </el-button>
        <el-button @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <el-form :model="filters" inline>
        <el-form-item label="批次">
          <el-select v-model="filters.batchId" placeholder="选择批次" clearable>
            <el-option
              v-for="batch in batches"
              :key="batch.id"
              :label="batch.name"
              :value="batch.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="打开" value="Open" />
            <el-option label="已解决" value="Resolved" />
            <el-option label="已关闭" value="Closed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadDefects">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="defects"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="issue_key" label="缺陷编号" width="120" />
      <el-table-column prop="summary" label="摘要" min-width="200" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="{ row }">
          <el-tag :type="getPriorityType(row.priority)">{{ row.priority }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="指派人" width="120" />
      <el-table-column prop="created" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
          <el-button size="small" type="primary" @click="editDefect(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="loadDefects"
      @current-change="loadDefects"
    />

    <!-- 导入对话框 -->
    <ImportDialog
      v-model:visible="showImportDialog"
      @success="handleImportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'
import { useDefectStore } from '@/stores/defects'
import { useBatchStore } from '@/stores/batches'
import type { Defect, DefectFilters } from '@/types/defect'

// 状态管理
const defectStore = useDefectStore()
const batchStore = useBatchStore()

// 响应式数据
const loading = ref(false)
const showImportDialog = ref(false)

const filters = reactive<DefectFilters>({
  batchId: '',
  status: '',
  priority: '',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 计算属性
const defects = computed(() => defectStore.defects)
const batches = computed(() => batchStore.batches)

// 方法实现
const loadDefects = async () => {
  loading.value = true
  try {
    await defectStore.fetchDefects({
      ...filters,
      page: pagination.page,
      size: pagination.size
    })
    pagination.total = defectStore.total
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadDefects()
  batchStore.fetchBatches()
})
</script>
```

#### 7.2.2 数据导入组件 (ImportDialog.vue)
```vue
<template>
  <el-dialog
    v-model="visible"
    title="数据导入"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="import-dialog">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center>
        <el-step title="选择文件" />
        <el-step title="字段验证" />
        <el-step title="确认导入" />
        <el-step title="导入完成" />
      </el-steps>

      <!-- 步骤1: 文件选择 -->
      <div v-if="currentStep === 0" class="step-content">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          :accept="'.xlsx,.xls,.csv'"
          :on-change="handleFileChange"
          :on-exceed="handleExceed"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .xlsx, .xls, .csv 格式，文件大小不超过50MB
            </div>
          </template>
        </el-upload>

        <el-form :model="importForm" label-width="100px" style="margin-top: 20px;">
          <el-form-item label="批次名称">
            <el-input
              v-model="importForm.batchName"
              placeholder="可选，默认使用文件名"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 步骤2: 字段验证 -->
      <div v-if="currentStep === 1" class="step-content">
        <div v-loading="validating" class="field-validation">
          <el-alert
            v-if="validationResult"
            :title="getValidationTitle()"
            :type="validationResult.valid ? 'success' : 'warning'"
            :closable="false"
            show-icon
          />

          <div v-if="validationResult" class="validation-details">
            <!-- 支持的字段 -->
            <el-card header="✅ 支持的字段" class="field-card">
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.supported_fields"
                  :key="field"
                  type="success"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
            </el-card>

            <!-- 未知字段 -->
            <el-card
              v-if="validationResult.unknown_fields?.length"
              header="⚠️ 未知字段"
              class="field-card"
            >
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.unknown_fields"
                  :key="field"
                  type="warning"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
              <p class="field-note">这些字段将被忽略</p>
            </el-card>

            <!-- 缺失字段 -->
            <el-card
              v-if="validationResult.missing_required_fields?.length"
              header="❌ 缺失必需字段"
              class="field-card"
            >
              <div class="field-tags">
                <el-tag
                  v-for="field in validationResult.missing_required_fields"
                  :key="field"
                  type="danger"
                  class="field-tag"
                >
                  {{ field }}
                </el-tag>
              </div>
              <p class="field-note">必须包含这些字段才能导入</p>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 步骤3: 确认导入 -->
      <div v-if="currentStep === 2" class="step-content">
        <el-descriptions title="导入信息确认" :column="2">
          <el-descriptions-item label="文件名">
            {{ selectedFile?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(selectedFile?.size || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="批次名称">
            {{ importForm.batchName || selectedFile?.name }}
          </el-descriptions-item>
          <el-descriptions-item label="支持字段数">
            {{ validationResult?.total_supported || 0 }}
          </el-descriptions-item>
        </el-descriptions>

        <el-alert
          title="确认导入后，系统将异步处理数据，您可以在任务管理页面查看进度"
          type="info"
          :closable="false"
        />
      </div>

      <!-- 步骤4: 导入完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <el-result
          icon="success"
          title="导入任务已提交"
          :sub-title="`任务ID: ${taskResult?.task_id}`"
        >
          <template #extra>
            <el-descriptions :column="2">
              <el-descriptions-item label="批次ID">
                {{ taskResult?.batch_id }}
              </el-descriptions-item>
              <el-descriptions-item label="队列位置">
                {{ taskResult?.queue_position }}
              </el-descriptions-item>
              <el-descriptions-item label="预估等待时间">
                {{ formatWaitTime(taskResult?.estimated_wait_time || 0) }}
              </el-descriptions-item>
            </el-descriptions>
          </template>
        </el-result>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          v-if="currentStep > 0 && currentStep < 3"
          @click="prevStep"
        >
          上一步
        </el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!selectedFile"
          @click="validateFields"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="!validationResult?.valid"
          @click="nextStep"
        >
          下一步
        </el-button>
        <el-button
          v-if="currentStep === 2"
          type="primary"
          :loading="importing"
          @click="submitImport"
        >
          确认导入
        </el-button>
        <el-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleComplete"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { useFileUpload } from '@/composables/useFileUpload'
import type { UploadFile } from 'element-plus'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success', result: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { validateFile, submitImportTask } = useFileUpload()

// 响应式数据
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const validating = ref(false)
const importing = ref(false)
const validationResult = ref<any>(null)
const taskResult = ref<any>(null)

const importForm = reactive({
  batchName: ''
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleFileChange = (file: UploadFile) => {
  selectedFile.value = file.raw || null
  importForm.batchName = file.name?.replace(/\.[^/.]+$/, '') || ''
}

const handleExceed = () => {
  ElMessage.warning('只能选择一个文件')
}

const validateFields = async () => {
  if (!selectedFile.value) return

  validating.value = true
  try {
    validationResult.value = await validateFile(selectedFile.value)
    currentStep.value = 1
  } catch (error) {
    ElMessage.error('字段验证失败')
  } finally {
    validating.value = false
  }
}

const submitImport = async () => {
  if (!selectedFile.value) return

  importing.value = true
  try {
    taskResult.value = await submitImportTask(
      selectedFile.value,
      importForm.batchName
    )
    currentStep.value = 3
    emit('success', taskResult.value)
  } catch (error) {
    ElMessage.error('提交导入任务失败')
  } finally {
    importing.value = false
  }
}

const nextStep = () => {
  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const handleCancel = () => {
  resetDialog()
  visible.value = false
}

const handleComplete = () => {
  resetDialog()
  visible.value = false
}

const resetDialog = () => {
  currentStep.value = 0
  selectedFile.value = null
  validationResult.value = null
  taskResult.value = null
  importForm.batchName = ''
}

// 工具方法
const getValidationTitle = () => {
  if (!validationResult.value) return ''

  if (validationResult.value.valid) {
    return `字段验证通过 (${validationResult.value.total_supported}/${validationResult.value.total_fields})`
  } else {
    return `字段验证失败，缺少必需字段`
  }
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const formatWaitTime = (seconds: number) => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.ceil(seconds / 60)}分钟`
  return `${Math.ceil(seconds / 3600)}小时`
}
</script>
```

### 7.3 状态管理 (Pinia Stores)

#### 7.3.1 缺陷数据状态管理 (stores/defects.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { defectApi } from '@/api/defects'
import type { Defect, DefectFilters, PaginationParams } from '@/types/defect'

export const useDefectStore = defineStore('defects', () => {
  // 状态
  const defects = ref<Defect[]>([])
  const total = ref(0)
  const loading = ref(false)
  const currentDefect = ref<Defect | null>(null)

  // 计算属性
  const defectCount = computed(() => defects.value.length)
  const hasDefects = computed(() => defects.value.length > 0)

  // 按状态分组的缺陷
  const defectsByStatus = computed(() => {
    const groups: Record<string, Defect[]> = {}
    defects.value.forEach(defect => {
      const status = defect.status || 'Unknown'
      if (!groups[status]) {
        groups[status] = []
      }
      groups[status].push(defect)
    })
    return groups
  })

  // 按优先级分组的缺陷
  const defectsByPriority = computed(() => {
    const groups: Record<string, Defect[]> = {}
    defects.value.forEach(defect => {
      const priority = defect.priority || 'Unknown'
      if (!groups[priority]) {
        groups[priority] = []
      }
      groups[priority].push(defect)
    })
    return groups
  })

  // Actions
  const fetchDefects = async (params: DefectFilters & PaginationParams) => {
    loading.value = true
    try {
      const response = await defectApi.getDefects(params)
      defects.value = response.data.results
      total.value = response.data.count
      return response.data
    } catch (error) {
      console.error('Failed to fetch defects:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchDefectById = async (id: string) => {
    try {
      const response = await defectApi.getDefectById(id)
      currentDefect.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch defect:', error)
      throw error
    }
  }

  const updateDefect = async (id: string, data: Partial<Defect>) => {
    try {
      const response = await defectApi.updateDefect(id, data)
      const index = defects.value.findIndex(d => d.id === id)
      if (index !== -1) {
        defects.value[index] = response.data
      }
      if (currentDefect.value?.id === id) {
        currentDefect.value = response.data
      }
      return response.data
    } catch (error) {
      console.error('Failed to update defect:', error)
      throw error
    }
  }

  const deleteDefect = async (id: string) => {
    try {
      await defectApi.deleteDefect(id)
      const index = defects.value.findIndex(d => d.id === id)
      if (index !== -1) {
        defects.value.splice(index, 1)
        total.value--
      }
      if (currentDefect.value?.id === id) {
        currentDefect.value = null
      }
    } catch (error) {
      console.error('Failed to delete defect:', error)
      throw error
    }
  }

  const exportDefects = async (filters: DefectFilters, format: 'excel' | 'csv' = 'excel') => {
    try {
      const response = await defectApi.exportDefects(filters, format)

      // 创建下载链接
      const blob = new Blob([response.data], {
        type: format === 'excel'
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `defects_export_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`
      link.click()

      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to export defects:', error)
      throw error
    }
  }

  const batchClassify = async (defectIds: string[]) => {
    try {
      const response = await defectApi.batchClassify(defectIds)
      // 更新本地数据
      response.data.forEach((updatedDefect: Defect) => {
        const index = defects.value.findIndex(d => d.id === updatedDefect.id)
        if (index !== -1) {
          defects.value[index] = updatedDefect
        }
      })
      return response.data
    } catch (error) {
      console.error('Failed to batch classify:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    defects.value = []
    total.value = 0
    currentDefect.value = null
    loading.value = false
  }

  return {
    // 状态
    defects,
    total,
    loading,
    currentDefect,

    // 计算属性
    defectCount,
    hasDefects,
    defectsByStatus,
    defectsByPriority,

    // Actions
    fetchDefects,
    fetchDefectById,
    updateDefect,
    deleteDefect,
    exportDefects,
    batchClassify,
    resetState
  }
})
```

#### 7.3.2 任务管理状态 (stores/tasks.ts)
```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi } from '@/api/tasks'
import type { Task, TaskFilters } from '@/types/task'

export const useTaskStore = defineStore('tasks', () => {
  // 状态
  const tasks = ref<Task[]>([])
  const currentTask = ref<Task | null>(null)
  const loading = ref(false)

  // 计算属性
  const runningTasks = computed(() =>
    tasks.value.filter(task => task.status === 'running')
  )

  const pendingTasks = computed(() =>
    tasks.value.filter(task => task.status === 'pending')
  )

  const completedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'completed')
  )

  const failedTasks = computed(() =>
    tasks.value.filter(task => task.status === 'failed')
  )

  const taskStats = computed(() => ({
    total: tasks.value.length,
    running: runningTasks.value.length,
    pending: pendingTasks.value.length,
    completed: completedTasks.value.length,
    failed: failedTasks.value.length
  }))

  // Actions
  const fetchTasks = async (filters?: TaskFilters) => {
    loading.value = true
    try {
      const response = await taskApi.getTasks(filters)
      tasks.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchTaskById = async (taskId: string) => {
    try {
      const response = await taskApi.getTaskById(taskId)
      currentTask.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch task:', error)
      throw error
    }
  }

  const cancelTask = async (taskId: string) => {
    try {
      await taskApi.cancelTask(taskId)
      const task = tasks.value.find(t => t.id === taskId)
      if (task) {
        task.status = 'cancelled'
      }
    } catch (error) {
      console.error('Failed to cancel task:', error)
      throw error
    }
  }

  const updateTaskProgress = (taskId: string, progress: number, step: string) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.progress = progress
      task.current_step = step
    }

    if (currentTask.value?.id === taskId) {
      currentTask.value.progress = progress
      currentTask.value.current_step = step
    }
  }

  const updateTaskStatus = (taskId: string, status: string, result?: any) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = status
      if (result) {
        task.result_data = result
      }
      if (status === 'completed' || status === 'failed') {
        task.completed_at = new Date().toISOString()
      }
    }

    if (currentTask.value?.id === taskId) {
      currentTask.value.status = status
      if (result) {
        currentTask.value.result_data = result
      }
    }
  }

  const addTask = (task: Task) => {
    tasks.value.unshift(task)
  }

  const removeTask = (taskId: string) => {
    const index = tasks.value.findIndex(t => t.id === taskId)
    if (index !== -1) {
      tasks.value.splice(index, 1)
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,

    // 计算属性
    runningTasks,
    pendingTasks,
    completedTasks,
    failedTasks,
    taskStats,

    // Actions
    fetchTasks,
    fetchTaskById,
    cancelTask,
    updateTaskProgress,
    updateTaskStatus,
    addTask,
    removeTask
  }
})
```

### 7.4 组合式函数 (Composables)

#### 7.4.1 WebSocket通信 (composables/useWebSocket.ts)
```typescript
import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTaskStore } from '@/stores/tasks'
import { useNotificationStore } from '@/stores/notifications'

export function useWebSocket() {
  const socket = ref<WebSocket | null>(null)
  const connected = ref(false)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = 5

  const taskStore = useTaskStore()
  const notificationStore = useNotificationStore()

  const connect = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`

    socket.value = new WebSocket(wsUrl)

    socket.value.onopen = () => {
      console.log('WebSocket连接已建立')
      connected.value = true
      reconnectAttempts.value = 0
    }

    socket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    socket.value.onclose = () => {
      console.log('WebSocket连接已关闭')
      connected.value = false
      attemptReconnect()
    }

    socket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
    }
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    connected.value = false
  }

  const handleMessage = (data: any) => {
    switch (data.type) {
      case 'notification':
        handleNotification(data.data)
        break
      case 'progress':
        handleProgressUpdate(data.data)
        break
      case 'task_status':
        handleTaskStatusUpdate(data.data)
        break
      default:
        console.log('未知消息类型:', data.type)
    }
  }

  const handleNotification = (notification: any) => {
    // 添加到通知存储
    notificationStore.addNotification(notification)

    // 显示Toast通知
    const messageType = getMessageType(notification.notification_type)
    ElMessage({
      type: messageType,
      title: notification.title,
      message: notification.message,
      duration: 5000,
      showClose: true
    })

    // 处理特定类型的通知
    if (notification.notification_type === 'import_completed') {
      handleImportCompleted(notification)
    } else if (notification.notification_type === 'import_failed') {
      handleImportFailed(notification)
    }
  }

  const handleProgressUpdate = (progress: any) => {
    taskStore.updateTaskProgress(
      progress.task_id,
      progress.progress,
      progress.step_description
    )
  }

  const handleTaskStatusUpdate = (taskUpdate: any) => {
    taskStore.updateTaskStatus(
      taskUpdate.task_id,
      taskUpdate.status,
      taskUpdate.result
    )
  }

  const handleImportCompleted = (notification: any) => {
    // 可以触发数据刷新等操作
    console.log('导入完成:', notification.data)
  }

  const handleImportFailed = (notification: any) => {
    console.error('导入失败:', notification.data)
  }

  const getMessageType = (notificationType: string) => {
    const typeMap: Record<string, string> = {
      'import_completed': 'success',
      'import_failed': 'error',
      'import_started': 'info',
      'task_queued': 'info',
      'task_cancelled': 'warning'
    }
    return typeMap[notificationType] || 'info'
  }

  const attemptReconnect = () => {
    if (reconnectAttempts.value < maxReconnectAttempts) {
      reconnectAttempts.value++
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000)

      setTimeout(() => {
        console.log(`尝试重连 (${reconnectAttempts.value}/${maxReconnectAttempts})`)
        connect()
      }, delay)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
      ElMessage.error('网络连接断开，请刷新页面重试')
    }
  }

  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect()
  })

  return {
    connected,
    connect,
    disconnect,
    reconnectAttempts
  }
}
```

#### 7.4.2 文件上传 (composables/useFileUpload.ts)
```typescript
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { defectApi } from '@/api/defects'

export function useFileUpload() {
  const uploading = ref(false)
  const validating = ref(false)

  const validateFile = async (file: File) => {
    if (!file) {
      throw new Error('请选择文件')
    }

    // 文件大小检查
    const maxSize = 50 * 1024 * 1024 // 50MB
    if (file.size > maxSize) {
      throw new Error('文件大小不能超过50MB')
    }

    // 文件类型检查
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ]

    if (!allowedTypes.includes(file.type)) {
      throw new Error('只支持Excel和CSV文件')
    }

    validating.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await defectApi.validateHeaders(formData)
      return response.data
    } catch (error) {
      console.error('字段验证失败:', error)
      throw error
    } finally {
      validating.value = false
    }
  }

  const submitImportTask = async (file: File, batchName?: string) => {
    uploading.value = true
    try {
      const formData = new FormData()
      formData.append('file', file)
      if (batchName) {
        formData.append('batch_name', batchName)
      }

      const response = await defectApi.importDefects(formData)
      return response.data
    } catch (error) {
      console.error('提交导入任务失败:', error)
      throw error
    } finally {
      uploading.value = false
    }
  }

  const downloadTemplate = async (format: 'excel' | 'csv' = 'excel') => {
    try {
      const response = await defectApi.downloadTemplate(format)

      const blob = new Blob([response.data], {
        type: format === 'excel'
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `defect_import_template.${format === 'excel' ? 'xlsx' : 'csv'}`
      link.click()

      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
    }
  }

  return {
    uploading,
    validating,
    validateFile,
    submitImportTask,
    downloadTemplate
  }
}
```

### 7.5 API层设计

#### 7.5.1 缺陷数据API (api/defects.ts)
```typescript
import { request } from '@/utils/request'
import type { Defect, DefectFilters, PaginationParams } from '@/types/defect'

export const defectApi = {
  // 获取缺陷列表
  getDefects(params: DefectFilters & PaginationParams) {
    return request.get('/api/defects/list/', { params })
  },

  // 获取单个缺陷
  getDefectById(id: string) {
    return request.get(`/api/defects/${id}/`)
  },

  // 更新缺陷
  updateDefect(id: string, data: Partial<Defect>) {
    return request.put(`/api/defects/${id}/`, data)
  },

  // 删除缺陷
  deleteDefect(id: string) {
    return request.delete(`/api/defects/${id}/`)
  },

  // 导入缺陷数据
  importDefects(formData: FormData) {
    return request.post('/api/defects/import/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 验证CSV表头
  validateHeaders(formData: FormData) {
    return request.post('/api/defects/validate-headers/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 导出缺陷数据
  exportDefects(filters: DefectFilters, format: 'excel' | 'csv' = 'excel') {
    return request.get('/api/defects/export/', {
      params: { ...filters, format },
      responseType: 'blob'
    })
  },

  // 批量分类
  batchClassify(defectIds: string[]) {
    return request.post('/api/defects/batch-classify/', { defect_ids: defectIds })
  },

  // 下载导入模板
  downloadTemplate(format: 'excel' | 'csv' = 'excel') {
    return request.get('/api/defects/template/', {
      params: { format },
      responseType: 'blob'
    })
  },

  // 获取支持的字段列表
  getSupportedFields() {
    return request.get('/api/defects/fields/')
  }
}
```

#### 7.5.2 任务管理API (api/tasks.ts)
```typescript
import { request } from '@/utils/request'
import type { Task, TaskFilters } from '@/types/task'

export const taskApi = {
  // 获取任务列表
  getTasks(filters?: TaskFilters) {
    return request.get('/api/tasks/', { params: filters })
  },

  // 获取单个任务
  getTaskById(taskId: string) {
    return request.get(`/api/tasks/${taskId}/`)
  },

  // 取消任务
  cancelTask(taskId: string) {
    return request.delete(`/api/tasks/${taskId}/`)
  },

  // 获取任务进度
  getTaskProgress(taskId: string) {
    return request.get(`/api/tasks/${taskId}/progress/`)
  },

  // 获取队列状态
  getQueueStatus() {
    return request.get('/api/tasks/queue-status/')
  },

  // 提交任务
  submitTask(taskData: any) {
    return request.post('/api/tasks/submit/', taskData)
  }
}
```

### 7.6 TypeScript类型定义

#### 7.6.1 缺陷数据类型 (types/defect.ts)
```typescript
export interface Defect {
  id: string
  issue_key: string
  issue_id?: string
  summary: string
  issue_type?: string
  status: string
  priority: string
  resolution?: string

  // 项目相关
  project_key?: string
  project_name?: string
  project_type?: string
  project_lead?: string

  // 人员相关
  assignee?: string
  reporter?: string
  creator?: string
  rd_owner?: string

  // 时间相关
  created?: string
  updated?: string
  resolved?: string
  due_date?: string

  // 版本和组件
  affects_versions?: string
  components?: string

  // 描述
  description?: string
  environment?: string

  // 自定义字段
  device_model?: string
  bug_category_custom?: string
  common_issue?: string
  root_cause?: string
  solution?: string

  // 分析结果
  project_extracted?: string
  summary_text?: string
  bug_category?: string
  commonality?: string

  // 系统字段
  import_batch_id: string
  import_timestamp: string
  uploaded_by: string
  created_at: string
  updated_at: string
}

export interface DefectFilters {
  batchId?: string
  status?: string
  priority?: string
  assignee?: string
  project?: string
  keyword?: string
  dateRange?: [string, string]
}

export interface PaginationParams {
  page: number
  size: number
}

export interface DefectListResponse {
  results: Defect[]
  count: number
  next?: string
  previous?: string
}
```

#### 7.6.2 任务类型 (types/task.ts)
```typescript
export interface Task {
  id: string
  task_id: string
  task_type: 'data_import' | 'data_analysis' | 'rule_import' | 'batch_export'
  task_name: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  current_step: string

  // 队列信息
  queue_position: number
  estimated_wait_time: number
  priority: number

  // 时间信息
  created_at: string
  started_at?: string
  completed_at?: string
  estimated_duration: number

  // 任务参数和结果
  task_params: Record<string, any>
  result_data: Record<string, any>
  error_message?: string

  // 关联信息
  created_by: string
  related_batch_id?: string
}

export interface TaskFilters {
  status?: string
  task_type?: string
  created_by?: string
}

export interface TaskStats {
  total: number
  running: number
  pending: number
  completed: number
  failed: number
}
```

#### 7.6.3 用户和权限类型 (types/user.ts)
```typescript
export interface User {
  id: string
  username: string
  email: string
  first_name: string
  last_name: string
  is_active: boolean
  is_staff: boolean
  is_superuser: boolean
  date_joined: string
  last_login?: string
}

export interface UserProfile {
  user: User
  role: 'admin' | 'user'
  can_view_all_data: boolean
  can_manage_rules: boolean
  can_export_data: boolean
  max_import_size: number
  max_records_per_import: number
  max_personal_rules: number
}

export interface Permission {
  id: string
  name: string
  codename: string
  content_type: string
}
```

### 7.7 项目配置文件

#### 7.7.1 Vite配置 (vite.config.ts)
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          charts: ['echarts']
        }
      }
    }
  }
})
```

#### 7.7.2 TypeScript配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

#### 7.7.3 包管理配置 (package.json)
```json
{
  "name": "defect-classification-frontend",
  "private": true,
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext vue,js,jsx,cjs,mjs,ts,tsx,cts,mts --fix",
    "type-check": "vue-tsc --noEmit"
  },
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0",
    "echarts": "^5.4.0",
    "vue-echarts": "^6.6.0",
    "dayjs": "^1.11.0",
    "lodash-es": "^4.17.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/lodash-es": "^4.17.0",
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/eslint-config-prettier": "^8.0.0",
    "@vue/eslint-config-typescript": "^12.0.0",
    "@vue/tsconfig": "^0.4.0",
    "eslint": "^8.0.0",
    "eslint-plugin-vue": "^9.0.0",
    "prettier": "^3.0.0",
    "sass": "^1.69.0",
    "typescript": "^5.0.0",
    "vite": "^5.0.0",
    "vue-tsc": "^1.8.0"
  }
}
```

### 7.8 Vue3前端特性和优势

#### 7.8.1 技术特性
- **Vue 3 Composition API**: 更好的逻辑复用和代码组织
- **TypeScript支持**: 完整的类型安全和开发体验
- **响应式设计**: 支持桌面端和移动端访问
- **组件化架构**: 高度可复用的组件设计
- **状态管理**: Pinia提供现代化的状态管理方案
- **实时通信**: WebSocket集成，支持实时数据更新

#### 7.8.2 用户体验特性
- **实时数据更新**: WebSocket推送任务状态和进度
- **交互式图表**: ECharts提供丰富的数据可视化
- **智能表单**: 自动验证和错误提示
- **拖拽上传**: 支持文件拖拽上传
- **进度可视化**: 实时进度条和状态指示器
- **Toast通知**: 优雅的消息提示系统

#### 7.8.3 业务功能特性
- **批次选择器**: 支持按导入批次筛选数据
- **权限控制**: 基于角色的界面权限控制
- **配额显示**: 实时显示用户使用情况和限制
- **智能字段映射**: 自动识别CSV字段并提供映射建议
- **字段验证**: 导入前验证字段完整性和格式
- **队列可视化**: 显示任务队列状态和等待时间
- **数据导出**: 支持多种格式的数据导出
- **搜索筛选**: 强大的数据筛选和搜索功能

#### 7.8.4 开发体验特性
- **热重载**: Vite提供快速的开发体验
- **代码分割**: 自动代码分割和懒加载
- **ESLint + Prettier**: 代码质量和格式化
- **TypeScript**: 完整的类型检查和智能提示
- **组合式函数**: 逻辑复用和测试友好
- **模块化设计**: 清晰的项目结构和依赖关系

### 7.3 用户体验设计

#### 7.3.1 权限友好提示
```javascript
// 前端权限检查示例
function checkUserPermissions() {
    fetch('/api/users/profile/')
        .then(response => response.json())
        .then(profile => {
            // 根据权限显示/隐藏功能
            if (!profile.can_manage_rules) {
                document.getElementById('rules-menu').style.display = 'none';
            }

            if (!profile.can_export_data) {
                document.querySelectorAll('.export-btn').forEach(btn => {
                    btn.disabled = true;
                    btn.title = '没有导出权限';
                });
            }

            // 显示配额信息
            updateQuotaDisplay(profile);
        });
}
```

#### 7.3.2 异步导入界面
```html
<!-- 数据导入界面 -->
<div class="import-section">
    <h3>数据导入</h3>

    <!-- 文件上传 -->
    <div class="file-upload">
        <input type="file" id="data-file" accept=".xlsx,.xls,.csv">
        <input type="text" id="batch-name" placeholder="批次名称（可选）">
        <button onclick="submitImportTask()" id="import-btn">开始导入</button>
    </div>

    <!-- 当前任务状态 -->
    <div id="current-task" style="display:none;">
        <h4>当前导入任务</h4>
        <div class="task-info">
            <div class="task-status">
                <span id="task-status-text">排队中</span>
                <span id="queue-position"></span>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" style="width: 0%"></div>
                </div>
                <span id="progress-text">0%</span>
            </div>
            <div id="current-step">等待处理...</div>
            <button onclick="cancelTask()" id="cancel-btn">取消任务</button>
        </div>
    </div>
</div>

<!-- 任务历史 -->
<div class="task-history">
    <h3>导入历史</h3>
    <table id="tasks-table">
        <thead>
            <tr>
                <th>任务名称</th>
                <th>状态</th>
                <th>进度</th>
                <th>创建时间</th>
                <th>完成时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 任务数据 -->
        </tbody>
    </table>
</div>

<!-- 批次选择器 -->
<div class="batch-selector">
    <label>选择导入批次:</label>
    <select id="batch-select">
        <option value="">全部数据</option>
        <option value="batch_001">2025-07-02 14:30 - Excel导入 (1,234条) - 已完成</option>
        <option value="batch_002">2025-07-01 09:15 - CSV导入 (856条) - 处理中</option>
    </select>
    <button onclick="exportBatchData()">导出当前批次</button>
</div>
```

#### 7.3.3 WebSocket和实时通知实现
```javascript
// WebSocket连接管理
class NotificationManager {
    constructor() {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.currentTaskId = null;
    }

    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;

        this.socket = new WebSocket(wsUrl);

        this.socket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.reconnectAttempts = 0;
        };

        this.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.socket.onclose = () => {
            console.log('WebSocket连接已关闭');
            this.attemptReconnect();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    handleMessage(data) {
        if (data.type === 'notification') {
            this.showNotification(data.data);
        } else if (data.type === 'progress') {
            this.updateProgress(data.data);
        }
    }

    showNotification(notification) {
        // 显示Toast通知
        this.showToast(notification.title, notification.message, notification.notification_type);

        // 如果是导入完成通知，刷新页面数据
        if (notification.notification_type === 'import_completed') {
            this.handleImportCompleted(notification);
        } else if (notification.notification_type === 'import_failed') {
            this.handleImportFailed(notification);
        }
    }

    updateProgress(progress) {
        if (progress.task_id === this.currentTaskId) {
            // 更新进度条
            document.getElementById('progress-fill').style.width = `${progress.progress}%`;
            document.getElementById('progress-text').textContent = `${progress.progress.toFixed(1)}%`;
            document.getElementById('current-step').textContent = progress.step_description;
        }
    }

    showToast(title, message, type) {
        // 创建Toast通知
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button type="button" class="close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        // 添加到页面
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        toastContainer.appendChild(toast);

        // 自动消失
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        document.body.appendChild(container);
        return container;
    }

    handleImportCompleted(notification) {
        // 隐藏当前任务显示
        document.getElementById('current-task').style.display = 'none';
        this.currentTaskId = null;

        // 刷新批次列表
        refreshBatchList();

        // 刷新任务历史
        refreshTaskHistory();

        // 如果在分析页面，刷新分析数据
        if (window.location.pathname.includes('analysis')) {
            refreshAnalysisData();
        }
    }

    handleImportFailed(notification) {
        // 显示错误状态
        document.getElementById('task-status-text').textContent = '导入失败';
        document.getElementById('current-step').textContent = notification.data.error;

        // 隐藏进度条
        document.querySelector('.progress-container').style.display = 'none';
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, 2000 * this.reconnectAttempts);
        }
    }
}

// 异步导入功能
async function submitImportTask() {
    const fileInput = document.getElementById('data-file');
    const batchNameInput = document.getElementById('batch-name');

    if (!fileInput.files[0]) {
        alert('请选择要导入的文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('batch_name', batchNameInput.value);

    try {
        // 提交导入任务
        const response = await fetch('/api/defects/import/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        const result = await response.json();

        if (response.ok) {
            // 显示任务状态
            showTaskStatus(result);

            // 开始轮询任务状态
            startTaskPolling(result.task_id);
        } else {
            alert(`导入失败: ${result.error}`);
        }
    } catch (error) {
        alert(`导入失败: ${error.message}`);
    }
}

function showTaskStatus(taskInfo) {
    // 显示当前任务区域
    document.getElementById('current-task').style.display = 'block';

    // 设置任务ID
    notificationManager.currentTaskId = taskInfo.task_id;

    // 显示队列信息
    if (taskInfo.queue_position > 1) {
        document.getElementById('task-status-text').textContent = '排队中';
        document.getElementById('queue-position').textContent =
            `队列位置: ${taskInfo.queue_position}, 预估等待: ${Math.ceil(taskInfo.estimated_wait_time / 60)}分钟`;
    } else {
        document.getElementById('task-status-text').textContent = '准备处理';
        document.getElementById('queue-position').textContent = '';
    }

    // 重置进度
    document.getElementById('progress-fill').style.width = '0%';
    document.getElementById('progress-text').textContent = '0%';
    document.getElementById('current-step').textContent = '等待处理...';
}

async function cancelTask() {
    if (!notificationManager.currentTaskId) return;

    try {
        const response = await fetch(`/api/tasks/${notificationManager.currentTaskId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        if (response.ok) {
            document.getElementById('current-task').style.display = 'none';
            notificationManager.currentTaskId = null;
            notificationManager.showToast('任务已取消', '导入任务已成功取消', 'info');
        }
    } catch (error) {
        console.error('取消任务失败:', error);
    }
}

// 初始化通知管理器
const notificationManager = new NotificationManager();
document.addEventListener('DOMContentLoaded', () => {
    notificationManager.connect();
});
```

#### 7.3.4 字段映射和验证界面
```html
<!-- 导入前字段验证 -->
<div class="field-validation-section">
    <h3>字段验证</h3>

    <!-- 文件预览 -->
    <div class="file-preview">
        <input type="file" id="preview-file" accept=".xlsx,.xls,.csv" onchange="previewFile()">
        <button onclick="validateFields()" id="validate-btn" disabled>验证字段</button>
    </div>

    <!-- 字段映射结果 -->
    <div id="field-mapping-result" style="display:none;">
        <h4>字段映射结果</h4>

        <!-- 支持的字段 -->
        <div class="supported-fields">
            <h5>✅ 支持的字段 (<span id="supported-count">0</span>)</h5>
            <div id="supported-fields-list" class="field-list"></div>
        </div>

        <!-- 未知字段 -->
        <div class="unknown-fields" style="display:none;">
            <h5>⚠️ 未知字段 (<span id="unknown-count">0</span>)</h5>
            <div id="unknown-fields-list" class="field-list"></div>
            <p class="help-text">这些字段将被忽略，不会导入到数据库中</p>
        </div>

        <!-- 缺失的必需字段 -->
        <div class="missing-fields" style="display:none;">
            <h5>❌ 缺失的必需字段 (<span id="missing-count">0</span>)</h5>
            <div id="missing-fields-list" class="field-list"></div>
            <p class="error-text">必须包含这些字段才能成功导入</p>
        </div>

        <!-- 字段映射详情 -->
        <div class="field-mapping-details">
            <h5>字段映射详情</h5>
            <table class="mapping-table">
                <thead>
                    <tr>
                        <th>CSV字段名</th>
                        <th>数据库字段</th>
                        <th>字段类型</th>
                        <th>是否必需</th>
                        <th>示例值</th>
                    </tr>
                </thead>
                <tbody id="mapping-details-body">
                    <!-- 映射详情 -->
                </tbody>
            </table>
        </div>

        <div class="validation-actions">
            <button onclick="proceedWithImport()" id="proceed-btn" disabled>继续导入</button>
            <button onclick="downloadFieldTemplate()">下载字段模板</button>
        </div>
    </div>
</div>

<!-- 字段映射管理界面（管理员） -->
<div class="field-mapping-management" style="display:none;">
    <h3>字段映射管理</h3>

    <!-- 添加新映射 -->
    <div class="add-mapping-form">
        <h4>添加字段映射</h4>
        <form id="mapping-form">
            <div class="form-row">
                <label>CSV字段名:</label>
                <input type="text" id="csv-field-name" required>
            </div>
            <div class="form-row">
                <label>数据库字段:</label>
                <select id="db-field-name" required>
                    <option value="">选择数据库字段</option>
                    <!-- 动态加载数据库字段选项 -->
                </select>
            </div>
            <div class="form-row">
                <label>字段类型:</label>
                <select id="field-type" required>
                    <option value="string">字符串</option>
                    <option value="text">文本</option>
                    <option value="integer">整数</option>
                    <option value="float">浮点数</option>
                    <option value="datetime">日期时间</option>
                    <option value="date">日期</option>
                    <option value="boolean">布尔值</option>
                </select>
            </div>
            <div class="form-row">
                <label>是否必需:</label>
                <input type="checkbox" id="is-required">
            </div>
            <div class="form-row">
                <label>默认值:</label>
                <input type="text" id="default-value">
            </div>
            <div class="form-row">
                <label>字段描述:</label>
                <textarea id="field-description"></textarea>
            </div>
            <button type="submit">添加映射</button>
        </form>
    </div>

    <!-- 现有映射列表 -->
    <div class="existing-mappings">
        <h4>现有字段映射</h4>
        <table id="mappings-table">
            <thead>
                <tr>
                    <th>CSV字段名</th>
                    <th>数据库字段</th>
                    <th>字段类型</th>
                    <th>必需</th>
                    <th>核心字段</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 映射数据 -->
            </tbody>
        </table>
    </div>
</div>
```

```javascript
// 字段验证和映射功能
async function previewFile() {
    const fileInput = document.getElementById('preview-file');
    const validateBtn = document.getElementById('validate-btn');

    if (fileInput.files[0]) {
        validateBtn.disabled = false;
    } else {
        validateBtn.disabled = true;
    }
}

async function validateFields() {
    const fileInput = document.getElementById('preview-file');
    if (!fileInput.files[0]) return;

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);

    try {
        const response = await fetch('/api/defects/validate-headers/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        const result = await response.json();

        if (response.ok) {
            displayValidationResult(result);
        } else {
            alert(`验证失败: ${result.error}`);
        }
    } catch (error) {
        alert(`验证失败: ${error.message}`);
    }
}

function displayValidationResult(result) {
    const resultDiv = document.getElementById('field-mapping-result');
    resultDiv.style.display = 'block';

    // 显示支持的字段
    document.getElementById('supported-count').textContent = result.total_supported;
    const supportedList = document.getElementById('supported-fields-list');
    supportedList.innerHTML = result.supported_fields.map(field =>
        `<span class="field-tag supported">${field}</span>`
    ).join('');

    // 显示未知字段
    if (result.unknown_fields.length > 0) {
        document.getElementById('unknown-count').textContent = result.unknown_fields.length;
        const unknownList = document.getElementById('unknown-fields-list');
        unknownList.innerHTML = result.unknown_fields.map(field =>
            `<span class="field-tag unknown">${field}</span>`
        ).join('');
        document.querySelector('.unknown-fields').style.display = 'block';
    }

    // 显示缺失的必需字段
    if (result.missing_required_fields.length > 0) {
        document.getElementById('missing-count').textContent = result.missing_required_fields.length;
        const missingList = document.getElementById('missing-fields-list');
        missingList.innerHTML = result.missing_required_fields.map(field =>
            `<span class="field-tag missing">${field}</span>`
        ).join('');
        document.querySelector('.missing-fields').style.display = 'block';
        document.getElementById('proceed-btn').disabled = true;
    } else {
        document.getElementById('proceed-btn').disabled = false;
    }

    // 显示字段映射详情
    displayMappingDetails(result.field_mappings);
}

function displayMappingDetails(mappings) {
    const tbody = document.getElementById('mapping-details-body');
    tbody.innerHTML = mappings.map(mapping => `
        <tr>
            <td>${mapping.csv_field}</td>
            <td>${mapping.db_field}</td>
            <td>${mapping.field_type}</td>
            <td>${mapping.is_required ? '是' : '否'}</td>
            <td>${mapping.example_value || '-'}</td>
        </tr>
    `).join('');
}

async function downloadFieldTemplate() {
    try {
        const response = await fetch('/api/defects/field-template/', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'defect_import_template.xlsx';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    } catch (error) {
        alert(`下载模板失败: ${error.message}`);
    }
}

function proceedWithImport() {
    // 隐藏验证界面，显示导入界面
    document.getElementById('field-mapping-result').style.display = 'none';
    document.getElementById('current-task').style.display = 'block';

    // 执行导入
    submitImportTask();
}
```

## 8. 技术实现方案

### 8.1 分类算法设计
```python
class DefectClassifier:
    """缺陷分类器"""
    
    def __init__(self):
        self.project_patterns = self.load_project_patterns()
        self.category_patterns = self.load_category_patterns()
        self.commonality_mapping = self.load_commonality_mapping()
    
    def classify(self, title, description=""):
        """主分类方法"""
        result = {
            'project': self.extract_project(title),
            'summary_text': self.extract_summary_text(title),
            'bug_category': self.classify_category(title, description),
        }
        result['commonality'] = self.identify_commonality(result['bug_category'])
        return result
```

### 8.2 数据处理流程
```python
class DefectDataProcessor:
    """缺陷数据处理器"""
    
    def process_import_data(self, raw_data):
        """处理导入数据的完整流程"""
        # 1. 数据清洗
        cleaned_data = self.clean_data(raw_data)
        
        # 2. 数据转换
        transformed_data = self.transform_data(cleaned_data)
        
        # 3. 自动分类
        classified_data = self.auto_classify(transformed_data)
        
        # 4. 数据保存
        saved_records = self.save_records(classified_data)
        
        # 5. 触发统计分析
        self.trigger_analysis_update()
        
        return saved_records
```

## 9. 部署和配置

### 9.1 目录结构
```
defect_classification_tool/
├── models/
│   ├── defect_models.py
│   ├── classification_models.py
│   └── analysis_models.py
├── services/
│   ├── import_service.py
│   ├── classification_service.py
│   ├── statistics_service.py
│   └── rule_management_service.py
├── views/
│   ├── defect_views.py
│   ├── classification_views.py
│   └── analysis_views.py
├── templates/
│   ├── defect_management.html
│   ├── classification_rules.html
│   └── analysis_dashboard.html
├── static/
│   ├── css/
│   ├── js/
│   └── charts/
└── utils/
    ├── data_processors.py
    ├── classifiers.py
    └── exporters.py
```

### 9.2 配置文件
```python
# 分类配置
DEFECT_CLASSIFICATION_CONFIG = {
    'AUTO_CLASSIFY_ON_IMPORT': True,

    # 字段映射配置
    'FIELD_MAPPING_CONFIG': {
        'AUTO_CREATE_MAPPINGS': True,  # 自动创建字段映射
        'DEFAULT_FIELD_MAPPINGS': [
            # 核心必需字段
            {
                'csv_field_name': 'Issue key',
                'db_field_name': 'issue_key',
                'field_type': 'string',
                'is_required': True,
                'is_core_field': True,
                'max_length': 50,
                'description': '缺陷编号，唯一标识'
            },
            {
                'csv_field_name': 'Summary',
                'db_field_name': 'summary',
                'field_type': 'text',
                'is_required': False,
                'is_core_field': True,
                'description': '缺陷摘要描述'
            },
            {
                'csv_field_name': 'Status',
                'db_field_name': 'status',
                'field_type': 'string',
                'is_required': False,
                'is_core_field': True,
                'max_length': 50,
                'description': '缺陷状态'
            },
            {
                'csv_field_name': 'Priority',
                'db_field_name': 'priority',
                'field_type': 'string',
                'is_required': False,
                'is_core_field': True,
                'max_length': 20,
                'description': '优先级'
            },

            # 基础字段
            {
                'csv_field_name': 'Issue id',
                'db_field_name': 'issue_id',
                'field_type': 'string',
                'max_length': 50,
                'description': '缺陷ID'
            },
            {
                'csv_field_name': 'Issue Type',
                'db_field_name': 'issue_type',
                'field_type': 'string',
                'max_length': 50,
                'description': '问题类型'
            },
            {
                'csv_field_name': 'Resolution',
                'db_field_name': 'resolution',
                'field_type': 'string',
                'max_length': 50,
                'description': '解决方案'
            },

            # 项目相关字段
            {
                'csv_field_name': 'Project key',
                'db_field_name': 'project_key',
                'field_type': 'string',
                'max_length': 50,
                'description': '项目键'
            },
            {
                'csv_field_name': 'Project name',
                'db_field_name': 'project_name',
                'field_type': 'string',
                'max_length': 200,
                'description': '项目名称'
            },
            {
                'csv_field_name': 'Project type',
                'db_field_name': 'project_type',
                'field_type': 'string',
                'max_length': 50,
                'description': '项目类型'
            },

            # 人员字段
            {
                'csv_field_name': 'Assignee',
                'db_field_name': 'assignee',
                'field_type': 'string',
                'max_length': 100,
                'description': '指派人'
            },
            {
                'csv_field_name': 'Reporter',
                'db_field_name': 'reporter',
                'field_type': 'string',
                'max_length': 100,
                'description': '报告人'
            },
            {
                'csv_field_name': 'Creator',
                'db_field_name': 'creator',
                'field_type': 'string',
                'max_length': 100,
                'description': '创建人'
            },

            # 时间字段
            {
                'csv_field_name': 'Created',
                'db_field_name': 'created',
                'field_type': 'datetime',
                'date_format': '%Y/%m/%d %H:%M:%S',
                'description': '创建时间'
            },
            {
                'csv_field_name': 'Updated',
                'db_field_name': 'updated',
                'field_type': 'datetime',
                'date_format': '%Y/%m/%d %H:%M:%S',
                'description': '更新时间'
            },
            {
                'csv_field_name': 'Resolved',
                'db_field_name': 'resolved',
                'field_type': 'datetime',
                'date_format': '%Y/%m/%d %H:%M:%S',
                'description': '解决时间'
            },

            # 版本和组件
            {
                'csv_field_name': 'Affects Version/s',
                'db_field_name': 'affects_versions',
                'field_type': 'text',
                'description': '影响版本'
            },
            {
                'csv_field_name': 'Component/s',
                'db_field_name': 'components',
                'field_type': 'text',
                'description': '组件'
            },

            # 描述字段
            {
                'csv_field_name': 'Description',
                'db_field_name': 'description',
                'field_type': 'text',
                'description': '详细描述'
            },
            {
                'csv_field_name': 'Environment',
                'db_field_name': 'environment',
                'field_type': 'text',
                'description': '环境信息'
            },

            # 自定义字段映射
            {
                'csv_field_name': 'Custom field (机型)',
                'db_field_name': 'device_model',
                'field_type': 'string',
                'max_length': 100,
                'description': '设备机型'
            },
            {
                'csv_field_name': 'Custom field (Bug category)',
                'db_field_name': 'bug_category_custom',
                'field_type': 'string',
                'max_length': 100,
                'description': '缺陷分类'
            },
            {
                'csv_field_name': 'Custom field (CommonIssue)',
                'db_field_name': 'common_issue',
                'field_type': 'string',
                'max_length': 100,
                'description': '共性问题'
            },
            {
                'csv_field_name': 'Custom field (RD owner)',
                'db_field_name': 'rd_owner',
                'field_type': 'string',
                'max_length': 100,
                'description': 'RD负责人'
            },
            {
                'csv_field_name': 'Custom field (根因)',
                'db_field_name': 'root_cause',
                'field_type': 'text',
                'description': '根本原因分析'
            },
            {
                'csv_field_name': 'Custom field (解决方案)',
                'db_field_name': 'solution',
                'field_type': 'text',
                'description': '解决方案'
            },

            # 兼容旧格式字段
            {
                'csv_field_name': 'project',
                'db_field_name': 'project_extracted',
                'field_type': 'string',
                'max_length': 100,
                'description': '项目（旧格式）'
            },
            {
                'csv_field_name': 'summary_text',
                'db_field_name': 'summary_text',
                'field_type': 'text',
                'description': '摘要文本（旧格式）'
            },
            {
                'csv_field_name': 'bug_category',
                'db_field_name': 'bug_category',
                'field_type': 'string',
                'max_length': 100,
                'description': '缺陷分类（旧格式）'
            },
            {
                'csv_field_name': 'commonality',
                'db_field_name': 'commonality',
                'field_type': 'string',
                'max_length': 100,
                'description': '共性问题（旧格式）'
            },
        ]
    },

    # 规则导入配置
    'RULE_IMPORT_CONFIG': {
        'SUPPORTED_FORMATS': ['xlsx', 'xls', 'csv', 'json'],
        'MAX_FILE_SIZE': 5 * 1024 * 1024,  # 5MB
        'MAX_RULES_PER_IMPORT': 1000,
        'REQUIRED_FIELDS': ['rule_type', 'rule_name', 'pattern', 'target_value'],
        'OPTIONAL_FIELDS': ['priority', 'description'],
        'VALID_RULE_TYPES': ['project', 'category', 'commonality'],
    },

    # 默认系统规则（作为全局规则的基础）
    'DEFAULT_PROJECT_PATTERNS': [
        {
            'rule_name': 'Weather项目识别',
            'pattern': r'Weather|天气',
            'target_value': 'Weather',
            'priority': 10
        },
        {
            'rule_name': 'TransID项目识别',
            'pattern': r'TransID',
            'target_value': 'TransID',
            'priority': 10
        },
        {
            'rule_name': 'Notes项目识别',
            'pattern': r'Notes|笔记',
            'target_value': 'Notes',
            'priority': 10
        },
        # 更多模式...
    ],

    'DEFAULT_CATEGORY_PATTERNS': [
        {
            'rule_name': 'UI问题识别',
            'pattern': r'界面|显示|布局|UI|页面|视图',
            'target_value': 'UI问题',
            'priority': 5
        },
        {
            'rule_name': '功能问题识别',
            'pattern': r'功能|操作|流程|逻辑|业务',
            'target_value': '功能问题',
            'priority': 5
        },
        {
            'rule_name': '性能问题识别',
            'pattern': r'卡顿|慢|性能|内存|CPU|响应',
            'target_value': '性能问题',
            'priority': 5
        },
        {
            'rule_name': '兼容性问题识别',
            'pattern': r'兼容|适配|版本|机型',
            'target_value': '兼容性问题',
            'priority': 5
        },
        # 更多分类...
    ],

    'DEFAULT_COMMONALITY_PATTERNS': [
        {
            'rule_name': 'UI共性问题',
            'pattern': r'UI问题',
            'target_value': '界面显示类',
            'priority': 1
        },
        {
            'rule_name': '功能共性问题',
            'pattern': r'功能问题',
            'target_value': '功能逻辑类',
            'priority': 1
        },
        {
            'rule_name': '性能共性问题',
            'pattern': r'性能问题',
            'target_value': '性能优化类',
            'priority': 1
        },
        {
            'rule_name': '兼容性共性问题',
            'pattern': r'兼容性问题',
            'target_value': '兼容适配类',
            'priority': 1
        },
        # 更多规则...
    ],

    # 权限配置
    'PERMISSION_CONFIG': {
        'ALLOW_USER_RULE_IMPORT': True,
        'ALLOW_USER_RULE_EXPORT': True,
        'MAX_PERSONAL_RULES': 500,
        'RULE_PRIORITY_RANGE': {
            'global': (1, 100),
            'personal': (1, 50)
        }
    }
}

# Celery异步任务配置
CELERY_CONFIG = {
    'broker_url': 'redis://localhost:6379/0',
    'result_backend': 'redis://localhost:6379/0',
    'task_serializer': 'json',
    'accept_content': ['json'],
    'result_serializer': 'json',
    'timezone': 'Asia/Shanghai',
    'enable_utc': True,

    # 任务路由
    'task_routes': {
        'defect_classification.tasks.process_data_import': {'queue': 'data_import'},
        'defect_classification.tasks.process_data_analysis': {'queue': 'data_analysis'},
        'defect_classification.tasks.cleanup_completed_tasks': {'queue': 'maintenance'},
    },

    # 队列配置
    'task_default_queue': 'default',
    'task_queues': {
        'data_import': {
            'exchange': 'data_import',
            'routing_key': 'data_import',
        },
        'data_analysis': {
            'exchange': 'data_analysis',
            'routing_key': 'data_analysis',
        },
        'maintenance': {
            'exchange': 'maintenance',
            'routing_key': 'maintenance',
        },
    },

    # 并发配置
    'worker_concurrency': 4,
    'worker_max_tasks_per_child': 1000,
    'task_soft_time_limit': 3600,  # 1小时软限制
    'task_time_limit': 7200,       # 2小时硬限制

    # 重试配置
    'task_default_retry_delay': 60,
    'task_max_retries': 3,
}

# WebSocket配置
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('localhost', 6379)],
            'capacity': 1500,
            'expiry': 60,
        },
    },
}

# 任务队列配置
TASK_QUEUE_CONFIG = {
    'MAX_CONCURRENT_IMPORTS': 3,        # 最大并发导入任务数
    'MAX_QUEUE_SIZE': 100,              # 最大队列长度
    'TASK_TIMEOUT': 3600,               # 任务超时时间（秒）
    'CLEANUP_INTERVAL': 3600,           # 清理间隔（秒）
    'PROGRESS_UPDATE_INTERVAL': 5,      # 进度更新间隔（秒）

    # 优先级配置
    'PRIORITY_WEIGHTS': {
        1: 0.25,  # 低优先级
        2: 1.0,   # 普通优先级
        3: 2.0,   # 高优先级
        4: 4.0,   # 紧急优先级
    },

    # 用户配额
    'USER_LIMITS': {
        'max_concurrent_tasks': 2,      # 用户最大并发任务数
        'max_daily_imports': 10,        # 每日最大导入次数
        'max_file_size': 50 * 1024 * 1024,  # 50MB
    }
}

# 通知配置
NOTIFICATION_CONFIG = {
    'ENABLE_WEBSOCKET': True,
    'ENABLE_EMAIL': False,
    'ENABLE_SMS': False,

    # WebSocket通知类型
    'WEBSOCKET_EVENTS': [
        'import_started',
        'import_progress',
        'import_completed',
        'import_failed',
        'task_queued',
        'task_cancelled',
    ],

    # 通知保留时间
    'NOTIFICATION_RETENTION_DAYS': 30,
}

# 规则模板配置
RULE_TEMPLATE_CONFIG = {
    'EXCEL_TEMPLATE': {
        'filename': 'classification_rules_template.xlsx',
        'sheets': {
            'rules': {
                'columns': [
                    'rule_type', 'rule_name', 'pattern',
                    'target_value', 'priority', 'description'
                ],
                'sample_data': [
                    ['project', 'Weather项目识别', 'Weather|天气', 'Weather', 10, '识别天气相关项目'],
                    ['category', 'UI问题分类', '界面|显示|布局|UI', 'UI问题', 5, '识别界面显示类问题'],
                    ['commonality', '界面共性问题', 'UI问题', '界面显示类', 1, 'UI问题的共性分类']
                ]
            },
            'help': {
                'content': '''
规则导入说明：
1. rule_type: 规则类型，可选值：project（项目提取）、category（缺陷分类）、commonality（共性问题）
2. rule_name: 规则名称，用于标识规则
3. pattern: 匹配模式，支持正则表达式
4. target_value: 目标值，匹配成功时返回的值
5. priority: 优先级，数字越大优先级越高（1-50）
6. description: 规则描述，可选字段
                '''
            }
        }
    }
}
```

## 10. 扩展性设计

### 10.1 规则引擎
- 支持动态配置分类规则
- 支持正则表达式和关键词匹配
- 支持规则优先级和权重设置
- 支持规则效果评估和优化

### 10.2 机器学习集成
- 预留机器学习模型接口
- 支持训练数据收集
- 支持模型效果评估
- 支持在线学习和模型更新

### 10.3 数据源扩展
- 支持多种数据源导入（JIRA、Excel、CSV、API）
- 支持实时数据同步
- 支持增量数据更新
- 支持数据源配置管理

## 11. 实施计划

### 11.1 开发阶段
1. **第一阶段**（1-2周）：基础框架搭建
   - 数据模型设计和实现
   - 基础API接口开发
   - 数据导入功能实现

2. **第二阶段**（2-3周）：核心功能开发
   - 自动分类算法实现
   - 统计分析服务开发
   - 前端界面开发

3. **第三阶段**（1周）：测试和优化
   - 功能测试和性能优化
   - 用户界面优化
   - 文档编写

### 11.2 部署和维护
- 生产环境部署
- 用户培训
- 持续优化和迭代

## 12. 总结

本设计方案基于现代化的技术栈，采用Django + Vue3的前后端分离架构，针对缺陷分析的特定需求进行了全面优化设计。方案具有以下特点：

- **一键启动部署**：提供自动化启动脚本，5分钟内完成环境搭建
- **现代化前端架构**：Vue3 + TypeScript + Pinia提供优秀的开发体验和用户体验
- **智能字段映射**：自动识别CSV字段，支持部分字段导入和全量字段导出
- **权限管理完善**：支持管理员和普通用户的差异化权限控制
- **数据隔离安全**：确保用户只能访问自己的数据，保护数据安全
- **批次管理便捷**：每次导入都有唯一标识，便于数据追踪和管理
- **规则导入灵活**：普通用户可以导入个人分类规则，支持多种文件格式
- **实时交互体验**：WebSocket实时通信，提供即时的状态更新和通知
- **可扩展性强**：支持规则动态配置和机器学习集成
- **易于维护**：清晰的分层架构和模块化设计
- **用户友好**：基于Element Plus的现代化界面设计
- **性能优化**：前后端分离、代码分割、懒加载等性能优化策略
- **容器化部署**：完整的Docker部署方案，支持一键部署到生产环境

## 核心优化亮点：

### 1. **智能字段映射系统**
- **灵活字段映射**: 基于实际CSV文件字段设计的完整数据模型
- **部分字段导入**: 支持用户导入部分字段，空字段自动设为默认值
- **全量字段导出**: 导出时包含所有字段，确保数据完整性
- **自动字段识别**: 智能识别CSV表头并提供映射建议
- **字段验证**: 导入前验证字段完整性和格式正确性

### 2. **多用户并发导入系统**
- **异步任务处理**: 基于Celery的分布式任务队列
- **智能排队机制**: 自动队列管理和优先级调度
- **实时进度反馈**: WebSocket推送任务状态和处理进度
- **并发控制**: 支持多用户同时导入，系统自动调度资源
- **任务管理**: 完整的任务生命周期管理（提交、排队、处理、完成）

### 3. **实时通知系统**
- **WebSocket通信**: 实时双向通信，零延迟状态更新
- **Toast通知**: 友好的用户界面提示
- **进度可视化**: 实时进度条和状态指示器
- **多种通知类型**: 导入开始、进度更新、完成、失败等

### 4. **分类规则导入功能**
- 支持Excel、CSV、JSON三种格式
- 提供规则模板下载
- 完整的验证和错误处理机制
- 支持规则冲突检测和处理

### 5. **双层权限体系**
- 全局规则：管理员管理，所有用户共享
- 个人规则：用户自主管理，优先级更高
- 规则作用域清晰，权限控制精确

### 6. **数据权限隔离**
- 数据库层自动过滤
- API层权限检查
- 前端界面权限提示
- 完整的审计日志

### 7. **增强的批次追踪管理**
- 唯一批次标识符
- 导入来源追踪
- 批次数据导出
- 批次分析报告
- **任务状态追踪**: 上传、排队、处理中、已完成、失败等状态

## 🚀 技术架构优势：

### 1. **高并发处理能力**
- Celery分布式任务队列支持水平扩展
- Redis作为消息代理，高性能低延迟
- 多Worker并行处理，提升系统吞吐量

### 2. **用户体验优化**
- 异步处理避免页面阻塞
- 实时反馈让用户了解处理状态
- 智能排队显示预估等待时间
- 一键取消任务功能

### 3. **系统稳定性**
- 任务失败自动重试机制
- 完整的错误处理和日志记录
- 任务超时保护
- 定期清理机制

### 4. **Vue3前端架构优势**
- **组合式API**: 更好的逻辑复用和代码组织
- **TypeScript集成**: 完整的类型安全和开发体验
- **响应式系统**: 高效的数据响应和更新机制
- **组件化设计**: 高度可复用的组件架构
- **现代化工具链**: Vite + ESLint + Prettier提供优秀的开发体验

### 5. **可扩展性设计**
- 微服务架构，易于扩展
- 插件化的通知系统
- 灵活的队列配置
- 支持多种部署方式
- 前后端分离，独立部署和扩展

## 🎨 前端技术亮点：

### 1. **Vue3 Composition API**
- 更好的逻辑复用和代码组织
- 更强的TypeScript支持
- 更灵活的组件设计

### 2. **现代化UI体验**
- Element Plus提供丰富的组件库
- ECharts提供专业的数据可视化
- 响应式设计适配多种设备

### 3. **状态管理优化**
- Pinia提供轻量级状态管理
- 模块化的store设计
- 完整的TypeScript支持

### 4. **开发体验提升**
- Vite提供极快的热重载
- 完整的TypeScript类型检查
- ESLint + Prettier代码质量保证

该方案完美解决了多用户并发导入的需求，通过现代化的Vue3前端架构和异步任务队列系统，为用户提供了流畅、直观、现代化的使用体验。系统具备良好的扩展性、可维护性和稳定性，可以根据实际使用情况进行优化调整。

## 🚀 快速启动指南

### 环境要求
- Python 3.11+
- Node.js 18+
- Redis 6+
- PostgreSQL 13+ (可选，默认使用SQLite)

### 一键启动开发环境

#### Linux/macOS 用户
```bash
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 给脚本执行权限
chmod +x start-dev.sh stop-dev.sh

# 启动开发环境
./start-dev.sh
```

#### Windows 用户

**方法1: 使用批处理文件 (推荐)**
```cmd
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 启动开发环境
start-dev.bat
```

**方法2: 使用PowerShell**
```powershell
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 启动开发环境
.\start-dev.ps1
```

**方法3: 使用Git Bash**
```bash
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 启动开发环境
./start-dev.sh
```

启动后访问：
- **前端**: http://localhost:3000
- **后端API**: http://localhost:8000/api
- **管理后台**: http://localhost:8000/admin

**默认管理员账号**: `admin` / `admin123`

#### Windows 特别说明
- **推荐使用**: Windows Terminal 或 PowerShell 获得更好的体验
- **权限问题**: 如遇权限问题，请以管理员身份运行
- **Redis安装**: Windows下推荐使用WSL或Docker运行Redis
- **服务管理**: 各服务在独立窗口运行，关闭窗口即停止服务
- **停止服务**: 运行 `stop-dev.bat` 或 `stop-dev.ps1`

### 手动启动步骤

#### 1. 后端启动

**Linux/macOS:**
```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动Redis
redis-server

# 启动Celery Worker
celery -A defect_classification worker --loglevel=info

# 启动Django服务器
python manage.py runserver
```

**Windows:**
```cmd
cd backend

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate.bat

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动Redis (需要先安装)
redis-server

# 启动Celery Worker
celery -A defect_classification worker --loglevel=info --pool=solo

# 启动Django服务器
python manage.py runserver
```

#### 2. 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 项目结构

```
defect-classification-tool/
├── backend/                    # Django后端
│   ├── defect_classification/  # 项目配置
│   ├── defects/               # 缺陷管理应用
│   ├── tasks/                 # 任务管理应用
│   ├── users/                 # 用户管理应用
│   ├── notifications/         # 通知应用
│   └── requirements.txt       # Python依赖
├── frontend/                  # Vue3前端
│   ├── src/
│   │   ├── api/              # API接口
│   │   ├── components/       # Vue组件
│   │   ├── views/            # 页面组件
│   │   ├── stores/           # Pinia状态管理
│   │   ├── composables/      # 组合式函数
│   │   ├── types/            # TypeScript类型
│   │   └── utils/            # 工具函数
│   ├── package.json          # Node.js依赖
│   └── vite.config.ts        # Vite配置
├── docker-compose.yml        # Docker编排
├── start-dev.sh             # 开发环境启动脚本
├── stop-dev.sh              # 开发环境停止脚本
└── README.md                # 项目文档
```

### 使用指南

#### 数据导入流程
1. 点击"导入数据"按钮
2. 选择Excel或CSV文件（支持拖拽上传）
3. 系统自动验证字段映射
4. 查看字段验证结果：
   - ✅ 支持的字段（绿色标签）
   - ⚠️ 未知字段（黄色标签，将被忽略）
   - ❌ 缺失必需字段（红色标签，需要修正）
5. 确认导入信息和批次名称
6. 提交任务，系统异步处理
7. 实时查看导入进度和状态

#### 字段映射管理（管理员）
- 配置CSV字段到数据库字段的映射关系
- 支持多种数据类型：字符串、文本、整数、浮点数、日期时间、布尔值
- 设置字段验证规则：必需字段、最大长度、正则表达式验证
- 配置默认值和日期格式

#### 分类规则管理
- **全局规则**：管理员创建，所有用户可见
- **个人规则**：用户自己创建，仅自己可见
- 支持正则表达式匹配模式
- 规则优先级设置
- 规则测试功能

#### 数据分析功能
- **A/B/C类问题统计**：按优先级分类统计
- **设备型号缺陷分布**：横向条形图显示
- **时间趋势分析**：按日期统计缺陷数量
- **共性问题识别**：自动识别重复问题模式

### 配置说明

#### 环境变量配置
```bash
# Django配置
DEBUG=True
SECRET_KEY=your-secret-key-here
DB_HOST=localhost
DB_NAME=defect_classification
DB_USER=postgres
DB_PASSWORD=password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB
MAX_RECORDS_PER_IMPORT=10000
```

#### 支持的字段类型
系统自动识别和映射以下字段：

**核心字段**（必需）：
- Issue key（缺陷编号）
- Summary（摘要）
- Status（状态）
- Priority（优先级）

**扩展字段**（可选）：
- Project key, Project name（项目信息）
- Assignee, Reporter, Creator（人员信息）
- Created, Updated, Resolved（时间信息）
- Description, Environment（描述信息）
- 设备型号, 缺陷分类, 共性问题（自定义字段）

### 故障排除

#### 常见问题
1. **Redis连接失败**
   ```bash
   # 启动Redis服务
   redis-server
   ```

2. **数据库连接错误**
   ```bash
   # 检查数据库配置
   python manage.py dbshell
   ```

3. **前端构建失败**
   ```bash
   # 清除缓存重新安装
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **Celery任务不执行**
   ```bash
   # 检查Celery Worker状态
   celery -A defect_classification inspect active
   ```

#### 日志查看
- **Django日志**: `backend/logs/django.log`
- **Celery日志**: `backend/celery.log`
- **前端开发日志**: 浏览器控制台

### 性能优化建议

1. **数据库优化**
   - 为常用查询字段添加索引
   - 定期清理过期的任务记录
   - 使用PostgreSQL替代SQLite（生产环境）

2. **缓存优化**
   - 启用Redis缓存
   - 缓存频繁查询的数据
   - 设置合理的缓存过期时间

3. **前端优化**
   - 启用代码分割和懒加载
   - 压缩静态资源
   - 使用CDN加速

4. **任务队列优化**
   - 根据服务器配置调整Worker数量
   - 设置合理的任务超时时间
   - 监控队列长度和处理速度

---

## 📋 快速参考

### 常用命令

#### Linux/macOS
```bash
# 启动开发环境
./start-dev.sh

# 停止开发环境
./stop-dev.sh

# Docker部署
docker-compose up -d

# 查看日志
docker-compose logs -f

# 数据库迁移
python3 manage.py migrate

# 创建超级用户
python3 manage.py createsuperuser

# 启动Celery Worker
celery -A defect_classification worker --loglevel=info
```

#### Windows
```cmd
# 启动开发环境 (批处理)
start-dev.bat

# 停止开发环境 (批处理)
stop-dev.bat

# 启动开发环境 (PowerShell)
.\start-dev.ps1

# 停止开发环境 (PowerShell)
.\stop-dev.ps1

# Docker部署
docker-compose up -d

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 启动Celery Worker
celery -A defect_classification worker --loglevel=info --pool=solo
```

### 重要端口
- **3000**: 前端开发服务器
- **8000**: Django后端服务器
- **6379**: Redis缓存服务
- **5432**: PostgreSQL数据库

### 默认账号
- **管理员**: admin / admin123
- **API Token**: 通过Django管理后台获取

### 关键目录
- **后端代码**: `backend/`
- **前端代码**: `frontend/src/`
- **日志文件**: `backend/logs/`
- **媒体文件**: `backend/media/`
- **静态文件**: `backend/staticfiles/`

### 技术支持
- **项目文档**: README.md
- **API文档**: http://localhost:8000/api/
- **管理后台**: http://localhost:8000/admin/
- **前端界面**: http://localhost:3000/
