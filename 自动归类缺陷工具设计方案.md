# 自动归类缺陷工具设计方案

## 1. 项目概述

### 1.1 项目背景
作为三级部门测开提效工具负责人，需要开发一款自动归类缺陷工具，用于解决日常交付中的缺陷分析痛点。

### 1.2 核心功能
- **数据汇总**: A、B、C类问题个数统计
- **机型缺陷汇总**: 各机型缺陷量分析
- **每日缺陷提报趋势**: 时间维度趋势分析
- **缺陷归类**: 自动分类缺陷类型
- **共性问题判断**: 识别和分析共性问题

### 1.3 核心字段
- Priority（优先级）
- Status（状态）
- Resolution（解决方案）
- Created（创建日期，转化为天格式：2025/4/7）
- stage（阶段）
- project（根据标题提取）
- summary_text（根据标题提取）
- bug_category（根据标题匹配）
- commonality（根据bug_category匹配）

## 2. 系统架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端界面层 (UI Layer)"
        A1[缺陷数据管理页面]
        A2[分类规则配置页面]
        A3[统计分析仪表板]
    end

    subgraph "API层 (API Layer)"
        B1[缺陷管理接口]
        B2[分类规则接口]
        B3[统计分析接口]
        B4[数据导入接口]
    end

    subgraph "业务逻辑层 (Business Layer)"
        C1[数据导入服务<br/>DataImportService]
        C2[自动分类服务<br/>AutoClassificationService]
        C3[统计分析服务<br/>StatisticsService]
        C4[规则管理服务<br/>RuleManagementService]
    end

    subgraph "数据层 (Data Layer)"
        D1[缺陷数据模型<br/>DefectRecord]
        D2[分类规则模型<br/>ClassificationRule]
        D3[统计分析模型<br/>DefectAnalysis]
        D4[SQLite数据库]
    end

    subgraph "外部数据源"
        E1[Excel文件]
        E2[CSV文件]
        E3[JIRA API]
        E4[其他数据源]
    end

    subgraph "工具组件"
        F1[分类算法引擎]
        F2[数据处理器]
        F3[报告生成器]
        F4[规则引擎]
    end

    %% 连接关系
    A1 --> B1
    A2 --> B2
    A3 --> B3

    B1 --> C1
    B1 --> C2
    B2 --> C4
    B3 --> C3
    B4 --> C1

    C1 --> D1
    C2 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D2

    D1 --> D4
    D2 --> D4
    D3 --> D4

    E1 --> C1
    E2 --> C1
    E3 --> C1
    E4 --> C1

    C2 --> F1
    C1 --> F2
    C3 --> F3
    C4 --> F4
```

### 2.2 架构说明
- **前端界面层**：提供用户交互界面，包括数据管理、规则配置和分析展示
- **API层**：RESTful API接口，实现前后端分离
- **业务逻辑层**：核心业务逻辑处理，包括数据导入、自动分类、统计分析等
- **数据层**：数据模型定义和数据库操作
- **外部数据源**：支持多种数据源导入
- **工具组件**：可复用的工具模块

### 2.3 技术栈
- **后端框架**: Django 5.2
- **数据库**: SQLite3（开发）/ PostgreSQL（生产）
- **前端**: HTML + JavaScript + Chart.js
- **数据处理**: Pandas + NumPy
- **异步任务**: Celery + Redis
- **实时通信**: Django Channels + WebSocket
- **任务调度**: Django APScheduler

### 2.4 多用户并发导入流程图

```mermaid
sequenceDiagram
    participant U1 as 用户1
    participant U2 as 用户2
    participant UI as 前端界面
    participant API as API服务
    participant Queue as 任务队列
    participant Celery as Celery Worker
    participant WS as WebSocket
    participant DB as 数据库

    Note over U1,U2: 多用户同时上传文件

    U1->>UI: 上传文件A
    U2->>UI: 上传文件B

    UI->>API: POST /api/defects/import/ (文件A)
    API->>DB: 创建ImportBatch记录
    API->>Queue: 提交任务到队列
    API->>UI: 返回任务ID和队列位置

    UI->>API: POST /api/defects/import/ (文件B)
    API->>DB: 创建ImportBatch记录
    API->>Queue: 提交任务到队列
    API->>UI: 返回任务ID和队列位置

    Note over UI: 建立WebSocket连接
    UI->>WS: 连接WebSocket
    WS->>UI: 连接成功

    Note over Queue,Celery: 任务队列处理
    Queue->>Celery: 分配任务A给Worker1
    Queue->>Celery: 任务B排队等待

    Note over Celery: Worker1处理任务A
    Celery->>DB: 更新状态为"处理中"
    Celery->>WS: 发送进度更新
    WS->>UI: 推送进度到用户1

    loop 处理过程
        Celery->>Celery: 数据验证和转换
        Celery->>DB: 更新进度
        Celery->>WS: 发送进度更新
        WS->>UI: 实时进度推送
    end

    Celery->>DB: 任务A完成，保存结果
    Celery->>WS: 发送完成通知
    WS->>UI: Toast提示"导入完成"

    Note over Queue: 任务B开始处理
    Queue->>Celery: 分配任务B给Worker2
    Celery->>DB: 更新状态为"处理中"
    Celery->>WS: 发送进度更新
    WS->>UI: 推送进度到用户2

    loop 处理过程
        Celery->>Celery: 数据验证和转换
        Celery->>DB: 更新进度
        Celery->>WS: 发送进度更新
        WS->>UI: 实时进度推送
    end

    Celery->>DB: 任务B完成，保存结果
    Celery->>WS: 发送完成通知
    WS->>UI: Toast提示"导入完成"

    Note over U1,U2: 用户查看结果
    U1->>UI: 查看导入结果
    U2->>UI: 查看导入结果
    UI->>API: 获取批次数据
    API->>DB: 查询用户数据
    DB->>API: 返回结果
    API->>UI: 返回数据
    UI->>U1: 显示用户1的数据
    UI->>U2: 显示用户2的数据
```

### 2.5 数据处理流程图

```mermaid
flowchart TD
    A[开始] --> B{数据来源}

    B -->|Excel文件| C1[Excel数据读取]
    B -->|CSV文件| C2[CSV数据读取]
    B -->|JIRA API| C3[JIRA数据获取]
    B -->|其他来源| C4[其他数据读取]

    C1 --> D[数据验证]
    C2 --> D
    C3 --> D
    C4 --> D

    D --> E{验证通过?}
    E -->|否| F[错误处理<br/>返回错误信息]
    E -->|是| G[数据清洗]

    G --> H[数据转换<br/>标准化格式]

    H --> I[项目信息提取<br/>extract_project]
    I --> J[摘要文本提取<br/>extract_summary_text]
    J --> K[缺陷分类<br/>classify_bug_category]
    K --> L[共性问题识别<br/>identify_commonality]

    L --> M[保存到数据库<br/>DefectRecord]

    M --> N[触发统计分析]

    N --> O[生成A/B/C类汇总]
    N --> P[生成机型缺陷汇总]
    N --> Q[生成每日趋势]
    N --> R[生成分类分布]
    N --> S[生成共性问题分析]

    O --> T[保存分析结果<br/>DefectAnalysis]
    P --> T
    Q --> T
    R --> T
    S --> T

    T --> U[更新仪表板数据]
    U --> V[结束]

    F --> V

    %% 分类规则更新流程
    W[分类规则管理] --> X[规则配置]
    X --> Y[规则测试]
    Y --> Z{测试通过?}
    Z -->|是| AA[保存规则<br/>ClassificationRule]
    Z -->|否| AB[规则调整]
    AB --> Y
    AA --> AC[重新分类现有数据]
    AC --> N
```

### 2.6 统计分析维度关系图

```mermaid
graph LR
    subgraph "数据输入"
        A1[Priority<br/>优先级]
        A2[Status<br/>状态]
        A3[Resolution<br/>解决方案]
        A4[Created<br/>创建日期]
        A5[Stage<br/>阶段]
        A6[Original Title<br/>原始标题]
    end

    subgraph "数据提取"
        B1[Project<br/>项目]
        B2[Summary Text<br/>摘要文本]
        B3[Bug Category<br/>缺陷分类]
        B4[Commonality<br/>共性问题]
    end

    subgraph "分析维度"
        C1[A/B/C类问题汇总<br/>基于Priority]
        C2[机型缺陷汇总<br/>基于Project]
        C3[每日缺陷趋势<br/>基于Created]
        C4[缺陷归类分析<br/>基于Bug Category]
        C5[共性问题判断<br/>基于Commonality]
    end

    subgraph "输出结果"
        D1[统计图表]
        D2[趋势分析]
        D3[分布分析]
        D4[汇总报告]
        D5[导出数据]
    end

    %% 数据流向
    A6 --> B1
    A6 --> B2
    A6 --> B3
    B3 --> B4

    A1 --> C1
    B1 --> C2
    A4 --> C3
    B3 --> C4
    B4 --> C5

    C1 --> D1
    C2 --> D1
    C3 --> D2
    C4 --> D3
    C5 --> D3

    C1 --> D4
    C2 --> D4
    C3 --> D4
    C4 --> D4
    C5 --> D4

    D1 --> D5
    D2 --> D5
    D3 --> D5
    D4 --> D5
```

## 3. 数据模型设计

### 3.1 缺陷数据模型
```python
class DefectRecord(models.Model):
    """
    缺陷记录模型 - 基于实际CSV文件字段设计
    支持部分字段导入，空字段默认为空值
    导出时按照全量字段导出
    """

    # === 核心标识字段 ===
    issue_key = models.CharField(max_length=50, unique=True, verbose_name='Issue key')
    issue_id = models.CharField(max_length=50, verbose_name='Issue id', blank=True)
    summary = models.TextField(verbose_name='Summary', blank=True)

    # === 基础状态字段 ===
    issue_type = models.CharField(max_length=50, verbose_name='Issue Type', blank=True)
    status = models.CharField(max_length=50, verbose_name='Status', blank=True)
    priority = models.CharField(max_length=20, verbose_name='Priority', blank=True)
    resolution = models.CharField(max_length=50, verbose_name='Resolution', blank=True)

    # === 项目相关字段 ===
    project_key = models.CharField(max_length=50, verbose_name='Project key', blank=True)
    project_name = models.CharField(max_length=200, verbose_name='Project name', blank=True)
    project_type = models.CharField(max_length=50, verbose_name='Project type', blank=True)
    project_lead = models.CharField(max_length=100, verbose_name='Project lead', blank=True)
    project_description = models.TextField(verbose_name='Project description', blank=True)
    project_url = models.URLField(verbose_name='Project url', blank=True)

    # === 人员相关字段 ===
    assignee = models.CharField(max_length=100, verbose_name='Assignee', blank=True)
    reporter = models.CharField(max_length=100, verbose_name='Reporter', blank=True)
    creator = models.CharField(max_length=100, verbose_name='Creator', blank=True)

    # === 时间相关字段 ===
    created = models.DateTimeField(verbose_name='Created', null=True, blank=True)
    updated = models.DateTimeField(verbose_name='Updated', null=True, blank=True)
    last_viewed = models.DateTimeField(verbose_name='Last Viewed', null=True, blank=True)
    resolved = models.DateTimeField(verbose_name='Resolved', null=True, blank=True)
    due_date = models.DateTimeField(verbose_name='Due Date', null=True, blank=True)

    # === 版本相关字段 ===
    affects_versions = models.TextField(verbose_name='Affects Version/s', blank=True)
    components = models.TextField(verbose_name='Component/s', blank=True)

    # === 描述和环境字段 ===
    description = models.TextField(verbose_name='Description', blank=True)
    environment = models.TextField(verbose_name='Environment', blank=True)

    # === 工作量相关字段 ===
    original_estimate = models.CharField(max_length=50, verbose_name='Original Estimate', blank=True)
    remaining_estimate = models.CharField(max_length=50, verbose_name='Remaining Estimate', blank=True)
    time_spent = models.CharField(max_length=50, verbose_name='Time Spent', blank=True)
    work_ratio = models.FloatField(verbose_name='Work Ratio', null=True, blank=True)

    # === 其他基础字段 ===
    votes = models.IntegerField(verbose_name='Votes', default=0)
    labels = models.TextField(verbose_name='Labels', blank=True)
    watchers = models.TextField(verbose_name='Watchers', blank=True)
    security_level = models.CharField(max_length=50, verbose_name='Security Level', blank=True)
    attachments = models.TextField(verbose_name='Attachments', blank=True)

    # === 自定义字段（基于CSV中的Custom field字段） ===
    # 版本相关
    affect_apk_versions = models.TextField(verbose_name='Affect Apk Version/s', blank=True)
    fix_apk_versions = models.TextField(verbose_name='Fix Apk Version/s', blank=True)
    android_version = models.CharField(max_length=100, verbose_name='Android版本', blank=True)
    os_version = models.CharField(max_length=100, verbose_name='OS版本', blank=True)

    # 项目和模块相关
    affect_project = models.CharField(max_length=200, verbose_name='Affect Project', blank=True)
    module = models.CharField(max_length=100, verbose_name='模块', blank=True)
    component_custom = models.CharField(max_length=100, verbose_name='Change Component', blank=True)

    # 缺陷分类相关
    bug_source = models.CharField(max_length=100, verbose_name='BUG来源', blank=True)
    bug_category_custom = models.CharField(max_length=100, verbose_name='Bug category', blank=True)
    issue_category = models.CharField(max_length=100, verbose_name='Issue Category', blank=True)
    issue_nature = models.CharField(max_length=100, verbose_name='Issue Nature', blank=True)
    issue_source = models.CharField(max_length=100, verbose_name='Issue Source', blank=True)
    issue_stage = models.CharField(max_length=100, verbose_name='Issue Stage', blank=True)
    common_issue = models.CharField(max_length=100, verbose_name='CommonIssue', blank=True)

    # 设备和环境相关
    device_model = models.CharField(max_length=100, verbose_name='机型', blank=True)
    country_code = models.CharField(max_length=10, verbose_name='CountryCode', blank=True)
    country = models.CharField(max_length=50, verbose_name='国家', blank=True)
    imei = models.CharField(max_length=50, verbose_name='IMEI', blank=True)
    sn = models.CharField(max_length=50, verbose_name='SN', blank=True)

    # 责任人相关
    rd_owner = models.CharField(max_length=100, verbose_name='RD owner', blank=True)
    pm = models.CharField(max_length=100, verbose_name='PM', blank=True)
    opener = models.CharField(max_length=100, verbose_name='Opener', blank=True)
    fixer = models.CharField(max_length=100, verbose_name='Fixer', blank=True)

    # 分析和解决相关
    root_cause = models.TextField(verbose_name='根因', blank=True)
    solution = models.TextField(verbose_name='解决方案', blank=True)
    fix_way = models.TextField(verbose_name='Fix Way', blank=True)
    risk = models.CharField(max_length=100, verbose_name='Risk', blank=True)

    # 测试相关
    test_suggestion = models.TextField(verbose_name='测试建议', blank=True)
    test_owner = models.CharField(max_length=100, verbose_name='测试负责人', blank=True)
    need_retest = models.CharField(max_length=10, verbose_name='需要Retest', blank=True)
    retest_reason = models.TextField(verbose_name='Retest原因', blank=True)
    retest_suggestion = models.TextField(verbose_name='Retest建议', blank=True)

    # === 数据处理字段（自动生成） ===
    # 提取和分类字段
    project_extracted = models.CharField(max_length=100, verbose_name='提取的项目', blank=True)
    summary_text = models.TextField(verbose_name='摘要文本', blank=True)
    bug_category = models.CharField(max_length=100, verbose_name='缺陷分类', blank=True)
    commonality = models.CharField(max_length=100, verbose_name='共性问题', blank=True)

    # 原始数据存储
    original_data = models.JSONField(default=dict, verbose_name='原始导入数据')

    # === 数据权限和导入标识 ===
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', db_index=True)
    import_timestamp = models.DateTimeField(verbose_name='导入时间戳', db_index=True)
    uploaded_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='上传用户',
        related_name='uploaded_defects'
    )

    # === 系统字段 ===
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='记录创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='记录更新时间')

    class Meta:
        verbose_name = '缺陷记录'
        verbose_name_plural = '缺陷记录'
        ordering = ['-import_timestamp', '-created']
        indexes = [
            models.Index(fields=['import_batch_id', 'uploaded_by']),
            models.Index(fields=['import_timestamp', 'uploaded_by']),
            models.Index(fields=['issue_key']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['project_key', 'uploaded_by']),
            models.Index(fields=['assignee', 'uploaded_by']),
        ]

    def __str__(self):
        return f"{self.issue_key} - {self.summary[:50]}"

    @property
    def created_date(self):
        """兼容性属性，返回创建日期"""
        return self.created.date() if self.created else None
```

### 3.2 分类规则模型
```python
class ClassificationRule(models.Model):
    RULE_TYPE_CHOICES = [
        ('project', '项目提取'),
        ('category', '缺陷分类'),
        ('commonality', '共性问题'),
    ]

    SCOPE_CHOICES = [
        ('global', '全局规则'),
        ('personal', '个人规则'),
    ]

    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    rule_name = models.CharField(max_length=100, verbose_name='规则名称')
    pattern = models.TextField(verbose_name='匹配模式')
    target_value = models.CharField(max_length=100, verbose_name='目标值')
    priority = models.IntegerField(default=0, verbose_name='优先级')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, verbose_name='规则描述')

    # 权限和作用域控制
    scope = models.CharField(max_length=20, choices=SCOPE_CHOICES, default='personal', verbose_name='规则作用域')
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='创建用户',
        related_name='created_rules'
    )

    # 规则来源追踪
    import_batch_id = models.CharField(max_length=50, verbose_name='导入批次ID', blank=True, db_index=True)
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '分类规则'
        verbose_name_plural = '分类规则'
        ordering = ['scope', '-priority', 'rule_type']
        indexes = [
            models.Index(fields=['scope', 'created_by', 'is_active']),
            models.Index(fields=['rule_type', 'scope', 'priority']),
            models.Index(fields=['import_batch_id']),
        ]

    def can_edit(self, user):
        """检查用户是否可以编辑此规则"""
        if self.scope == 'global':
            # 全局规则只有管理员可以编辑
            user_profile = UserProfile.objects.get_or_create(user=user)[0]
            return user_profile.role == 'admin' or user_profile.can_manage_rules
        else:
            # 个人规则只有创建者可以编辑
            return self.created_by == user
```

### 3.3 导入批次管理模型
```python
class ImportBatch(models.Model):
    """导入批次管理模型"""
    batch_id = models.CharField(max_length=50, unique=True, verbose_name='批次ID')
    batch_name = models.CharField(max_length=200, verbose_name='批次名称', blank=True)
    import_timestamp = models.DateTimeField(auto_now_add=True, verbose_name='导入时间')
    uploaded_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='上传用户'
    )

    # 导入统计
    total_records = models.IntegerField(default=0, verbose_name='总记录数')
    success_records = models.IntegerField(default=0, verbose_name='成功记录数')
    failed_records = models.IntegerField(default=0, verbose_name='失败记录数')
    processed_records = models.IntegerField(default=0, verbose_name='已处理记录数')

    # 数据源信息
    data_source = models.CharField(max_length=50, verbose_name='数据源类型')  # excel, csv, jira_api
    source_file_name = models.CharField(max_length=255, verbose_name='源文件名', blank=True)
    source_file_size = models.BigIntegerField(verbose_name='文件大小(字节)', null=True, blank=True)

    # 处理状态和进度
    STATUS_CHOICES = [
        ('uploaded', '已上传'),
        ('queued', '排队中'),
        ('processing', '处理中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded', verbose_name='处理状态')
    progress_percentage = models.FloatField(default=0.0, verbose_name='处理进度百分比')
    current_step = models.CharField(max_length=100, verbose_name='当前处理步骤', blank=True)

    # 队列信息
    queue_position = models.IntegerField(default=0, verbose_name='队列位置')
    estimated_wait_time = models.IntegerField(default=0, verbose_name='预估等待时间(秒)')

    # 处理时间
    processing_started_at = models.DateTimeField(verbose_name='开始处理时间', null=True, blank=True)
    processing_completed_at = models.DateTimeField(verbose_name='处理完成时间', null=True, blank=True)

    # 错误信息
    error_message = models.TextField(verbose_name='错误信息', blank=True)
    error_details = models.JSONField(default=dict, verbose_name='详细错误信息')

    # 任务ID（用于异步任务追踪）
    task_id = models.CharField(max_length=100, verbose_name='任务ID', blank=True, db_index=True)

    class Meta:
        verbose_name = '导入批次'
        verbose_name_plural = '导入批次'
        ordering = ['-import_timestamp']
        indexes = [
            models.Index(fields=['status', 'uploaded_by']),
            models.Index(fields=['task_id']),
            models.Index(fields=['queue_position', 'status']),
        ]

### 3.4 任务队列管理模型
```python
class TaskQueue(models.Model):
    """任务队列管理模型"""
    TASK_TYPE_CHOICES = [
        ('data_import', '数据导入'),
        ('data_analysis', '数据分析'),
        ('rule_import', '规则导入'),
        ('batch_export', '批量导出'),
    ]

    PRIORITY_CHOICES = [
        (1, '低优先级'),
        (2, '普通优先级'),
        (3, '高优先级'),
        (4, '紧急优先级'),
    ]

    task_id = models.CharField(max_length=100, unique=True, verbose_name='任务ID')
    task_type = models.CharField(max_length=20, choices=TASK_TYPE_CHOICES, verbose_name='任务类型')
    task_name = models.CharField(max_length=200, verbose_name='任务名称')

    # 用户信息
    created_by = models.ForeignKey('auth.User', on_delete=models.CASCADE, verbose_name='创建用户')

    # 任务参数
    task_params = models.JSONField(default=dict, verbose_name='任务参数')

    # 优先级和队列
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2, verbose_name='优先级')
    queue_position = models.IntegerField(default=0, verbose_name='队列位置')

    # 状态和进度
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='任务状态')
    progress = models.FloatField(default=0.0, verbose_name='执行进度')
    current_step = models.CharField(max_length=100, verbose_name='当前步骤', blank=True)

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    started_at = models.DateTimeField(verbose_name='开始时间', null=True, blank=True)
    completed_at = models.DateTimeField(verbose_name='完成时间', null=True, blank=True)
    estimated_duration = models.IntegerField(default=0, verbose_name='预估执行时间(秒)')

    # 结果和错误
    result_data = models.JSONField(default=dict, verbose_name='执行结果')
    error_message = models.TextField(verbose_name='错误信息', blank=True)

    # 关联对象
    related_batch_id = models.CharField(max_length=50, verbose_name='关联批次ID', blank=True, db_index=True)

    class Meta:
        verbose_name = '任务队列'
        verbose_name_plural = '任务队列'
        ordering = ['priority', 'queue_position', 'created_at']
        indexes = [
            models.Index(fields=['status', 'priority', 'queue_position']),
            models.Index(fields=['created_by', 'status']),
            models.Index(fields=['task_type', 'status']),
        ]

### 3.5 统计分析模型
```python
class DefectAnalysis(models.Model):
    ANALYSIS_TYPE_CHOICES = [
        ('abc_summary', 'A/B/C类问题汇总'),
        ('model_summary', '机型缺陷汇总'),
        ('daily_trend', '每日趋势'),
        ('category_distribution', '分类分布'),
        ('commonality_analysis', '共性问题分析'),
    ]

    analysis_date = models.DateField(verbose_name='分析日期')
    analysis_type = models.CharField(max_length=50, choices=ANALYSIS_TYPE_CHOICES, verbose_name='分析类型')
    analysis_data = models.JSONField(verbose_name='分析数据')
    date_range_start = models.DateField(verbose_name='数据范围开始', null=True, blank=True)
    date_range_end = models.DateField(verbose_name='数据范围结束', null=True, blank=True)

    # 权限控制
    created_by = models.ForeignKey(
        'auth.User',
        on_delete=models.CASCADE,
        verbose_name='创建用户',
        null=True, blank=True
    )
    is_global = models.BooleanField(default=False, verbose_name='是否全局分析')
    import_batch = models.ForeignKey(
        ImportBatch,
        on_delete=models.CASCADE,
        verbose_name='关联导入批次',
        null=True, blank=True
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = '缺陷分析'
        verbose_name_plural = '缺陷分析'
        indexes = [
            models.Index(fields=['analysis_date', 'analysis_type', 'created_by']),
            models.Index(fields=['import_batch', 'analysis_type']),
        ]

### 3.6 用户权限扩展模型
```python
class UserProfile(models.Model):
    """用户权限扩展模型"""
    user = models.OneToOneField('auth.User', on_delete=models.CASCADE, verbose_name='用户')

    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('user', '普通用户'),
    ]
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='user', verbose_name='用户角色')

    # 权限设置
    can_view_all_data = models.BooleanField(default=False, verbose_name='可查看全部数据')
    can_manage_rules = models.BooleanField(default=False, verbose_name='可管理分类规则')
    can_export_data = models.BooleanField(default=True, verbose_name='可导出数据')

    # 配额限制
    max_import_size = models.BigIntegerField(default=10*1024*1024, verbose_name='最大导入文件大小(字节)')  # 10MB
    max_records_per_import = models.IntegerField(default=10000, verbose_name='单次导入最大记录数')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '用户配置'
        verbose_name_plural = '用户配置'
```

## 4. 核心服务设计

### 4.1 异步任务队列服务
```python
class TaskQueueService:
    """异步任务队列服务"""

    def __init__(self):
        self.redis_client = self._get_redis_client()

    def submit_import_task(self, user, file_path, batch_name=None, priority=2):
        """提交数据导入任务"""
        import uuid
        from datetime import datetime

        # 生成任务ID
        task_id = f"import_{user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 创建导入批次
        batch_id = f"{user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 获取文件信息
        import os
        file_size = os.path.getsize(file_path)
        file_name = os.path.basename(file_path)

        # 创建批次记录
        batch = ImportBatch.objects.create(
            batch_id=batch_id,
            batch_name=batch_name or f"导入_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            uploaded_by=user,
            data_source=self._get_file_type(file_path),
            source_file_name=file_name,
            source_file_size=file_size,
            status='queued',
            task_id=task_id
        )

        # 创建任务记录
        task = TaskQueue.objects.create(
            task_id=task_id,
            task_type='data_import',
            task_name=f"导入数据 - {file_name}",
            created_by=user,
            priority=priority,
            task_params={
                'file_path': file_path,
                'batch_id': batch_id,
                'batch_name': batch_name
            },
            related_batch_id=batch_id,
            estimated_duration=self._estimate_import_duration(file_size)
        )

        # 更新队列位置
        self._update_queue_positions()

        # 提交到Celery队列
        from .tasks import process_data_import
        process_data_import.delay(task_id)

        return {
            'task_id': task_id,
            'batch_id': batch_id,
            'queue_position': task.queue_position,
            'estimated_wait_time': self._calculate_wait_time(task.queue_position)
        }

    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            task = TaskQueue.objects.get(task_id=task_id)
            batch = ImportBatch.objects.get(task_id=task_id)

            return {
                'task_id': task_id,
                'status': task.status,
                'progress': task.progress,
                'current_step': task.current_step,
                'queue_position': task.queue_position,
                'estimated_wait_time': self._calculate_wait_time(task.queue_position),
                'batch_info': {
                    'batch_id': batch.batch_id,
                    'total_records': batch.total_records,
                    'processed_records': batch.processed_records,
                    'success_records': batch.success_records,
                    'failed_records': batch.failed_records,
                    'progress_percentage': batch.progress_percentage
                },
                'error_message': task.error_message
            }
        except (TaskQueue.DoesNotExist, ImportBatch.DoesNotExist):
            return {'error': '任务不存在'}

    def cancel_task(self, task_id, user):
        """取消任务"""
        try:
            task = TaskQueue.objects.get(task_id=task_id, created_by=user)
            if task.status in ['pending', 'running']:
                task.status = 'cancelled'
                task.save()

                # 更新批次状态
                ImportBatch.objects.filter(task_id=task_id).update(status='cancelled')

                # 撤销Celery任务
                from celery import current_app
                current_app.control.revoke(task_id, terminate=True)

                return True
        except TaskQueue.DoesNotExist:
            pass
        return False

    def get_user_tasks(self, user, status=None):
        """获取用户任务列表"""
        queryset = TaskQueue.objects.filter(created_by=user)
        if status:
            queryset = queryset.filter(status=status)
        return queryset.order_by('-created_at')

    def _update_queue_positions(self):
        """更新队列位置"""
        pending_tasks = TaskQueue.objects.filter(status='pending').order_by('priority', 'created_at')
        for i, task in enumerate(pending_tasks):
            task.queue_position = i + 1
            task.save(update_fields=['queue_position'])

    def _calculate_wait_time(self, queue_position):
        """计算预估等待时间"""
        if queue_position <= 1:
            return 0

        # 基于历史数据计算平均处理时间
        avg_processing_time = 300  # 5分钟默认值
        return (queue_position - 1) * avg_processing_time

    def _estimate_import_duration(self, file_size):
        """预估导入时间"""
        # 基于文件大小预估处理时间（每MB约30秒）
        mb_size = file_size / (1024 * 1024)
        return int(mb_size * 30)

    def _get_file_type(self, file_path):
        """获取文件类型"""
        import os
        ext = os.path.splitext(file_path)[1].lower()
        if ext in ['.xlsx', '.xls']:
            return 'excel'
        elif ext == '.csv':
            return 'csv'
        else:
            return 'unknown'

    def _get_redis_client(self):
        """获取Redis客户端"""
        import redis
        return redis.Redis(host='localhost', port=6379, db=0)

### 4.2 数据导入服务
```python
class DefectDataImportService:
    """缺陷数据导入服务"""

    def __init__(self, user, task_id=None):
        self.user = user
        self.user_profile = UserProfile.objects.get_or_create(user=user)[0]
        self.task_id = task_id

    def update_progress(self, progress, step_description):
        """更新任务进度"""
        if self.task_id:
            TaskQueue.objects.filter(task_id=self.task_id).update(
                progress=progress,
                current_step=step_description
            )

            # 同时更新批次进度
            ImportBatch.objects.filter(task_id=self.task_id).update(
                progress_percentage=progress,
                current_step=step_description
            )

    def process_import_async(self, file_path, batch_id):
        """异步处理数据导入"""
        try:
            # 更新状态为处理中
            batch = ImportBatch.objects.get(batch_id=batch_id)
            batch.status = 'processing'
            batch.processing_started_at = timezone.now()
            batch.save()

            self.update_progress(5, "开始处理数据")

            # 检查权限和配额
            self._check_import_permissions(file_path)
            self.update_progress(10, "权限检查完成")

            # 执行导入逻辑
            result = self._process_import(file_path, batch, batch.data_source)

            # 更新完成状态
            batch.status = 'completed'
            batch.processing_completed_at = timezone.now()
            batch.save()

            self.update_progress(100, "处理完成")

            # 发送完成通知
            self._send_completion_notification(batch, result)

            return result

        except Exception as e:
            # 更新失败状态
            batch.status = 'failed'
            batch.error_message = str(e)
            batch.processing_completed_at = timezone.now()
            batch.save()

            if self.task_id:
                TaskQueue.objects.filter(task_id=self.task_id).update(
                    status='failed',
                    error_message=str(e)
                )

            # 发送失败通知
            self._send_failure_notification(batch, str(e))
            raise

    def import_from_csv(self, file_path, batch_name=None):
        """从CSV导入缺陷数据"""
        # 类似Excel导入逻辑
        pass

    def import_from_jira_api(self, jql_query, batch_name=None):
        """从JIRA API导入缺陷数据"""
        # 创建API导入批次
        batch = self.create_import_batch(
            batch_name=batch_name or f"JIRA查询_{jql_query[:50]}",
            data_source='jira_api'
        )
        # 执行API导入逻辑
        pass

    def _check_import_permissions(self, file_path):
        """检查导入权限和配额"""
        file_size = os.path.getsize(file_path)

        if file_size > self.user_profile.max_import_size:
            raise PermissionError(f"文件大小超过限制: {file_size} > {self.user_profile.max_import_size}")

    def _process_import(self, file_path, batch, data_source):
        """处理导入数据"""
        # 读取和验证数据
        raw_data = self._read_data(file_path, data_source)

        if len(raw_data) > self.user_profile.max_records_per_import:
            raise PermissionError(f"记录数超过限制: {len(raw_data)} > {self.user_profile.max_records_per_import}")

        # 数据转换和分类
        processed_data = []
        success_count = 0
        failed_count = 0

        for row_data in raw_data:
            try:
                # 数据转换
                defect_data = self.transform_data(row_data)

                # 添加批次信息
                defect_data.update({
                    'import_batch_id': batch.batch_id,
                    'import_timestamp': batch.import_timestamp,
                    'uploaded_by': self.user
                })

                # 自动分类
                classification_result = self.auto_classify(defect_data)
                defect_data.update(classification_result)

                # 保存记录
                DefectRecord.objects.create(**defect_data)
                success_count += 1

            except Exception as e:
                failed_count += 1
                # 记录错误日志

        # 更新批次统计
        batch.total_records = len(raw_data)
        batch.success_records = success_count
        batch.failed_records = failed_count
        batch.save()

        return {
            'batch_id': batch.batch_id,
            'total': len(raw_data),
            'success': success_count,
            'failed': failed_count
        }

    def _send_completion_notification(self, batch, result):
        """发送完成通知"""
        from .notification_service import NotificationService
        notification_service = NotificationService()

        notification_service.send_user_notification(
            user=batch.uploaded_by,
            notification_type='import_completed',
            title='数据导入完成',
            message=f"批次 {batch.batch_name} 导入完成，成功处理 {result['success']} 条记录",
            data={
                'batch_id': batch.batch_id,
                'result': result
            }
        )

    def _send_failure_notification(self, batch, error_message):
        """发送失败通知"""
        from .notification_service import NotificationService
        notification_service = NotificationService()

        notification_service.send_user_notification(
            user=batch.uploaded_by,
            notification_type='import_failed',
            title='数据导入失败',
            message=f"批次 {batch.batch_name} 导入失败：{error_message}",
            data={
                'batch_id': batch.batch_id,
                'error': error_message
            }
        )

    def validate_data(self, data):
        """数据验证"""
        pass

    def transform_data(self, raw_data):
        """数据转换"""
        pass

    def auto_classify(self, defect_data):
        """自动分类"""
        pass

### 4.3 Celery异步任务
```python
# tasks.py
from celery import shared_task
from django.contrib.auth.models import User
from .models import TaskQueue, ImportBatch
from .services import DefectDataImportService
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True)
def process_data_import(self, task_id):
    """处理数据导入任务"""
    try:
        # 获取任务信息
        task = TaskQueue.objects.get(task_id=task_id)
        task.status = 'running'
        task.started_at = timezone.now()
        task.save()

        # 获取任务参数
        file_path = task.task_params['file_path']
        batch_id = task.task_params['batch_id']
        user = task.created_by

        # 创建导入服务实例
        import_service = DefectDataImportService(user, task_id)

        # 执行导入
        result = import_service.process_import_async(file_path, batch_id)

        # 更新任务状态
        task.status = 'completed'
        task.completed_at = timezone.now()
        task.result_data = result
        task.save()

        logger.info(f"数据导入任务 {task_id} 完成")
        return result

    except Exception as e:
        logger.error(f"数据导入任务 {task_id} 失败: {str(e)}")

        # 更新任务状态
        TaskQueue.objects.filter(task_id=task_id).update(
            status='failed',
            error_message=str(e),
            completed_at=timezone.now()
        )

        raise

@shared_task
def cleanup_completed_tasks():
    """清理已完成的任务（定期执行）"""
    from datetime import timedelta
    from django.utils import timezone

    # 删除7天前的已完成任务
    cutoff_date = timezone.now() - timedelta(days=7)

    completed_tasks = TaskQueue.objects.filter(
        status__in=['completed', 'failed', 'cancelled'],
        completed_at__lt=cutoff_date
    )

    count = completed_tasks.count()
    completed_tasks.delete()

    logger.info(f"清理了 {count} 个已完成的任务")
    return count

### 4.4 WebSocket通知服务
```python
class NotificationService:
    """实时通知服务"""

    def __init__(self):
        self.channel_layer = get_channel_layer()

    def send_user_notification(self, user, notification_type, title, message, data=None):
        """发送用户通知"""
        notification_data = {
            'type': 'user_notification',
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'timestamp': timezone.now().isoformat(),
            'data': data or {}
        }

        # 发送到用户专用频道
        user_channel = f"user_{user.id}"

        async_to_sync(self.channel_layer.group_send)(
            user_channel,
            {
                'type': 'send_notification',
                'notification': notification_data
            }
        )

        # 同时保存到数据库（可选）
        self._save_notification_to_db(user, notification_data)

    def send_progress_update(self, user, task_id, progress, step_description):
        """发送进度更新"""
        progress_data = {
            'type': 'progress_update',
            'task_id': task_id,
            'progress': progress,
            'step_description': step_description,
            'timestamp': timezone.now().isoformat()
        }

        user_channel = f"user_{user.id}"

        async_to_sync(self.channel_layer.group_send)(
            user_channel,
            {
                'type': 'send_progress',
                'progress': progress_data
            }
        )

    def _save_notification_to_db(self, user, notification_data):
        """保存通知到数据库"""
        # 可以创建一个Notification模型来保存历史通知
        pass

# WebSocket消费者
class NotificationConsumer(AsyncWebsocketConsumer):
    """WebSocket通知消费者"""

    async def connect(self):
        self.user = self.scope["user"]
        if self.user.is_authenticated:
            self.user_group_name = f"user_{self.user.id}"

            # 加入用户组
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )

            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

    async def send_notification(self, event):
        """发送通知消息"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'data': event['notification']
        }))

    async def send_progress(self, event):
        """发送进度更新"""
        await self.send(text_data=json.dumps({
            'type': 'progress',
            'data': event['progress']
        }))
```
```

### 4.2 分类规则管理服务
```python
class ClassificationRuleService:
    """分类规则管理服务"""

    def __init__(self, user):
        self.user = user
        self.permission_service = DataPermissionService(user)

    def import_rules_from_file(self, file_path, batch_name=None):
        """从文件导入分类规则"""
        import uuid
        from datetime import datetime

        # 创建规则导入批次ID
        batch_id = f"rules_{self.user.username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        try:
            # 读取规则文件（支持Excel、CSV、JSON格式）
            rules_data = self._read_rules_file(file_path)

            # 验证规则数据
            validated_rules = self._validate_rules_data(rules_data)

            # 保存规则
            created_rules = []
            for rule_data in validated_rules:
                rule = ClassificationRule.objects.create(
                    rule_type=rule_data['rule_type'],
                    rule_name=rule_data['rule_name'],
                    pattern=rule_data['pattern'],
                    target_value=rule_data['target_value'],
                    priority=rule_data.get('priority', 0),
                    description=rule_data.get('description', ''),
                    scope='personal',  # 普通用户导入的规则默认为个人规则
                    created_by=self.user,
                    import_batch_id=batch_id,
                    source_file_name=os.path.basename(file_path)
                )
                created_rules.append(rule)

            return {
                'batch_id': batch_id,
                'total_rules': len(created_rules),
                'success_count': len(created_rules),
                'rules': created_rules
            }

        except Exception as e:
            raise ValueError(f"规则导入失败: {str(e)}")

    def export_rules_to_file(self, file_format='excel', rule_scope='personal'):
        """导出分类规则到文件"""
        # 获取用户可访问的规则
        rules = self.get_accessible_rules(rule_scope)

        if file_format == 'excel':
            return self._export_rules_to_excel(rules)
        elif file_format == 'csv':
            return self._export_rules_to_csv(rules)
        elif file_format == 'json':
            return self._export_rules_to_json(rules)
        else:
            raise ValueError(f"不支持的导出格式: {file_format}")

    def get_accessible_rules(self, scope=None):
        """获取用户可访问的规则"""
        queryset = ClassificationRule.objects.filter(is_active=True)

        if self.permission_service.is_admin:
            # 管理员可以看到所有规则
            if scope:
                queryset = queryset.filter(scope=scope)
        else:
            # 普通用户只能看到全局规则和自己的个人规则
            queryset = queryset.filter(
                models.Q(scope='global') |
                models.Q(scope='personal', created_by=self.user)
            )
            if scope == 'personal':
                queryset = queryset.filter(scope='personal', created_by=self.user)
            elif scope == 'global':
                queryset = queryset.filter(scope='global')

        return queryset.order_by('scope', '-priority', 'rule_type')

    def create_rule(self, rule_data):
        """创建分类规则"""
        # 检查权限
        scope = rule_data.get('scope', 'personal')
        if scope == 'global' and not self.permission_service.can_manage_rules():
            raise PermissionError("没有创建全局规则的权限")

        rule = ClassificationRule.objects.create(
            **rule_data,
            created_by=self.user,
            scope=scope
        )
        return rule

    def update_rule(self, rule_id, rule_data):
        """更新分类规则"""
        try:
            rule = ClassificationRule.objects.get(id=rule_id)
            if not rule.can_edit(self.user):
                raise PermissionError("没有编辑此规则的权限")

            for key, value in rule_data.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            rule.save()
            return rule

        except ClassificationRule.DoesNotExist:
            raise ValueError("规则不存在")

    def delete_rule(self, rule_id):
        """删除分类规则"""
        try:
            rule = ClassificationRule.objects.get(id=rule_id)
            if not rule.can_edit(self.user):
                raise PermissionError("没有删除此规则的权限")

            rule.delete()
            return True

        except ClassificationRule.DoesNotExist:
            raise ValueError("规则不存在")

    def test_rule(self, pattern, test_text):
        """测试分类规则"""
        import re
        try:
            if re.search(pattern, test_text, re.IGNORECASE):
                return {'match': True, 'message': '匹配成功'}
            else:
                return {'match': False, 'message': '未匹配'}
        except re.error as e:
            return {'match': False, 'message': f'正则表达式错误: {str(e)}'}

    def _read_rules_file(self, file_path):
        """读取规则文件"""
        import pandas as pd
        import json

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
            return df.to_dict('records')
        elif file_ext == '.csv':
            df = pd.read_csv(file_path)
            return df.to_dict('records')
        elif file_ext == '.json':
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _validate_rules_data(self, rules_data):
        """验证规则数据"""
        required_fields = ['rule_type', 'rule_name', 'pattern', 'target_value']
        validated_rules = []

        for i, rule_data in enumerate(rules_data):
            # 检查必需字段
            for field in required_fields:
                if field not in rule_data or not rule_data[field]:
                    raise ValueError(f"第{i+1}行缺少必需字段: {field}")

            # 验证规则类型
            if rule_data['rule_type'] not in ['project', 'category', 'commonality']:
                raise ValueError(f"第{i+1}行规则类型无效: {rule_data['rule_type']}")

            # 验证正则表达式
            try:
                import re
                re.compile(rule_data['pattern'])
            except re.error:
                raise ValueError(f"第{i+1}行正则表达式无效: {rule_data['pattern']}")

            validated_rules.append(rule_data)

        return validated_rules

### 4.3 自动分类服务
```python
class AutoClassificationService:
    """自动分类服务"""

    def __init__(self, user):
        self.user = user
        self.rule_service = ClassificationRuleService(user)

    def extract_project(self, title):
        """从标题提取项目信息"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='project')

        for rule in rules:
            if self._match_pattern(rule.pattern, title):
                return rule.target_value

        return "未知项目"

    def extract_summary_text(self, title):
        """从标题提取摘要文本"""
        # 简单的摘要提取逻辑，可以根据需要扩展
        return title[:100] if len(title) > 100 else title

    def classify_bug_category(self, title, description=""):
        """缺陷分类"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='category')

        text_to_match = f"{title} {description}".strip()

        for rule in rules:
            if self._match_pattern(rule.pattern, text_to_match):
                return rule.target_value

        return "未分类"

    def identify_commonality(self, bug_category):
        """共性问题识别"""
        rules = self.rule_service.get_accessible_rules().filter(rule_type='commonality')

        for rule in rules:
            if self._match_pattern(rule.pattern, bug_category):
                return rule.target_value

        return "无共性"

    def batch_classify(self, defect_records):
        """批量分类"""
        results = []
        for record in defect_records:
            classification = {
                'project': self.extract_project(record.original_title),
                'summary_text': self.extract_summary_text(record.original_title),
                'bug_category': self.classify_bug_category(record.original_title),
            }
            classification['commonality'] = self.identify_commonality(classification['bug_category'])
            results.append(classification)

        return results

    def _match_pattern(self, pattern, text):
        """匹配模式"""
        import re
        try:
            return bool(re.search(pattern, text, re.IGNORECASE))
        except re.error:
            return False
```

### 4.4 数据权限管理服务
```python
class DataPermissionService:
    """数据权限管理服务"""

    def __init__(self, user):
        self.user = user
        self.user_profile = UserProfile.objects.get_or_create(user=user)[0]
        self.is_admin = self.user_profile.role == 'admin' or self.user_profile.can_view_all_data

    def get_accessible_defects(self, queryset=None):
        """获取用户可访问的缺陷数据"""
        if queryset is None:
            queryset = DefectRecord.objects.all()

        if self.is_admin:
            return queryset
        else:
            return queryset.filter(uploaded_by=self.user)

    def get_accessible_batches(self, queryset=None):
        """获取用户可访问的导入批次"""
        if queryset is None:
            queryset = ImportBatch.objects.all()

        if self.is_admin:
            return queryset
        else:
            return queryset.filter(uploaded_by=self.user)

    def can_view_defect(self, defect_record):
        """检查是否可以查看特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_edit_defect(self, defect_record):
        """检查是否可以编辑特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_delete_defect(self, defect_record):
        """检查是否可以删除特定缺陷记录"""
        if self.is_admin:
            return True
        return defect_record.uploaded_by == self.user

    def can_manage_rules(self):
        """检查是否可以管理分类规则"""
        return self.is_admin or self.user_profile.can_manage_rules

    def can_export_data(self):
        """检查是否可以导出数据"""
        return self.user_profile.can_export_data

### 4.5 统计分析服务
```python
class DefectStatisticsService:
    """缺陷统计分析服务"""

    def __init__(self, user):
        self.user = user
        self.permission_service = DataPermissionService(user)

    def get_abc_summary(self, date_range=None, import_batch_id=None):
        """A、B、C类问题汇总"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 统计逻辑
        summary = {
            'A类问题': queryset.filter(priority__in=['Blocker', 'Critical']).count(),
            'B类问题': queryset.filter(priority='Major').count(),
            'C类问题': queryset.filter(priority__in=['Minor', 'Trivial']).count(),
        }
        return summary

    def get_model_defect_summary(self, date_range=None, import_batch_id=None):
        """机型缺陷汇总"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按项目统计
        from django.db.models import Count
        summary = queryset.values('project').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(summary)

    def get_daily_trend(self, date_range=None, import_batch_id=None):
        """每日缺陷提报趋势"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按日期统计
        from django.db.models import Count
        trend = queryset.values('created_date').annotate(
            defect_count=Count('id')
        ).order_by('created_date')

        return list(trend)

    def get_category_distribution(self, date_range=None, import_batch_id=None):
        """缺陷分类分布"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按分类统计
        from django.db.models import Count
        distribution = queryset.values('bug_category').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(distribution)

    def get_commonality_analysis(self, date_range=None, import_batch_id=None):
        """共性问题分析"""
        queryset = self.permission_service.get_accessible_defects()

        if date_range:
            queryset = queryset.filter(created_date__range=date_range)

        if import_batch_id:
            queryset = queryset.filter(import_batch_id=import_batch_id)

        # 按共性问题统计
        from django.db.models import Count
        analysis = queryset.values('commonality').annotate(
            defect_count=Count('id')
        ).order_by('-defect_count')

        return list(analysis)

    def export_analysis_report(self, analysis_type, date_range=None, import_batch_id=None):
        """导出分析报告"""
        if not self.permission_service.can_export_data():
            raise PermissionError("没有数据导出权限")

        # 根据分析类型生成报告
        if analysis_type == 'abc_summary':
            data = self.get_abc_summary(date_range, import_batch_id)
        elif analysis_type == 'model_summary':
            data = self.get_model_defect_summary(date_range, import_batch_id)
        elif analysis_type == 'daily_trend':
            data = self.get_daily_trend(date_range, import_batch_id)
        elif analysis_type == 'category_distribution':
            data = self.get_category_distribution(date_range, import_batch_id)
        elif analysis_type == 'commonality_analysis':
            data = self.get_commonality_analysis(date_range, import_batch_id)
        else:
            raise ValueError(f"不支持的分析类型: {analysis_type}")

        return data

    def export_batch_data(self, import_batch_id):
        """导出指定批次的原始数据"""
        if not self.permission_service.can_export_data():
            raise PermissionError("没有数据导出权限")

        # 检查批次权限
        try:
            batch = ImportBatch.objects.get(batch_id=import_batch_id)
            if not self.permission_service.is_admin and batch.uploaded_by != self.user:
                raise PermissionError("没有权限访问该批次数据")
        except ImportBatch.DoesNotExist:
            raise ValueError("批次不存在")

        # 获取批次数据
        queryset = DefectRecord.objects.filter(import_batch_id=import_batch_id)
        return queryset
```

## 5. API接口设计

### 5.1 缺陷管理接口
```python
# 缺陷数据管理
POST /api/defects/import/          # 异步导入缺陷数据（返回任务ID）
GET  /api/defects/list/            # 获取缺陷列表（支持权限过滤）
PUT  /api/defects/<id>/            # 更新缺陷信息（权限检查）
DELETE /api/defects/<id>/          # 删除缺陷（权限检查）
POST /api/defects/batch-classify/  # 批量分类
GET  /api/defects/export/          # 导出缺陷数据（权限检查）
GET  /api/defects/batch/<batch_id>/export/ # 导出指定批次数据

# 导入批次管理
GET  /api/import-batches/          # 获取导入批次列表（权限过滤）
GET  /api/import-batches/<batch_id>/ # 获取批次详情
DELETE /api/import-batches/<batch_id>/ # 删除批次（权限检查）
GET  /api/import-batches/<batch_id>/defects/ # 获取批次下的缺陷数据

# 任务队列管理
POST /api/tasks/submit/            # 提交异步任务
GET  /api/tasks/                   # 获取用户任务列表
GET  /api/tasks/<task_id>/         # 获取任务详情和状态
DELETE /api/tasks/<task_id>/       # 取消任务
GET  /api/tasks/<task_id>/progress/ # 获取任务进度
GET  /api/tasks/queue-status/      # 获取队列状态

# 分类规则管理
GET  /api/classification-rules/    # 获取分类规则（权限过滤）
POST /api/classification-rules/    # 创建分类规则（权限检查）
PUT  /api/classification-rules/<id>/ # 更新分类规则（权限检查）
DELETE /api/classification-rules/<id>/ # 删除分类规则（权限检查）
POST /api/classification-rules/test/ # 测试分类规则
POST /api/classification-rules/import/ # 导入分类规则（普通用户可用）
GET  /api/classification-rules/export/ # 导出分类规则
GET  /api/classification-rules/template/ # 下载规则模板文件

# 实时通知
WebSocket /ws/notifications/       # WebSocket连接获取实时通知
GET  /api/notifications/           # 获取历史通知列表
PUT  /api/notifications/<id>/read/ # 标记通知为已读

# 用户权限管理（仅管理员）
GET  /api/users/                   # 获取用户列表
PUT  /api/users/<id>/permissions/  # 更新用户权限
GET  /api/users/profile/           # 获取当前用户配置
PUT  /api/users/profile/           # 更新当前用户配置
```

### 5.2 统计分析接口
```python
# 统计分析（支持权限过滤和批次筛选）
GET /api/analysis/abc-summary/         # A、B、C类问题汇总
    # 参数: date_range, import_batch_id
GET /api/analysis/model-summary/       # 机型缺陷汇总
    # 参数: date_range, import_batch_id
GET /api/analysis/daily-trend/         # 每日趋势
    # 参数: date_range, import_batch_id
GET /api/analysis/category-distribution/ # 分类分布
    # 参数: date_range, import_batch_id
GET /api/analysis/commonality/         # 共性问题分析
    # 参数: date_range, import_batch_id
GET /api/analysis/export/              # 导出报告（权限检查）
    # 参数: analysis_type, date_range, import_batch_id
GET /api/analysis/dashboard/           # 仪表板数据（权限过滤）
    # 参数: import_batch_id（可选，用于查看特定批次分析）

# 批次分析
GET /api/analysis/batch/<batch_id>/    # 获取指定批次的完整分析报告
```

### 5.3 权限控制说明
```python
# API权限控制装饰器示例
from functools import wraps
from django.http import JsonResponse

def permission_required(permission_type):
    """权限检查装饰器"""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({'error': '需要登录'}, status=401)

            permission_service = DataPermissionService(request.user)

            if permission_type == 'manage_rules' and not permission_service.can_manage_rules():
                return JsonResponse({'error': '没有管理规则权限'}, status=403)

            if permission_type == 'export_data' and not permission_service.can_export_data():
                return JsonResponse({'error': '没有数据导出权限'}, status=403)

            # 将权限服务添加到request中，供视图使用
            request.permission_service = permission_service
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@permission_required('export_data')
def export_defects(request):
    """导出缺陷数据"""
    queryset = request.permission_service.get_accessible_defects()
    # 导出逻辑...

@permission_required('manage_rules')
def create_classification_rule(request):
    """创建分类规则"""
    # 创建规则逻辑...
```

## 6. 数据权限管理设计

### 6.1 权限模型设计

#### 6.1.1 用户角色
- **管理员(admin)**: 可以查看全量数据，管理所有用户和规则
- **普通用户(user)**: 只能查看自己上传的数据，有限的操作权限

#### 6.1.2 权限矩阵
| 功能 | 管理员 | 普通用户 | 说明 |
|------|--------|----------|------|
| 查看全部缺陷数据 | ✅ | ❌ | 普通用户只能看自己上传的数据 |
| 导入缺陷数据 | ✅ | ✅ | 都可以导入，但有配额限制 |
| 编辑缺陷数据 | ✅ | ✅* | 普通用户只能编辑自己上传的数据 |
| 删除缺陷数据 | ✅ | ✅* | 普通用户只能删除自己上传的数据 |
| 管理全局分类规则 | ✅ | ❌ | 只有管理员可以管理全局规则 |
| 导入个人分类规则 | ✅ | ✅ | 普通用户可以导入自己的分类规则 |
| 管理个人分类规则 | ✅ | ✅ | 普通用户可以管理自己的分类规则 |
| 导出数据 | ✅ | ✅* | 可配置，普通用户只能导出自己的数据 |
| 查看统计分析 | ✅ | ✅* | 普通用户只能看基于自己数据的分析 |
| 用户管理 | ✅ | ❌ | 只有管理员可以管理用户 |

### 6.2 导入批次标识设计

#### 6.2.1 批次ID生成规则
```python
# 批次ID格式: {用户名}_{日期时间}_{随机字符}
# 示例: zhangsan_20250702_143025_a1b2c3d4
batch_id = f"{username}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
```

#### 6.2.2 批次管理功能
- **批次列表**: 显示用户的所有导入批次
- **批次详情**: 显示批次的导入统计和处理状态
- **批次数据**: 查看和导出特定批次的数据
- **批次分析**: 基于特定批次生成分析报告

### 6.3 数据隔离机制

#### 6.3.1 数据库层隔离
```python
# 在所有查询中自动添加用户过滤条件
class DefectQuerySet(models.QuerySet):
    def for_user(self, user):
        """根据用户权限过滤数据"""
        user_profile = UserProfile.objects.get_or_create(user=user)[0]
        if user_profile.role == 'admin' or user_profile.can_view_all_data:
            return self
        return self.filter(uploaded_by=user)

class DefectRecord(models.Model):
    # ... 字段定义 ...

    objects = DefectQuerySet.as_manager()
```

#### 6.3.2 API层权限控制
```python
# 在视图中自动应用权限过滤
class DefectListView(APIView):
    def get(self, request):
        permission_service = DataPermissionService(request.user)
        queryset = permission_service.get_accessible_defects()

        # 支持按批次筛选
        batch_id = request.GET.get('batch_id')
        if batch_id:
            queryset = queryset.filter(import_batch_id=batch_id)

        # 序列化和返回数据
        serializer = DefectSerializer(queryset, many=True)
        return Response(serializer.data)
```

### 6.4 配额和限制管理

#### 6.4.1 导入配额
- **文件大小限制**: 默认10MB，管理员可调整
- **记录数限制**: 默认10,000条，管理员可调整
- **并发导入限制**: 同时只能有一个导入任务

#### 6.4.2 存储配额
- **用户数据总量**: 可设置用户最大存储空间
- **批次数量限制**: 限制用户最大批次数量
- **数据保留期**: 可设置数据自动清理策略

### 6.5 分类规则导入功能设计

#### 6.5.1 规则导入格式支持

**Excel格式模板**:
| rule_type | rule_name | pattern | target_value | priority | description |
|-----------|-----------|---------|--------------|----------|-------------|
| project | Weather项目识别 | Weather\|天气 | Weather | 10 | 识别天气相关项目 |
| category | UI问题分类 | 界面\|显示\|布局\|UI | UI问题 | 5 | 识别界面显示类问题 |
| commonality | 界面共性问题 | UI问题 | 界面显示类 | 1 | UI问题的共性分类 |

**CSV格式示例**:
```csv
rule_type,rule_name,pattern,target_value,priority,description
project,Weather项目识别,Weather|天气,Weather,10,识别天气相关项目
category,UI问题分类,界面|显示|布局|UI,UI问题,5,识别界面显示类问题
commonality,界面共性问题,UI问题,界面显示类,1,UI问题的共性分类
```

**JSON格式示例**:
```json
[
  {
    "rule_type": "project",
    "rule_name": "Weather项目识别",
    "pattern": "Weather|天气",
    "target_value": "Weather",
    "priority": 10,
    "description": "识别天气相关项目"
  },
  {
    "rule_type": "category",
    "rule_name": "UI问题分类",
    "pattern": "界面|显示|布局|UI",
    "target_value": "UI问题",
    "priority": 5,
    "description": "识别界面显示类问题"
  }
]
```

#### 6.5.2 规则导入流程

```mermaid
flowchart TD
    A[用户选择规则文件] --> B{文件格式检查}

    B -->|Excel| C1[读取Excel文件]
    B -->|CSV| C2[读取CSV文件]
    B -->|JSON| C3[读取JSON文件]
    B -->|不支持| D[返回格式错误]

    C1 --> E[解析文件内容]
    C2 --> E
    C3 --> E

    E --> F[验证必需字段]
    F --> G{字段完整?}
    G -->|否| H[返回字段缺失错误]
    G -->|是| I[验证规则类型]

    I --> J{规则类型有效?}
    J -->|否| K[返回类型错误]
    J -->|是| L[验证正则表达式]

    L --> M{正则表达式有效?}
    M -->|否| N[返回正则错误]
    M -->|是| O[检查规则数量限制]

    O --> P{超过限制?}
    P -->|是| Q[返回数量超限错误]
    P -->|否| R[检查重复规则]

    R --> S{存在重复?}
    S -->|是| T[提示重复规则<br/>用户选择覆盖或跳过]
    S -->|否| U[生成导入批次ID]

    T --> V{用户选择}
    V -->|覆盖| W[标记覆盖现有规则]
    V -->|跳过| X[标记跳过重复规则]
    V -->|取消| Y[取消导入]

    W --> U
    X --> U

    U --> Z[批量创建规则记录]
    Z --> AA[更新规则统计]
    AA --> BB[生成导入报告]
    BB --> CC[导入完成]

    D --> DD[结束]
    H --> DD
    K --> DD
    N --> DD
    Q --> DD
    Y --> DD
    CC --> DD
```

**流程说明**:
1. **文件上传**: 用户选择规则文件（Excel/CSV/JSON）
2. **格式验证**: 检查文件格式和必需字段
3. **数据验证**: 验证规则类型、正则表达式有效性
4. **配额检查**: 检查规则数量是否超过用户限制
5. **冲突检查**: 检查是否与现有规则冲突
6. **用户确认**: 对于重复规则，用户选择处理方式
7. **批量导入**: 创建个人规则记录
8. **导入报告**: 显示导入结果和错误信息

#### 6.5.3 规则作用域和优先级
- **全局规则**: 管理员创建，对所有用户生效
- **个人规则**: 用户创建，只对自己生效
- **优先级顺序**: 个人规则 > 全局规则 > 系统默认规则
- **规则合并**: 同类型规则按优先级排序执行

#### 6.5.4 规则管理界面设计
```html
<!-- 规则导入界面 -->
<div class="rule-import-section">
    <h3>导入分类规则</h3>

    <!-- 模板下载 -->
    <div class="template-download">
        <label>下载模板:</label>
        <button onclick="downloadTemplate('excel')">Excel模板</button>
        <button onclick="downloadTemplate('csv')">CSV模板</button>
        <button onclick="downloadTemplate('json')">JSON模板</button>
    </div>

    <!-- 文件上传 -->
    <div class="file-upload">
        <input type="file" id="rule-file" accept=".xlsx,.xls,.csv,.json">
        <button onclick="importRules()">导入规则</button>
    </div>

    <!-- 导入预览 -->
    <div id="import-preview" style="display:none;">
        <h4>导入预览</h4>
        <table id="preview-table">
            <!-- 预览数据 -->
        </table>
        <button onclick="confirmImport()">确认导入</button>
        <button onclick="cancelImport()">取消</button>
    </div>
</div>

<!-- 规则列表 -->
<div class="rule-list-section">
    <h3>我的分类规则</h3>

    <!-- 规则筛选 -->
    <div class="rule-filter">
        <select id="scope-filter">
            <option value="all">全部规则</option>
            <option value="personal">个人规则</option>
            <option value="global">全局规则</option>
        </select>
        <select id="type-filter">
            <option value="all">全部类型</option>
            <option value="project">项目提取</option>
            <option value="category">缺陷分类</option>
            <option value="commonality">共性问题</option>
        </select>
    </div>

    <!-- 规则表格 -->
    <table id="rules-table">
        <thead>
            <tr>
                <th>规则名称</th>
                <th>类型</th>
                <th>匹配模式</th>
                <th>目标值</th>
                <th>优先级</th>
                <th>作用域</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 规则数据 -->
        </tbody>
    </table>
</div>
```

### 6.6 审计和日志

#### 6.5.1 操作日志
```python
class OperationLog(models.Model):
    """操作日志模型"""
    user = models.ForeignKey('auth.User', on_delete=models.CASCADE)
    operation_type = models.CharField(max_length=50)  # import, export, delete, etc.
    target_type = models.CharField(max_length=50)     # defect, batch, rule, etc.
    target_id = models.CharField(max_length=100)
    operation_detail = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
```

#### 6.5.2 数据访问日志
- 记录用户的数据访问行为
- 监控异常访问模式
- 支持数据访问审计

## 7. 前端界面设计

### 7.1 主要页面
1. **缺陷数据管理页面** (`/api/defects/`)
   - **异步数据导入功能**（支持Excel、CSV）
   - 缺陷列表展示（分页、筛选、排序）
   - 批量操作功能（分类、删除、导出）
   - 单个缺陷详情查看和编辑
   - **权限控制**: 普通用户只能看到自己上传的数据
   - **导入状态显示**: 实时显示导入进度和状态

2. **任务管理页面** (`/api/tasks/`)
   - **任务列表**: 显示所有导入任务的状态
   - **实时进度**: 显示当前任务的处理进度
   - **队列状态**: 显示排队位置和预估等待时间
   - **任务操作**: 取消、重试、查看详情
   - **状态筛选**: 按任务状态筛选（进行中、已完成、失败等）

3. **导入批次管理页面** (`/api/import-batches/`)
   - 批次列表展示（按时间倒序）
   - **批次状态显示**: 上传、排队、处理中、已完成、失败
   - 批次详情查看（导入统计、处理状态）
   - 批次数据预览和导出
   - 批次删除功能
   - **权限控制**: 用户只能管理自己的批次

3. **分类规则配置页面** (`/api/classification-rules/`)
   - 规则列表管理（显示全局规则和个人规则）
   - 规则编辑器（支持正则表达式）
   - 规则测试功能
   - 规则优先级调整
   - **规则导入功能**: 支持Excel、CSV、JSON格式
   - **规则导出功能**: 导出个人规则或全局规则
   - **模板下载**: 提供规则导入模板
   - **权限控制**:
     - 普通用户可管理个人规则，查看全局规则
     - 管理员可管理所有规则

4. **统计分析仪表板** (`/api/analysis/dashboard/`)
   - A、B、C类问题统计图表
   - 机型缺陷分布图
   - 每日趋势图
   - 分类分布饼图
   - 共性问题分析表
   - **批次筛选器**: 可选择查看特定批次的分析
   - **权限控制**: 数据范围根据用户权限自动过滤

5. **用户管理页面** (`/api/users/`) - 仅管理员
   - 用户列表和权限配置
   - 配额设置和调整
   - 操作日志查看

### 7.2 界面特性
- 响应式设计，支持移动端访问
- **实时数据更新**: WebSocket推送任务状态和进度
- 交互式图表（Chart.js）
- 数据导出功能
- 时间范围筛选器
- **批次选择器**: 支持按导入批次筛选数据
- **权限提示**: 清晰显示用户权限范围
- **配额显示**: 显示用户当前使用情况和限制
- **实时通知**: Toast提示和通知中心
- **进度显示**: 实时进度条和状态指示器
- **队列可视化**: 显示任务队列状态和等待时间

### 7.3 用户体验设计

#### 7.3.1 权限友好提示
```javascript
// 前端权限检查示例
function checkUserPermissions() {
    fetch('/api/users/profile/')
        .then(response => response.json())
        .then(profile => {
            // 根据权限显示/隐藏功能
            if (!profile.can_manage_rules) {
                document.getElementById('rules-menu').style.display = 'none';
            }

            if (!profile.can_export_data) {
                document.querySelectorAll('.export-btn').forEach(btn => {
                    btn.disabled = true;
                    btn.title = '没有导出权限';
                });
            }

            // 显示配额信息
            updateQuotaDisplay(profile);
        });
}
```

#### 7.3.2 异步导入界面
```html
<!-- 数据导入界面 -->
<div class="import-section">
    <h3>数据导入</h3>

    <!-- 文件上传 -->
    <div class="file-upload">
        <input type="file" id="data-file" accept=".xlsx,.xls,.csv">
        <input type="text" id="batch-name" placeholder="批次名称（可选）">
        <button onclick="submitImportTask()" id="import-btn">开始导入</button>
    </div>

    <!-- 当前任务状态 -->
    <div id="current-task" style="display:none;">
        <h4>当前导入任务</h4>
        <div class="task-info">
            <div class="task-status">
                <span id="task-status-text">排队中</span>
                <span id="queue-position"></span>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progress-fill" style="width: 0%"></div>
                </div>
                <span id="progress-text">0%</span>
            </div>
            <div id="current-step">等待处理...</div>
            <button onclick="cancelTask()" id="cancel-btn">取消任务</button>
        </div>
    </div>
</div>

<!-- 任务历史 -->
<div class="task-history">
    <h3>导入历史</h3>
    <table id="tasks-table">
        <thead>
            <tr>
                <th>任务名称</th>
                <th>状态</th>
                <th>进度</th>
                <th>创建时间</th>
                <th>完成时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <!-- 任务数据 -->
        </tbody>
    </table>
</div>

<!-- 批次选择器 -->
<div class="batch-selector">
    <label>选择导入批次:</label>
    <select id="batch-select">
        <option value="">全部数据</option>
        <option value="batch_001">2025-07-02 14:30 - Excel导入 (1,234条) - 已完成</option>
        <option value="batch_002">2025-07-01 09:15 - CSV导入 (856条) - 处理中</option>
    </select>
    <button onclick="exportBatchData()">导出当前批次</button>
</div>
```

#### 7.3.3 WebSocket和实时通知实现
```javascript
// WebSocket连接管理
class NotificationManager {
    constructor() {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.currentTaskId = null;
    }

    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;

        this.socket = new WebSocket(wsUrl);

        this.socket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.reconnectAttempts = 0;
        };

        this.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        };

        this.socket.onclose = () => {
            console.log('WebSocket连接已关闭');
            this.attemptReconnect();
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
        };
    }

    handleMessage(data) {
        if (data.type === 'notification') {
            this.showNotification(data.data);
        } else if (data.type === 'progress') {
            this.updateProgress(data.data);
        }
    }

    showNotification(notification) {
        // 显示Toast通知
        this.showToast(notification.title, notification.message, notification.notification_type);

        // 如果是导入完成通知，刷新页面数据
        if (notification.notification_type === 'import_completed') {
            this.handleImportCompleted(notification);
        } else if (notification.notification_type === 'import_failed') {
            this.handleImportFailed(notification);
        }
    }

    updateProgress(progress) {
        if (progress.task_id === this.currentTaskId) {
            // 更新进度条
            document.getElementById('progress-fill').style.width = `${progress.progress}%`;
            document.getElementById('progress-text').textContent = `${progress.progress.toFixed(1)}%`;
            document.getElementById('current-step').textContent = progress.step_description;
        }
    }

    showToast(title, message, type) {
        // 创建Toast通知
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${title}</strong>
                <button type="button" class="close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        // 添加到页面
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        toastContainer.appendChild(toast);

        // 自动消失
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container';
        document.body.appendChild(container);
        return container;
    }

    handleImportCompleted(notification) {
        // 隐藏当前任务显示
        document.getElementById('current-task').style.display = 'none';
        this.currentTaskId = null;

        // 刷新批次列表
        refreshBatchList();

        // 刷新任务历史
        refreshTaskHistory();

        // 如果在分析页面，刷新分析数据
        if (window.location.pathname.includes('analysis')) {
            refreshAnalysisData();
        }
    }

    handleImportFailed(notification) {
        // 显示错误状态
        document.getElementById('task-status-text').textContent = '导入失败';
        document.getElementById('current-step').textContent = notification.data.error;

        // 隐藏进度条
        document.querySelector('.progress-container').style.display = 'none';
    }

    attemptReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            setTimeout(() => {
                console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, 2000 * this.reconnectAttempts);
        }
    }
}

// 异步导入功能
async function submitImportTask() {
    const fileInput = document.getElementById('data-file');
    const batchNameInput = document.getElementById('batch-name');

    if (!fileInput.files[0]) {
        alert('请选择要导入的文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('batch_name', batchNameInput.value);

    try {
        // 提交导入任务
        const response = await fetch('/api/defects/import/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        const result = await response.json();

        if (response.ok) {
            // 显示任务状态
            showTaskStatus(result);

            // 开始轮询任务状态
            startTaskPolling(result.task_id);
        } else {
            alert(`导入失败: ${result.error}`);
        }
    } catch (error) {
        alert(`导入失败: ${error.message}`);
    }
}

function showTaskStatus(taskInfo) {
    // 显示当前任务区域
    document.getElementById('current-task').style.display = 'block';

    // 设置任务ID
    notificationManager.currentTaskId = taskInfo.task_id;

    // 显示队列信息
    if (taskInfo.queue_position > 1) {
        document.getElementById('task-status-text').textContent = '排队中';
        document.getElementById('queue-position').textContent =
            `队列位置: ${taskInfo.queue_position}, 预估等待: ${Math.ceil(taskInfo.estimated_wait_time / 60)}分钟`;
    } else {
        document.getElementById('task-status-text').textContent = '准备处理';
        document.getElementById('queue-position').textContent = '';
    }

    // 重置进度
    document.getElementById('progress-fill').style.width = '0%';
    document.getElementById('progress-text').textContent = '0%';
    document.getElementById('current-step').textContent = '等待处理...';
}

async function cancelTask() {
    if (!notificationManager.currentTaskId) return;

    try {
        const response = await fetch(`/api/tasks/${notificationManager.currentTaskId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        });

        if (response.ok) {
            document.getElementById('current-task').style.display = 'none';
            notificationManager.currentTaskId = null;
            notificationManager.showToast('任务已取消', '导入任务已成功取消', 'info');
        }
    } catch (error) {
        console.error('取消任务失败:', error);
    }
}

// 初始化通知管理器
const notificationManager = new NotificationManager();
document.addEventListener('DOMContentLoaded', () => {
    notificationManager.connect();
});
```

## 8. 技术实现方案

### 8.1 分类算法设计
```python
class DefectClassifier:
    """缺陷分类器"""
    
    def __init__(self):
        self.project_patterns = self.load_project_patterns()
        self.category_patterns = self.load_category_patterns()
        self.commonality_mapping = self.load_commonality_mapping()
    
    def classify(self, title, description=""):
        """主分类方法"""
        result = {
            'project': self.extract_project(title),
            'summary_text': self.extract_summary_text(title),
            'bug_category': self.classify_category(title, description),
        }
        result['commonality'] = self.identify_commonality(result['bug_category'])
        return result
```

### 8.2 数据处理流程
```python
class DefectDataProcessor:
    """缺陷数据处理器"""
    
    def process_import_data(self, raw_data):
        """处理导入数据的完整流程"""
        # 1. 数据清洗
        cleaned_data = self.clean_data(raw_data)
        
        # 2. 数据转换
        transformed_data = self.transform_data(cleaned_data)
        
        # 3. 自动分类
        classified_data = self.auto_classify(transformed_data)
        
        # 4. 数据保存
        saved_records = self.save_records(classified_data)
        
        # 5. 触发统计分析
        self.trigger_analysis_update()
        
        return saved_records
```

## 9. 部署和配置

### 9.1 目录结构
```
defect_classification_tool/
├── models/
│   ├── defect_models.py
│   ├── classification_models.py
│   └── analysis_models.py
├── services/
│   ├── import_service.py
│   ├── classification_service.py
│   ├── statistics_service.py
│   └── rule_management_service.py
├── views/
│   ├── defect_views.py
│   ├── classification_views.py
│   └── analysis_views.py
├── templates/
│   ├── defect_management.html
│   ├── classification_rules.html
│   └── analysis_dashboard.html
├── static/
│   ├── css/
│   ├── js/
│   └── charts/
└── utils/
    ├── data_processors.py
    ├── classifiers.py
    └── exporters.py
```

### 9.2 配置文件
```python
# 分类配置
DEFECT_CLASSIFICATION_CONFIG = {
    'AUTO_CLASSIFY_ON_IMPORT': True,

    # 规则导入配置
    'RULE_IMPORT_CONFIG': {
        'SUPPORTED_FORMATS': ['xlsx', 'xls', 'csv', 'json'],
        'MAX_FILE_SIZE': 5 * 1024 * 1024,  # 5MB
        'MAX_RULES_PER_IMPORT': 1000,
        'REQUIRED_FIELDS': ['rule_type', 'rule_name', 'pattern', 'target_value'],
        'OPTIONAL_FIELDS': ['priority', 'description'],
        'VALID_RULE_TYPES': ['project', 'category', 'commonality'],
    },

    # 默认系统规则（作为全局规则的基础）
    'DEFAULT_PROJECT_PATTERNS': [
        {
            'rule_name': 'Weather项目识别',
            'pattern': r'Weather|天气',
            'target_value': 'Weather',
            'priority': 10
        },
        {
            'rule_name': 'TransID项目识别',
            'pattern': r'TransID',
            'target_value': 'TransID',
            'priority': 10
        },
        {
            'rule_name': 'Notes项目识别',
            'pattern': r'Notes|笔记',
            'target_value': 'Notes',
            'priority': 10
        },
        # 更多模式...
    ],

    'DEFAULT_CATEGORY_PATTERNS': [
        {
            'rule_name': 'UI问题识别',
            'pattern': r'界面|显示|布局|UI|页面|视图',
            'target_value': 'UI问题',
            'priority': 5
        },
        {
            'rule_name': '功能问题识别',
            'pattern': r'功能|操作|流程|逻辑|业务',
            'target_value': '功能问题',
            'priority': 5
        },
        {
            'rule_name': '性能问题识别',
            'pattern': r'卡顿|慢|性能|内存|CPU|响应',
            'target_value': '性能问题',
            'priority': 5
        },
        {
            'rule_name': '兼容性问题识别',
            'pattern': r'兼容|适配|版本|机型',
            'target_value': '兼容性问题',
            'priority': 5
        },
        # 更多分类...
    ],

    'DEFAULT_COMMONALITY_PATTERNS': [
        {
            'rule_name': 'UI共性问题',
            'pattern': r'UI问题',
            'target_value': '界面显示类',
            'priority': 1
        },
        {
            'rule_name': '功能共性问题',
            'pattern': r'功能问题',
            'target_value': '功能逻辑类',
            'priority': 1
        },
        {
            'rule_name': '性能共性问题',
            'pattern': r'性能问题',
            'target_value': '性能优化类',
            'priority': 1
        },
        {
            'rule_name': '兼容性共性问题',
            'pattern': r'兼容性问题',
            'target_value': '兼容适配类',
            'priority': 1
        },
        # 更多规则...
    ],

    # 权限配置
    'PERMISSION_CONFIG': {
        'ALLOW_USER_RULE_IMPORT': True,
        'ALLOW_USER_RULE_EXPORT': True,
        'MAX_PERSONAL_RULES': 500,
        'RULE_PRIORITY_RANGE': {
            'global': (1, 100),
            'personal': (1, 50)
        }
    }
}

# Celery异步任务配置
CELERY_CONFIG = {
    'broker_url': 'redis://localhost:6379/0',
    'result_backend': 'redis://localhost:6379/0',
    'task_serializer': 'json',
    'accept_content': ['json'],
    'result_serializer': 'json',
    'timezone': 'Asia/Shanghai',
    'enable_utc': True,

    # 任务路由
    'task_routes': {
        'defect_classification.tasks.process_data_import': {'queue': 'data_import'},
        'defect_classification.tasks.process_data_analysis': {'queue': 'data_analysis'},
        'defect_classification.tasks.cleanup_completed_tasks': {'queue': 'maintenance'},
    },

    # 队列配置
    'task_default_queue': 'default',
    'task_queues': {
        'data_import': {
            'exchange': 'data_import',
            'routing_key': 'data_import',
        },
        'data_analysis': {
            'exchange': 'data_analysis',
            'routing_key': 'data_analysis',
        },
        'maintenance': {
            'exchange': 'maintenance',
            'routing_key': 'maintenance',
        },
    },

    # 并发配置
    'worker_concurrency': 4,
    'worker_max_tasks_per_child': 1000,
    'task_soft_time_limit': 3600,  # 1小时软限制
    'task_time_limit': 7200,       # 2小时硬限制

    # 重试配置
    'task_default_retry_delay': 60,
    'task_max_retries': 3,
}

# WebSocket配置
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [('localhost', 6379)],
            'capacity': 1500,
            'expiry': 60,
        },
    },
}

# 任务队列配置
TASK_QUEUE_CONFIG = {
    'MAX_CONCURRENT_IMPORTS': 3,        # 最大并发导入任务数
    'MAX_QUEUE_SIZE': 100,              # 最大队列长度
    'TASK_TIMEOUT': 3600,               # 任务超时时间（秒）
    'CLEANUP_INTERVAL': 3600,           # 清理间隔（秒）
    'PROGRESS_UPDATE_INTERVAL': 5,      # 进度更新间隔（秒）

    # 优先级配置
    'PRIORITY_WEIGHTS': {
        1: 0.25,  # 低优先级
        2: 1.0,   # 普通优先级
        3: 2.0,   # 高优先级
        4: 4.0,   # 紧急优先级
    },

    # 用户配额
    'USER_LIMITS': {
        'max_concurrent_tasks': 2,      # 用户最大并发任务数
        'max_daily_imports': 10,        # 每日最大导入次数
        'max_file_size': 50 * 1024 * 1024,  # 50MB
    }
}

# 通知配置
NOTIFICATION_CONFIG = {
    'ENABLE_WEBSOCKET': True,
    'ENABLE_EMAIL': False,
    'ENABLE_SMS': False,

    # WebSocket通知类型
    'WEBSOCKET_EVENTS': [
        'import_started',
        'import_progress',
        'import_completed',
        'import_failed',
        'task_queued',
        'task_cancelled',
    ],

    # 通知保留时间
    'NOTIFICATION_RETENTION_DAYS': 30,
}

# 规则模板配置
RULE_TEMPLATE_CONFIG = {
    'EXCEL_TEMPLATE': {
        'filename': 'classification_rules_template.xlsx',
        'sheets': {
            'rules': {
                'columns': [
                    'rule_type', 'rule_name', 'pattern',
                    'target_value', 'priority', 'description'
                ],
                'sample_data': [
                    ['project', 'Weather项目识别', 'Weather|天气', 'Weather', 10, '识别天气相关项目'],
                    ['category', 'UI问题分类', '界面|显示|布局|UI', 'UI问题', 5, '识别界面显示类问题'],
                    ['commonality', '界面共性问题', 'UI问题', '界面显示类', 1, 'UI问题的共性分类']
                ]
            },
            'help': {
                'content': '''
规则导入说明：
1. rule_type: 规则类型，可选值：project（项目提取）、category（缺陷分类）、commonality（共性问题）
2. rule_name: 规则名称，用于标识规则
3. pattern: 匹配模式，支持正则表达式
4. target_value: 目标值，匹配成功时返回的值
5. priority: 优先级，数字越大优先级越高（1-50）
6. description: 规则描述，可选字段
                '''
            }
        }
    }
}
```

## 10. 扩展性设计

### 10.1 规则引擎
- 支持动态配置分类规则
- 支持正则表达式和关键词匹配
- 支持规则优先级和权重设置
- 支持规则效果评估和优化

### 10.2 机器学习集成
- 预留机器学习模型接口
- 支持训练数据收集
- 支持模型效果评估
- 支持在线学习和模型更新

### 10.3 数据源扩展
- 支持多种数据源导入（JIRA、Excel、CSV、API）
- 支持实时数据同步
- 支持增量数据更新
- 支持数据源配置管理

## 11. 实施计划

### 11.1 开发阶段
1. **第一阶段**（1-2周）：基础框架搭建
   - 数据模型设计和实现
   - 基础API接口开发
   - 数据导入功能实现

2. **第二阶段**（2-3周）：核心功能开发
   - 自动分类算法实现
   - 统计分析服务开发
   - 前端界面开发

3. **第三阶段**（1周）：测试和优化
   - 功能测试和性能优化
   - 用户界面优化
   - 文档编写

### 11.2 部署和维护
- 生产环境部署
- 用户培训
- 持续优化和迭代

## 12. 总结

本设计方案基于现有的Django项目架构，充分利用了已有的技术栈和设计模式，针对缺陷分析的特定需求进行了优化设计。方案具有以下特点：

- **权限管理完善**：支持管理员和普通用户的差异化权限控制
- **数据隔离安全**：确保用户只能访问自己的数据，保护数据安全
- **批次管理便捷**：每次导入都有唯一标识，便于数据追踪和管理
- **规则导入灵活**：普通用户可以导入个人分类规则，支持多种文件格式
- **可扩展性强**：支持规则动态配置和机器学习集成
- **易于维护**：清晰的分层架构和模块化设计
- **用户友好**：直观的界面设计和丰富的分析功能
- **性能优化**：合理的数据模型设计和缓存策略

## 核心优化亮点：

### 1. **多用户并发导入系统**
- **异步任务处理**: 基于Celery的分布式任务队列
- **智能排队机制**: 自动队列管理和优先级调度
- **实时进度反馈**: WebSocket推送任务状态和处理进度
- **并发控制**: 支持多用户同时导入，系统自动调度资源
- **任务管理**: 完整的任务生命周期管理（提交、排队、处理、完成）

### 2. **实时通知系统**
- **WebSocket通信**: 实时双向通信，零延迟状态更新
- **Toast通知**: 友好的用户界面提示
- **进度可视化**: 实时进度条和状态指示器
- **多种通知类型**: 导入开始、进度更新、完成、失败等

### 3. **分类规则导入功能**
- 支持Excel、CSV、JSON三种格式
- 提供规则模板下载
- 完整的验证和错误处理机制
- 支持规则冲突检测和处理

### 4. **双层权限体系**
- 全局规则：管理员管理，所有用户共享
- 个人规则：用户自主管理，优先级更高
- 规则作用域清晰，权限控制精确

### 5. **数据权限隔离**
- 数据库层自动过滤
- API层权限检查
- 前端界面权限提示
- 完整的审计日志

### 6. **增强的批次追踪管理**
- 唯一批次标识符
- 导入来源追踪
- 批次数据导出
- 批次分析报告
- **任务状态追踪**: 上传、排队、处理中、已完成、失败等状态

## 🚀 技术架构优势：

### 1. **高并发处理能力**
- Celery分布式任务队列支持水平扩展
- Redis作为消息代理，高性能低延迟
- 多Worker并行处理，提升系统吞吐量

### 2. **用户体验优化**
- 异步处理避免页面阻塞
- 实时反馈让用户了解处理状态
- 智能排队显示预估等待时间
- 一键取消任务功能

### 3. **系统稳定性**
- 任务失败自动重试机制
- 完整的错误处理和日志记录
- 任务超时保护
- 定期清理机制

### 4. **可扩展性设计**
- 微服务架构，易于扩展
- 插件化的通知系统
- 灵活的队列配置
- 支持多种部署方式

该方案完美解决了多用户并发导入的需求，通过异步任务队列和实时通知系统，为用户提供了流畅、直观的使用体验。系统具备良好的扩展性和稳定性，可以根据实际使用情况进行优化调整。
