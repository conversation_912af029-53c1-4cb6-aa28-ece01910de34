# 自动归类缺陷工具设计方案

## 1. 项目概述

### 1.1 项目背景
作为三级部门测开提效工具负责人，需要开发一款自动归类缺陷工具，用于解决日常交付中的缺陷分析痛点。

### 1.2 核心功能
- **数据汇总**: A、B、C类问题个数统计
- **机型缺陷汇总**: 各机型缺陷量分析
- **每日缺陷提报趋势**: 时间维度趋势分析
- **缺陷归类**: 自动分类缺陷类型
- **共性问题判断**: 识别和分析共性问题

### 1.3 核心字段
- Priority（优先级）
- Status（状态）
- Resolution（解决方案）
- Created（创建日期，转化为天格式：2025/4/7）
- stage（阶段）
- project（根据标题提取）
- summary_text（根据标题提取）
- bug_category（根据标题匹配）
- commonality（根据bug_category匹配）

## 2. 系统架构设计

### 2.1 整体架构
```
自动归类缺陷工具
├── 数据层 (Data Layer)
│   ├── 缺陷数据模型 (DefectModel)
│   ├── 分类规则模型 (ClassificationRuleModel)
│   └── 统计分析模型 (AnalysisModel)
├── 业务逻辑层 (Business Layer)
│   ├── 数据导入服务 (DataImportService)
│   ├── 自动分类服务 (AutoClassificationService)
│   ├── 统计分析服务 (StatisticsService)
│   └── 规则管理服务 (RuleManagementService)
├── API层 (API Layer)
│   ├── 缺陷管理接口
│   ├── 分类规则接口
│   └── 统计分析接口
└── 前端界面层 (UI Layer)
    ├── 缺陷列表管理
    ├── 分类规则配置
    └── 统计分析仪表板
```

### 2.2 技术栈
- **后端框架**: Django 5.2
- **数据库**: SQLite3（开发）/ PostgreSQL（生产）
- **前端**: HTML + JavaScript + Chart.js
- **数据处理**: Pandas + NumPy
- **任务调度**: Django APScheduler

## 3. 数据模型设计

### 3.1 缺陷数据模型
```python
class DefectRecord(models.Model):
    # 基础字段
    issue_key = models.CharField(max_length=50, unique=True, verbose_name='缺陷编号')
    priority = models.CharField(max_length=20, verbose_name='优先级')
    status = models.CharField(max_length=50, verbose_name='状态')
    resolution = models.CharField(max_length=50, verbose_name='解决方案', blank=True)
    created_date = models.DateField(verbose_name='创建日期')
    stage = models.CharField(max_length=50, verbose_name='阶段')
    
    # 提取字段
    project = models.CharField(max_length=100, verbose_name='项目', blank=True)
    summary_text = models.TextField(verbose_name='摘要文本', blank=True)
    
    # 分类字段
    bug_category = models.CharField(max_length=100, verbose_name='缺陷分类', blank=True)
    commonality = models.CharField(max_length=100, verbose_name='共性问题', blank=True)
    
    # 原始数据
    original_title = models.TextField(verbose_name='原始标题')
    original_data = models.JSONField(default=dict, verbose_name='原始数据')
    
    # 系统字段
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = '缺陷记录'
        verbose_name_plural = '缺陷记录'
        ordering = ['-created_date']
```

### 3.2 分类规则模型
```python
class ClassificationRule(models.Model):
    RULE_TYPE_CHOICES = [
        ('project', '项目提取'),
        ('category', '缺陷分类'),
        ('commonality', '共性问题'),
    ]
    
    rule_type = models.CharField(max_length=20, choices=RULE_TYPE_CHOICES)
    rule_name = models.CharField(max_length=100, verbose_name='规则名称')
    pattern = models.TextField(verbose_name='匹配模式')
    target_value = models.CharField(max_length=100, verbose_name='目标值')
    priority = models.IntegerField(default=0, verbose_name='优先级')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    description = models.TextField(blank=True, verbose_name='规则描述')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = '分类规则'
        verbose_name_plural = '分类规则'
        ordering = ['-priority', 'rule_type']
```

### 3.3 统计分析模型
```python
class DefectAnalysis(models.Model):
    ANALYSIS_TYPE_CHOICES = [
        ('abc_summary', 'A/B/C类问题汇总'),
        ('model_summary', '机型缺陷汇总'),
        ('daily_trend', '每日趋势'),
        ('category_distribution', '分类分布'),
        ('commonality_analysis', '共性问题分析'),
    ]
    
    analysis_date = models.DateField(verbose_name='分析日期')
    analysis_type = models.CharField(max_length=50, choices=ANALYSIS_TYPE_CHOICES, verbose_name='分析类型')
    analysis_data = models.JSONField(verbose_name='分析数据')
    date_range_start = models.DateField(verbose_name='数据范围开始', null=True, blank=True)
    date_range_end = models.DateField(verbose_name='数据范围结束', null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = '缺陷分析'
        verbose_name_plural = '缺陷分析'
        unique_together = ['analysis_date', 'analysis_type']
```

## 4. 核心服务设计

### 4.1 数据导入服务
```python
class DefectDataImportService:
    """缺陷数据导入服务"""
    
    def import_from_excel(self, file_path):
        """从Excel导入缺陷数据"""
        
    def import_from_csv(self, file_path):
        """从CSV导入缺陷数据"""
        
    def import_from_jira_api(self, jql_query):
        """从JIRA API导入缺陷数据"""
        
    def validate_data(self, data):
        """数据验证"""
        
    def transform_data(self, raw_data):
        """数据转换"""
        
    def batch_import(self, data_list):
        """批量导入"""
```

### 4.2 自动分类服务
```python
class AutoClassificationService:
    """自动分类服务"""
    
    def extract_project(self, title):
        """从标题提取项目信息"""
        
    def extract_summary_text(self, title):
        """从标题提取摘要文本"""
        
    def classify_bug_category(self, title, description=""):
        """缺陷分类"""
        
    def identify_commonality(self, bug_category):
        """共性问题识别"""
        
    def batch_classify(self, defect_records):
        """批量分类"""
        
    def update_classification_rules(self):
        """更新分类规则"""
```

### 4.3 统计分析服务
```python
class DefectStatisticsService:
    """缺陷统计分析服务"""
    
    def get_abc_summary(self, date_range=None):
        """A、B、C类问题汇总"""
        
    def get_model_defect_summary(self, date_range=None):
        """机型缺陷汇总"""
        
    def get_daily_trend(self, date_range=None):
        """每日缺陷提报趋势"""
        
    def get_category_distribution(self, date_range=None):
        """缺陷分类分布"""
        
    def get_commonality_analysis(self, date_range=None):
        """共性问题分析"""
        
    def export_analysis_report(self, analysis_type, date_range=None):
        """导出分析报告"""
```

## 5. API接口设计

### 5.1 缺陷管理接口
```python
# 缺陷数据管理
POST /api/defects/import/          # 导入缺陷数据
GET  /api/defects/list/            # 获取缺陷列表
PUT  /api/defects/<id>/            # 更新缺陷信息
DELETE /api/defects/<id>/          # 删除缺陷
POST /api/defects/batch-classify/  # 批量分类
GET  /api/defects/export/          # 导出缺陷数据

# 分类规则管理
GET  /api/classification-rules/    # 获取分类规则
POST /api/classification-rules/    # 创建分类规则
PUT  /api/classification-rules/<id>/ # 更新分类规则
DELETE /api/classification-rules/<id>/ # 删除分类规则
POST /api/classification-rules/test/ # 测试分类规则
```

### 5.2 统计分析接口
```python
# 统计分析
GET /api/analysis/abc-summary/         # A、B、C类问题汇总
GET /api/analysis/model-summary/       # 机型缺陷汇总
GET /api/analysis/daily-trend/         # 每日趋势
GET /api/analysis/category-distribution/ # 分类分布
GET /api/analysis/commonality/         # 共性问题分析
GET /api/analysis/export/              # 导出报告
GET /api/analysis/dashboard/           # 仪表板数据
```

## 6. 前端界面设计

### 6.1 主要页面
1. **缺陷数据管理页面** (`/api/defects/`)
   - 数据导入功能（支持Excel、CSV）
   - 缺陷列表展示（分页、筛选、排序）
   - 批量操作功能（分类、删除、导出）
   - 单个缺陷详情查看和编辑

2. **分类规则配置页面** (`/api/classification-rules/`)
   - 规则列表管理
   - 规则编辑器（支持正则表达式）
   - 规则测试功能
   - 规则优先级调整

3. **统计分析仪表板** (`/api/analysis/dashboard/`)
   - A、B、C类问题统计图表
   - 机型缺陷分布图
   - 每日趋势图
   - 分类分布饼图
   - 共性问题分析表

### 6.2 界面特性
- 响应式设计，支持移动端访问
- 实时数据更新
- 交互式图表（Chart.js）
- 数据导出功能
- 时间范围筛选器

## 7. 技术实现方案

### 7.1 分类算法设计
```python
class DefectClassifier:
    """缺陷分类器"""
    
    def __init__(self):
        self.project_patterns = self.load_project_patterns()
        self.category_patterns = self.load_category_patterns()
        self.commonality_mapping = self.load_commonality_mapping()
    
    def classify(self, title, description=""):
        """主分类方法"""
        result = {
            'project': self.extract_project(title),
            'summary_text': self.extract_summary_text(title),
            'bug_category': self.classify_category(title, description),
        }
        result['commonality'] = self.identify_commonality(result['bug_category'])
        return result
```

### 7.2 数据处理流程
```python
class DefectDataProcessor:
    """缺陷数据处理器"""
    
    def process_import_data(self, raw_data):
        """处理导入数据的完整流程"""
        # 1. 数据清洗
        cleaned_data = self.clean_data(raw_data)
        
        # 2. 数据转换
        transformed_data = self.transform_data(cleaned_data)
        
        # 3. 自动分类
        classified_data = self.auto_classify(transformed_data)
        
        # 4. 数据保存
        saved_records = self.save_records(classified_data)
        
        # 5. 触发统计分析
        self.trigger_analysis_update()
        
        return saved_records
```

## 8. 部署和配置

### 8.1 目录结构
```
defect_classification_tool/
├── models/
│   ├── defect_models.py
│   ├── classification_models.py
│   └── analysis_models.py
├── services/
│   ├── import_service.py
│   ├── classification_service.py
│   ├── statistics_service.py
│   └── rule_management_service.py
├── views/
│   ├── defect_views.py
│   ├── classification_views.py
│   └── analysis_views.py
├── templates/
│   ├── defect_management.html
│   ├── classification_rules.html
│   └── analysis_dashboard.html
├── static/
│   ├── css/
│   ├── js/
│   └── charts/
└── utils/
    ├── data_processors.py
    ├── classifiers.py
    └── exporters.py
```

### 8.2 配置文件
```python
# 分类配置
DEFECT_CLASSIFICATION_CONFIG = {
    'AUTO_CLASSIFY_ON_IMPORT': True,
    'DEFAULT_PROJECT_PATTERNS': [
        (r'Weather|天气', 'Weather'),
        (r'TransID', 'TransID'),
        (r'Notes|笔记', 'Notes'),
        # 更多模式...
    ],
    'CATEGORY_KEYWORDS': {
        'UI问题': ['界面', '显示', '布局', 'UI'],
        '功能问题': ['功能', '操作', '流程'],
        '性能问题': ['卡顿', '慢', '性能', '内存'],
        # 更多分类...
    },
    'COMMONALITY_RULES': {
        'UI问题': '界面显示类',
        '功能问题': '功能逻辑类',
        '性能问题': '性能优化类',
        # 更多规则...
    }
}
```

## 9. 扩展性设计

### 9.1 规则引擎
- 支持动态配置分类规则
- 支持正则表达式和关键词匹配
- 支持规则优先级和权重设置
- 支持规则效果评估和优化

### 9.2 机器学习集成
- 预留机器学习模型接口
- 支持训练数据收集
- 支持模型效果评估
- 支持在线学习和模型更新

### 9.3 数据源扩展
- 支持多种数据源导入（JIRA、Excel、CSV、API）
- 支持实时数据同步
- 支持增量数据更新
- 支持数据源配置管理

## 10. 实施计划

### 10.1 开发阶段
1. **第一阶段**（1-2周）：基础框架搭建
   - 数据模型设计和实现
   - 基础API接口开发
   - 数据导入功能实现

2. **第二阶段**（2-3周）：核心功能开发
   - 自动分类算法实现
   - 统计分析服务开发
   - 前端界面开发

3. **第三阶段**（1周）：测试和优化
   - 功能测试和性能优化
   - 用户界面优化
   - 文档编写

### 10.2 部署和维护
- 生产环境部署
- 用户培训
- 持续优化和迭代

## 11. 总结

本设计方案基于现有的Django项目架构，充分利用了已有的技术栈和设计模式，针对缺陷分析的特定需求进行了优化设计。方案具有以下特点：

- **可扩展性强**：支持规则动态配置和机器学习集成
- **易于维护**：清晰的分层架构和模块化设计
- **用户友好**：直观的界面设计和丰富的分析功能
- **性能优化**：合理的数据模型设计和缓存策略

该方案可以根据实际使用情况进行迭代优化，满足不断变化的业务需求。
