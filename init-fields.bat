@echo off
chcp 65001 >nul

echo 🔧 初始化字段映射...
echo.

if not exist "backend" (
    echo ❌ backend目录不存在，请在项目根目录运行此脚本
    pause
    exit /b 1
)

cd backend

if not exist "venv" (
    echo ❌ 虚拟环境不存在，请先运行 start-dev.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 创建Python脚本文件
echo 正在初始化字段映射...
echo import os > init_fields.py
echo os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'defect_classification.settings') >> init_fields.py
echo import django >> init_fields.py
echo django.setup() >> init_fields.py
echo. >> init_fields.py
echo from defects.models import FieldMapping >> init_fields.py
echo. >> init_fields.py
echo if not FieldMapping.objects.exists(): >> init_fields.py
echo     mappings = [ >> init_fields.py
echo         {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True}, >> init_fields.py
echo         {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True}, >> init_fields.py
echo         {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True}, >> init_fields.py
echo         {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True}, >> init_fields.py
echo         {'csv_field_name': 'Assignee', 'db_field_name': 'assignee', 'field_type': 'string'}, >> init_fields.py
echo         {'csv_field_name': 'Created', 'db_field_name': 'created', 'field_type': 'datetime'}, >> init_fields.py
echo         {'csv_field_name': 'Project key', 'db_field_name': 'project_key', 'field_type': 'string'}, >> init_fields.py
echo         {'csv_field_name': 'Description', 'db_field_name': 'description', 'field_type': 'text'}, >> init_fields.py
echo     ] >> init_fields.py
echo     for mapping in mappings: >> init_fields.py
echo         FieldMapping.objects.create(**mapping) >> init_fields.py
echo     print('✅ 字段映射初始化完成') >> init_fields.py
echo else: >> init_fields.py
echo     print('✅ 字段映射已存在') >> init_fields.py

REM 执行脚本
python init_fields.py

REM 清理临时文件
del init_fields.py

echo.
echo 📋 字段映射配置完成
echo.

cd ..
pause
