version: '3.8'

services:
  # PostgreSQL数据库
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: defect_classification
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DEBUG=False
      - DB_HOST=db
      - DB_NAME=defect_classification
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - REDIS_HOST=redis
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./backend:/app
      - media_files:/app/media
      - static_files:/app/staticfiles
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      sh -c "
        python manage.py migrate &&
        python manage.py collectstatic --noinput &&
        gunicorn defect_classification.wsgi:application --bind 0.0.0.0:8000 --workers 3
      "

  # Celery Worker
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - DEBUG=False
      - DB_HOST=db
      - DB_NAME=defect_classification
      - DB_USER=postgres
      - DB_PASSWORD=postgres123
      - REDIS_HOST=redis
      - SECRET_KEY=your-secret-key-here
    volumes:
      - ./backend:/app
      - media_files:/app/media
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: celery -A defect_classification worker --loglevel=info

  # 前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_files:/var/www/static
      - media_files:/var/www/media
    depends_on:
      - backend
      - frontend

volumes:
  postgres_data:
  redis_data:
  media_files:
  static_files:
