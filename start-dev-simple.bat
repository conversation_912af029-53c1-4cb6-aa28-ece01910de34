@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动缺陷分类工具开发环境 (Windows简化版)...
echo.

REM 基本环境检查
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装Node.js
    pause
    exit /b 1
)

REM 检查项目结构
if not exist "backend" (
    echo ❌ backend目录不存在，请确保在项目根目录运行
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ frontend目录不存在，请确保项目结构完整
    pause
    exit /b 1
)

REM 后端设置
echo 🔧 设置后端环境...
cd backend

REM 创建虚拟环境
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

REM 升级pip和安装工具
echo 🔧 升级pip和安装工具...
python -m pip install --upgrade pip setuptools wheel

REM 安装依赖 - 多种方法
echo 📦 安装后端依赖...

REM 尝试使用Windows专用requirements
if exist "requirements-windows.txt" (
    echo 尝试使用Windows专用依赖文件...
    pip install -r requirements-windows.txt --no-cache-dir
    if not errorlevel 1 (
        echo ✅ Windows专用依赖安装成功！
        goto :deps_installed
    )
)

REM 尝试使用国内镜像源
echo 尝试使用国内镜像源...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn --no-cache-dir
if not errorlevel 1 (
    echo ✅ 国内镜像源安装成功！
    goto :deps_installed
)

REM 尝试默认源
echo 尝试默认源...
pip install -r requirements.txt --no-cache-dir
if not errorlevel 1 (
    echo ✅ 默认源安装成功！
    goto :deps_installed
)

REM 如果都失败，提供解决建议
echo ❌ 所有安装方法都失败了
echo.
echo 💡 解决建议:
echo    1. 运行 fix-windows-install.bat 尝试修复
echo    2. 检查网络连接
echo    3. 以管理员身份运行
echo    4. 安装 Microsoft C++ Build Tools
echo.
pause
exit /b 1

:deps_installed

REM 数据库迁移
echo 🗄️ 执行数据库迁移...
python manage.py migrate
if errorlevel 1 (
    echo ❌ 数据库迁移失败
    pause
    exit /b 1
)

REM 启动服务
echo 🔄 启动Celery Worker...
start "Celery Worker" cmd /k "venv\Scripts\activate.bat && celery -A defect_classification worker --loglevel=info --pool=solo"

echo 🌐 启动Django开发服务器...
start "Django Server" cmd /k "venv\Scripts\activate.bat && python manage.py runserver 0.0.0.0:8000"

cd ..

REM 前端设置
echo 🎨 设置前端环境...
cd frontend

REM 安装依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 安装前端依赖失败，尝试使用国内镜像源...
        npm config set registry https://registry.npmmirror.com
        npm install
        if errorlevel 1 (
            echo ❌ 安装前端依赖失败
            pause
            exit /b 1
        )
    )
)

REM 启动前端服务器
echo 🌐 启动前端开发服务器...
start "Frontend Server" cmd /k "npm run dev"

cd ..

echo.
echo ✅ 开发环境启动完成！
echo.
echo 🌐 访问地址:
echo    前端: http://localhost:3000
echo    后端API: http://localhost:8000/api
echo    Django管理后台: http://localhost:8000/admin
echo.
echo 👤 首次使用请创建管理员账号:
echo    在Django Server窗口中按Ctrl+C停止服务
echo    然后运行: python manage.py createsuperuser
echo    再重新启动: python manage.py runserver 0.0.0.0:8000
echo.
echo ⏹️  停止服务: 关闭各服务窗口或运行 stop-dev.bat
echo.
pause
