# 缺陷分类工具开发环境停止脚本 (PowerShell跨平台版本)

Write-Host "🛑 停止缺陷分类工具开发环境 (PowerShell)..." -ForegroundColor Red
Write-Host ""

# 检测操作系统
$IsWindows = $PSVersionTable.PSVersion.Major -ge 6 ? $IsWindows : $true
$OS = if ($IsWindows) { "Windows" } elseif ($IsMacOS) { "macOS" } elseif ($IsLinux) { "Linux" } else { "Unknown" }

Write-Host "📱 检测到操作系统: $OS" -ForegroundColor Cyan

if ($IsWindows) {
    # Windows下停止服务
    Write-Host "🌐 停止Django开发服务器..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "python" -and $_.CommandLine -like "*manage.py runserver*" } | Stop-Process -Force -ErrorAction SilentlyContinue
        Get-Process | Where-Object { $_.MainWindowTitle -like "*Django*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        taskkill /F /IM python.exe /FI "WINDOWTITLE eq *Django*" 2>$null
    }
    
    Write-Host "🎨 停止前端开发服务器..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "node" -and ($_.CommandLine -like "*vite*" -or $_.CommandLine -like "*npm run dev*") } | Stop-Process -Force -ErrorAction SilentlyContinue
        Get-Process | Where-Object { $_.MainWindowTitle -like "*Frontend*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        taskkill /F /IM node.exe /FI "WINDOWTITLE eq *Frontend*" 2>$null
    }
    
    Write-Host "🔄 停止Celery Worker..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "python" -and $_.CommandLine -like "*celery*worker*" } | Stop-Process -Force -ErrorAction SilentlyContinue
        Get-Process | Where-Object { $_.MainWindowTitle -like "*Celery*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        taskkill /F /IM python.exe /FI "WINDOWTITLE eq *Celery*" 2>$null
    }
    
    # 清理端口占用
    Write-Host "🔌 清理端口占用..." -ForegroundColor Blue
    try {
        # 停止占用8000端口的进程
        $port8000 = netstat -ano | Select-String ":8000" | ForEach-Object { ($_ -split "\s+")[4] }
        if ($port8000) {
            $port8000 | ForEach-Object { Stop-Process -Id $_ -Force -ErrorAction SilentlyContinue }
        }
        
        # 停止占用3000端口的进程
        $port3000 = netstat -ano | Select-String ":3000" | ForEach-Object { ($_ -split "\s+")[4] }
        if ($port3000) {
            $port3000 | ForEach-Object { Stop-Process -Id $_ -Force -ErrorAction SilentlyContinue }
        }
    } catch {
        Write-Host "   端口清理可能不完整，请手动检查" -ForegroundColor Yellow
    }
    
} else {
    # Linux/macOS下停止服务
    Write-Host "🌐 停止Django开发服务器..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "python" -or $_.ProcessName -eq "python3" } | Where-Object { $_.CommandLine -like "*manage.py runserver*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        bash -c "pkill -f 'python.*manage.py runserver'"
    }
    
    Write-Host "🎨 停止前端开发服务器..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "node" } | Where-Object { $_.CommandLine -like "*vite*" -or $_.CommandLine -like "*npm run dev*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        bash -c "pkill -f 'npm run dev'"
        bash -c "pkill -f 'vite'"
    }
    
    Write-Host "🔄 停止Celery Worker..." -ForegroundColor Blue
    try {
        Get-Process | Where-Object { $_.ProcessName -eq "python" -or $_.ProcessName -eq "python3" } | Where-Object { $_.CommandLine -like "*celery*worker*" } | Stop-Process -Force -ErrorAction SilentlyContinue
    } catch {
        # 备用方法
        bash -c "pkill -f 'celery.*worker'"
    }
}

# 可选：停止Redis
# Write-Host "📦 停止Redis服务..." -ForegroundColor Blue
# if ($IsWindows) {
#     try {
#         Get-Process -Name "redis-server" -ErrorAction SilentlyContinue | Stop-Process -Force
#     } catch {
#         taskkill /F /IM redis-server.exe 2>$null
#     }
# } else {
#     try {
#         Get-Process -Name "redis-server" -ErrorAction SilentlyContinue | Stop-Process -Force
#     } catch {
#         bash -c "pkill redis-server"
#     }
# }

Write-Host ""
Write-Host "✅ 开发环境已停止" -ForegroundColor Green
Write-Host ""

if ($IsWindows) {
    Write-Host "💡 提示:" -ForegroundColor Yellow
    Write-Host "   - 如果某些进程未能停止，请手动关闭对应的PowerShell窗口" -ForegroundColor White
    Write-Host "   - 或者检查任务管理器中的python.exe和node.exe进程" -ForegroundColor White
    Write-Host "   - 重启计算机可以确保完全清理所有进程" -ForegroundColor White
    Write-Host "   - 下次启动请运行 .\start-dev.ps1" -ForegroundColor White
} else {
    Write-Host "💡 提示:" -ForegroundColor Yellow
    Write-Host "   - 如果某些进程未能停止，请手动使用 kill 命令" -ForegroundColor White
    Write-Host "   - 下次启动请运行 ./start-dev.ps1" -ForegroundColor White
}

Write-Host ""
Read-Host "按任意键退出"
