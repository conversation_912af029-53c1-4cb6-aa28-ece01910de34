Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Component/s,Due Date,Votes,Labels,Labels,Description,Environment,Watchers,Watchers,Watchers,Watchers,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Attachment,Attachment,Attachment,Attachment,Custom field (Affect Apk Version/s),Custom field (Affect Project),Custom field (Android版本),Custom field (Apk Version/s),Custom field (BOM),Custom field (BUG来源),Custom field (Baseline),Custom field (Baseline Effort),Custom field (Baseline End),Custom field (Baseline Start),Custom field (Baseline end date),Custom field (Baseline start date),Custom field (Bug category),Custom field (Business Value),Custom field (CMSBaseline),Custom field (CR Actual solution time),Custom field (CR Expected solution time),Custom field (CR submission time),Custom field (CausedBy),Custom field (Change Component),Custom field (Clients),Custom field (CloseDate),Custom field (Closed),Custom field (CommonIssue),Custom field (CountryCode),Custom field (CoverirtSubmitter),Custom field (CoverityAffectProject),Custom field (CoverityID),Custom field (CoverityIssueKind),Custom field (CoverityIssueStatus),Custom field (CoverityLegacy),Custom field (CoverityType),Custom field (Date of Baselining),Custom field (Deadline日期),Custom field (DevField-Module),Custom field (DevopsComponts),Custom field (Discovery Phase),Custom field (DupIssueStatus),Custom field (DuplicatedBy),Custom field (Duplicates),Custom field (Duration [Gantt]),Custom field (End Date [Gantt]),Custom field (End date),Custom field (Epic Colour),Custom field (Epic Link),Custom field (Epic Name),Custom field (Epic Status),Custom field (ExpClass),Custom field (Experience Datail),Custom field (Experience Type),Custom field (Fix Apk Version/s),Custom field (Fix Link),Custom field (Fix Link/s),Custom field (Fix Way),Custom field (Fixed),Custom field (Fixer),Custom field (GerritURL),Custom field (IMEI),Custom field (IPMTaskId),Custom field (Issue Category),Custom field (Issue Nature),Custom field (Issue Progress),Custom field (Issue Source),Custom field (Issue Stage),Custom field (IssueResponsible),Custom field (Latest End),Custom field (LogValidity),Custom field (OS Patch),Custom field (OSVersionList),Custom field (OS版本),Custom field (Opener),Custom field (PM),Custom field (PM Issue Classify),Custom field (PakagePath),Custom field (Plan Date),Custom field (Planned End),Custom field (Planned Start),Custom field (RD owner),Custom field (Rank),Custom field (Rank (Obsolete)),Custom field (ReleaseDate),Custom field (RemainingWork(hour)),Custom field (Retest原因),Custom field (Retest建议),Custom field (Risk),Custom field (SN),Custom field (SR技术负责人),Custom field (SR编号),Custom field (Solving Scheme),Custom field (SpecialType),Custom field (Start Date [Gantt]),Custom field (Start date),Custom field (Story Points),Custom field (SuitableProject),Custom field (TCID),Custom field (Task mode),Custom field (Task progress),Custom field (TestApproveDate),Custom field (TestEndDate),Custom field (ToTestDate),Custom field (TreeSelectorDF),Custom field (UI),Custom field (UI图),Custom field (UTPTaskId),Custom field (UsePath),Custom field (Value Point),Custom field (VersionID),Custom field (VersionNum),Custom field (VersionState),Custom field (Why New Way),Custom field (country_code),Custom field (lab_project),Custom field (reopened_time),Custom field (resolution),Custom field (专项名称),Custom field (专项管控类型),Custom field (中高端体验专项),Custom field (产品),Custom field (价值分类),Custom field (价值变更原因),Custom field (价值变更影响),Custom field (价值变更结果),Custom field (价值变更评审备注),Custom field (价值自检),Custom field (价值评估建议),Custom field (价值评审结果),Custom field (分析人),Custom field (创新点编号),Custom field (副田主),Custom field (包名),Custom field (区分方式),Custom field (占用空间（Data）),Custom field (占用空间（Super）),Custom field (历史分析进度),Custom field (原因分析),Custom field (受影响的模块),Custom field (可行性技术专家建议),Custom field (可行性评审建议),Custom field (可行性评审模块),Custom field (可行性评审结论),Custom field (国家),Custom field (子专项名称),Custom field (实测故障描述),Custom field (客诉故障描述),Custom field (导入策略),Custom field (影响),Custom field (影响其他模块),Custom field (截至日期),Custom field (提出日期),Custom field (改善方式),Custom field (改善时间),Custom field (方案类型),Custom field (是否上PD),Custom field (是否需要可行性评估),Custom field (本轮工作量),Custom field (机型),Custom field (来源),Custom field (标签),Custom field (根因),Custom field (模块),Custom field (测试建议),Custom field (测试负责人),Custom field (温馨提示),Custom field (物料),Custom field (目标导入系列/首项目),Custom field (系统提示),Custom field (累计工作量),Custom field (缺陷所在环境),Custom field (耗时(天)),Custom field (自检结果),Custom field (解决方案),Custom field (计划上线日期),Custom field (计划提测日期),Custom field (责任田主),Custom field (重点关注),Custom field (重要性),Custom field (问题来源),Custom field (问题类别),Custom field (需求JiraID),Custom field (需求文档),Custom field (需要Retest),Custom field (项目文档),Custom field (风险),Custom field (首项目导入时间),Comment,Comment,Comment,Comment,Comment
【交付二部】【用户场景】【X6870】【STR4】【OP】【NavigationBar】【SR-20250327-1151】【其他】翻译助手权限界面，导航栏未适配,TOS1600-1724,3332085,Bug,Fixed,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,yuan.yuan5,yu.jin5,yu.jin5,2025/06/12 15:42:32,2025/07/01 17:09:09,2025/07/02 14:40:35,,X6870-16.0.0.009SP01(OP001PF001AZ),MOL,,0,UX显示,用户场景,"A)Preconditions：
B)Operation step：设置-Infinix AI-翻译助手
C)Expect result：显示正常
D)Test result：权限界面导航栏未适配
E)Recovery actions：不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：无需
H)Other:",X6870,jian.chen5,lei.huang5,yu.jin5,zhanwei.bai,,,,,,,,内研新项目,"2025/06/12 15:45:28;yu.jin5;df714666-842b-45cd-8c34-ff1527b715e3.jpg;http://jira.transsion.com/secure/attachment/5615309/df714666-842b-45cd-8c34-ff1527b715e3.jpg","2025/06/12 15:43:15;yu.jin5;企业微信截图_17497141941615.png;http://jira.transsion.com/secure/attachment/5615295/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17497141941615.png","2025/06/12 15:43:33;yu.jin5;企业微信截图_17497142114290.png;http://jira.transsion.com/secure/attachment/5615299/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17497142114290.png",,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,AI翻译 -> app-aitranslate,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/MasterOfLanguage/+/1278771,修改代码,2025/07/01 17:09:09,yuan.yuan5,,,,UI,New issues,None,Free Test,,,,有用,,,,lei.huang5,,,,,,,,"0|ifpq07:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,xuewu.jiang1,,,,,,ui,,,,,,,,,,,,无,,,,,Bug Fix：修复已知缺陷,,,,,,,,,无,,,,,,,,,通过,优化,,,zebiao.zhou,None,中,,,,,,,,,"2025/06/16 11:32:17;zhanwei.bai;设置-Infinix AI-翻译助手
app自身ui兼容问题","2025/06/16 16:21:04;jian.chen5;.","2025/06/16 17:50:26;lei.huang5;版本未到，下版本验证","2025/06/17 14:38:59;lei.huang5;是否共性：否
 验证项目：X6870
 验证方案：按问题原步骤验证
 验证版本：
 X6870-16.0.0.011(OP001PF001AZ)","2025/06/17 16:30:01;yu.jin5;验证版本：X6870-16.0.0.011(OP001PF001AZ)

验证步骤：设置-Infinix AI-翻译助手

验证次数：10

验证结果：Fail，该版本未修改"
【交付二部】【用户场景】【X6870】【STR4】【OP】【MOL】【SR-20250327-1151】【个性化设置】全局主题下，智慧翻译界面未适配,TOS1600-2664,3361206,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,yuan.yuan5,junbo.yin,autotest-utp.auto,2025/06/20 13:54:35,2025/06/23 15:24:36,2025/07/02 14:40:35,,X6870-16.0.0.010SP01(OP001PF001AZ),MOL,,0,UX显示,用户场景,"A)Preconditions：应用Pixel Summer Sea全局主题
B)Operation step：设置-》Infinix AI-》智慧翻译-》查看界面显示
C)Expect result：各界面均已适配
D)Test result：未适配
E)Ref Phone Results if needed：
F)Problem Risk：5/5
G)Log and Screenshot address：UI无需Log
H)Recovery technique：不涉及恢复
I)other：",,autotest-utp.auto,,,,,,,,,,,内研新项目,"2025/06/20 13:55:42;junbo.yin;Screenshot_20250620-134741.jpg;http://jira.transsion.com/secure/attachment/5649109/Screenshot_20250620-134741.jpg","2025/06/20 13:55:43;junbo.yin;Screenshot_20250620-135224.jpg;http://jira.transsion.com/secure/attachment/5649110/Screenshot_20250620-135224.jpg","2025/06/20 13:55:13;junbo.yin;screenshot-1.png;http://jira.transsion.com/secure/attachment/5649099/screenshot-1.png","2025/06/20 13:55:33;junbo.yin;screenshot-2.png;http://jira.transsion.com/secure/attachment/5649100/screenshot-2.png","2025/06/20 14:14:22;junbo.yin;screenshot-3.png;http://jira.transsion.com/secure/attachment/5649454/screenshot-3.png",,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,New issues,None,Free Test,,,,,,,,zihan.huang,,,,,,,,"0|ifuojz:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,
