# Windows 环境搭建指南

本指南将帮助您在Windows环境下快速搭建缺陷分类工具的开发环境。

## 🎯 环境要求

- Windows 10/11
- Python 3.11+
- Node.js 18+
- Redis (可选，推荐使用WSL或Docker)

## 📦 软件安装

### 1. 安装Python

1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载最新版本的Python 3.11+
3. 运行安装程序，**务必勾选 "Add Python to PATH"**
4. 验证安装：
   ```cmd
   python --version
   pip --version
   ```

### 2. 安装Node.js

1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本
3. 运行安装程序，使用默认设置
4. 验证安装：
   ```cmd
   node --version
   npm --version
   ```

### 3. 安装Git

1. 访问 [Git官网](https://git-scm.com/download/win)
2. 下载并安装Git for Windows
3. 安装时选择 "Git Bash Here" 选项
4. 验证安装：
   ```cmd
   git --version
   ```

### 4. 安装Redis (推荐方法)

#### 方法1: 使用WSL (推荐)
```powershell
# 安装WSL
wsl --install

# 重启计算机后，在WSL中安装Redis
wsl
sudo apt update
sudo apt install redis-server
redis-server --version
```

#### 方法2: 使用Docker
```cmd
# 安装Docker Desktop for Windows
# 然后运行Redis容器
docker run -d -p 6379:6379 --name redis redis:alpine
```

#### 方法3: 使用Windows版本
1. 访问 [Redis Windows版本](https://github.com/microsoftarchive/redis/releases)
2. 下载并安装
3. 添加到系统PATH

### 5. 安装Windows Terminal (可选但推荐)

1. 从Microsoft Store安装Windows Terminal
2. 或访问 [GitHub Releases](https://github.com/microsoft/terminal/releases)

## 🚀 快速启动

### 方法1: 使用批处理文件 (最简单)

```cmd
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 双击运行或命令行执行
start-dev.bat
```

### 方法2: 使用PowerShell

```powershell
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 如果遇到执行策略问题，先运行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 启动开发环境
.\start-dev.ps1
```

### 方法3: 使用Git Bash

```bash
# 克隆项目
git clone <repository-url>
cd defect-classification-tool

# 启动开发环境
./start-dev.sh
```

## 🔧 常见问题解决

### 1. Python相关问题

**问题**: `'python' 不是内部或外部命令`
**解决**: 
- 重新安装Python，确保勾选 "Add Python to PATH"
- 或手动添加Python到系统PATH

**问题**: `pip install` 速度慢
**解决**: 使用国内镜像源
```cmd
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. Node.js相关问题

**问题**: `npm install` 速度慢
**解决**: 使用国内镜像源
```cmd
npm config set registry https://registry.npmmirror.com
npm install
```

**问题**: 权限错误
**解决**: 以管理员身份运行命令提示符

### 3. Redis相关问题

**问题**: Redis连接失败
**解决**: 
- 确保Redis服务正在运行
- 检查防火墙设置
- 使用Docker方式：`docker run -d -p 6379:6379 redis:alpine`

### 4. 权限相关问题

**问题**: PowerShell执行策略限制
**解决**: 
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**问题**: 文件访问权限错误
**解决**: 以管理员身份运行命令提示符或PowerShell

### 5. 端口占用问题

**问题**: 端口8000或3000被占用
**解决**: 
```cmd
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 结束占用进程
taskkill /F /PID <进程ID>
```

## 🛠️ 开发工具推荐

### 1. 代码编辑器
- **VS Code**: 免费，插件丰富
- **PyCharm**: 专业Python IDE
- **WebStorm**: 专业前端IDE

### 2. 终端工具
- **Windows Terminal**: 现代化终端
- **PowerShell 7**: 跨平台PowerShell
- **Git Bash**: 类Unix环境

### 3. 数据库工具
- **DBeaver**: 免费数据库管理工具
- **pgAdmin**: PostgreSQL专用工具

## 📋 启动检查清单

在启动项目前，请确认以下项目：

- [ ] Python 3.11+ 已安装并添加到PATH
- [ ] Node.js 18+ 已安装
- [ ] Git 已安装
- [ ] Redis 服务可用 (WSL/Docker/本地安装)
- [ ] 项目已克隆到本地
- [ ] 网络连接正常 (用于下载依赖)

## 🔄 服务管理

### 启动服务
```cmd
# 批处理方式
start-dev.bat

# PowerShell方式
.\start-dev.ps1
```

### 停止服务
```cmd
# 批处理方式
stop-dev.bat

# PowerShell方式
.\stop-dev.ps1

# 手动方式：关闭各服务的命令行窗口
```

### 服务状态检查
```cmd
# 检查端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000
netstat -ano | findstr :6379

# 检查进程
tasklist | findstr python
tasklist | findstr node
tasklist | findstr redis
```

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看项目的 [README.md](README.md)
2. 检查 [常见问题](#常见问题解决) 部分
3. 在项目仓库提交Issue
4. 查看日志文件：
   - Django: `backend\logs\django.log`
   - Celery: `backend\celery.log`

## 💡 性能优化建议

1. **使用SSD硬盘**: 提高文件读写速度
2. **关闭不必要的杀毒软件实时监控**: 避免影响开发环境
3. **使用WSL2**: 获得更好的Linux兼容性
4. **配置代理**: 如果网络访问受限
5. **增加虚拟内存**: 如果物理内存不足

## 🔐 安全注意事项

1. **不要在生产环境使用默认密码**
2. **定期更新依赖包**
3. **使用虚拟环境隔离项目依赖**
4. **备份重要数据**
5. **注意防火墙设置**
