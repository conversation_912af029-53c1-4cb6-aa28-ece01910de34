# 缺陷分类工具开发环境启动脚本 (PowerShell跨平台版本)

Write-Host "🚀 启动缺陷分类工具开发环境 (PowerShell)..." -ForegroundColor Green
Write-Host ""

# 检测操作系统
$IsWindows = $PSVersionTable.PSVersion.Major -ge 6 ? $IsWindows : $true
$OS = if ($IsWindows) { "Windows" } elseif ($IsMacOS) { "macOS" } elseif ($IsLinux) { "Linux" } else { "Unknown" }

Write-Host "📱 检测到操作系统: $OS" -ForegroundColor Cyan

# 检查Python环境
$PythonCmd = if ($IsWindows) { "python" } else { "python3" }

try {
    & $PythonCmd --version | Out-Null
    Write-Host "✅ Python 环境检查通过" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 未安装或未添加到PATH" -ForegroundColor Red
    if ($IsWindows) {
        Write-Host "   下载地址: https://www.python.org/downloads/" -ForegroundColor Yellow
        Write-Host "   安装时请勾选 'Add Python to PATH'" -ForegroundColor Yellow
    }
    Read-Host "按任意键退出"
    exit 1
}

# 检查Node.js环境
try {
    node --version | Out-Null
    Write-Host "✅ Node.js 环境检查通过" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安装或未添加到PATH" -ForegroundColor Red
    Write-Host "   下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查Redis
$SkipRedis = $false
try {
    redis-server --version | Out-Null
    Write-Host "✅ Redis 环境检查通过" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Redis 未安装或未添加到PATH" -ForegroundColor Yellow
    Write-Host "   推荐安装方法:" -ForegroundColor Yellow
    if ($IsWindows) {
        Write-Host "   1. 使用WSL: wsl --install，然后在WSL中安装Redis" -ForegroundColor Yellow
        Write-Host "   2. 使用Docker: docker run -d -p 6379:6379 redis:alpine" -ForegroundColor Yellow
        Write-Host "   3. 下载Windows版本: https://github.com/microsoftarchive/redis/releases" -ForegroundColor Yellow
    } else {
        Write-Host "   1. 使用包管理器安装Redis" -ForegroundColor Yellow
        Write-Host "   2. 使用Docker: docker run -d -p 6379:6379 redis:alpine" -ForegroundColor Yellow
    }
    Write-Host ""
    $continue = Read-Host "是否继续启动(不启动Redis)? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
    $SkipRedis = $true
}

# 启动Redis
if (-not $SkipRedis) {
    Write-Host "📦 启动Redis服务..." -ForegroundColor Blue
    if ($IsWindows) {
        Start-Process redis-server -WindowStyle Hidden
    } else {
        if (Get-Command systemctl -ErrorAction SilentlyContinue) {
            sudo systemctl start redis
        } elseif (Get-Command brew -ErrorAction SilentlyContinue) {
            brew services start redis
        } else {
            Start-Process redis-server -ArgumentList "--daemonize yes"
        }
    }
    Start-Sleep -Seconds 2
} else {
    Write-Host "⚠️  跳过Redis启动，请确保Redis服务正在运行" -ForegroundColor Yellow
}

# 后端设置
Write-Host "🔧 设置后端环境..." -ForegroundColor Blue
Set-Location backend

# 创建虚拟环境（如果不存在）
if (-not (Test-Path "venv")) {
    Write-Host "📦 创建Python虚拟环境..." -ForegroundColor Blue
    & $PythonCmd -m venv venv
}

# 激活虚拟环境
Write-Host "🔄 激活虚拟环境..." -ForegroundColor Blue
if ($IsWindows) {
    & "venv\Scripts\Activate.ps1"
} else {
    # 在PowerShell中激活Linux/macOS虚拟环境需要特殊处理
    $env:VIRTUAL_ENV = (Resolve-Path "venv").Path
    $env:PATH = "$env:VIRTUAL_ENV/bin:$env:PATH"
}

# 安装依赖
Write-Host "📦 安装后端依赖..." -ForegroundColor Blue
pip install -r requirements.txt

# 数据库迁移
Write-Host "🗄️ 执行数据库迁移..." -ForegroundColor Blue
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（如果不存在）
Write-Host "👤 检查超级用户..." -ForegroundColor Blue
$createUserScript = @"
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ 创建超级用户: admin/admin123')
else:
    print('✅ 超级用户已存在')
"@
python manage.py shell -c $createUserScript

# 初始化字段映射
Write-Host "🔧 初始化字段映射..." -ForegroundColor Blue
$initMappingScript = @"
from defects.models import FieldMapping
if not FieldMapping.objects.exists():
    mappings = [
        {'csv_field_name': 'Issue key', 'db_field_name': 'issue_key', 'field_type': 'string', 'is_required': True, 'is_core_field': True},
        {'csv_field_name': 'Summary', 'db_field_name': 'summary', 'field_type': 'text', 'is_core_field': True},
        {'csv_field_name': 'Status', 'db_field_name': 'status', 'field_type': 'string', 'is_core_field': True},
        {'csv_field_name': 'Priority', 'db_field_name': 'priority', 'field_type': 'string', 'is_core_field': True},
        {'csv_field_name': 'Assignee', 'db_field_name': 'assignee', 'field_type': 'string'},
        {'csv_field_name': 'Created', 'db_field_name': 'created', 'field_type': 'datetime'},
        {'csv_field_name': 'Project key', 'db_field_name': 'project_key', 'field_type': 'string'},
        {'csv_field_name': 'Description', 'db_field_name': 'description', 'field_type': 'text'},
    ]
    for mapping in mappings:
        FieldMapping.objects.create(**mapping)
    print('✅ 初始化字段映射完成')
else:
    print('✅ 字段映射已存在')
"@
python manage.py shell -c $initMappingScript

# 启动Celery Worker
Write-Host "🔄 启动Celery Worker..." -ForegroundColor Blue
if ($IsWindows) {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; venv\Scripts\Activate.ps1; celery -A defect_classification worker --loglevel=info --pool=solo" -WindowStyle Normal
} else {
    Start-Process bash -ArgumentList "-c", "cd '$PWD' && source venv/bin/activate && celery -A defect_classification worker --loglevel=info --detach"
}

# 启动Django开发服务器
Write-Host "🌐 启动Django开发服务器..." -ForegroundColor Blue
if ($IsWindows) {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; venv\Scripts\Activate.ps1; python manage.py runserver 0.0.0.0:8000" -WindowStyle Normal
} else {
    Start-Process bash -ArgumentList "-c", "cd '$PWD' && source venv/bin/activate && python manage.py runserver 0.0.0.0:8000"
}

Set-Location ..

# 前端设置
Write-Host "🎨 设置前端环境..." -ForegroundColor Blue
Set-Location frontend

# 安装依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 安装前端依赖..." -ForegroundColor Blue
    npm install
}

# 启动前端开发服务器
Write-Host "🌐 启动前端开发服务器..." -ForegroundColor Blue
if ($IsWindows) {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD'; npm run dev" -WindowStyle Normal
} else {
    Start-Process bash -ArgumentList "-c", "cd '$PWD' && npm run dev"
}

Set-Location ..

Write-Host ""
Write-Host "✅ 开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址:" -ForegroundColor Cyan
Write-Host "   前端: http://localhost:3000" -ForegroundColor White
Write-Host "   后端API: http://localhost:8000/api" -ForegroundColor White
Write-Host "   Django管理后台: http://localhost:8000/admin" -ForegroundColor White
Write-Host ""
Write-Host "👤 管理员账号:" -ForegroundColor Cyan
Write-Host "   用户名: admin" -ForegroundColor White
Write-Host "   密码: admin123" -ForegroundColor White
Write-Host ""
Write-Host "📝 日志文件:" -ForegroundColor Cyan
Write-Host "   Django: backend/logs/django.log" -ForegroundColor White
Write-Host "   Celery: backend/celery.log" -ForegroundColor White
Write-Host ""

if ($IsWindows) {
    Write-Host "🔧 Windows特别说明:" -ForegroundColor Yellow
    Write-Host "   - 各服务在独立的PowerShell窗口中运行" -ForegroundColor White
    Write-Host "   - 关闭对应窗口即可停止相应服务" -ForegroundColor White
    Write-Host "   - 或运行 stop-dev.ps1 停止所有服务" -ForegroundColor White
    Write-Host "   - 如遇权限问题，请以管理员身份运行PowerShell" -ForegroundColor White
    Write-Host ""
    Write-Host "⏹️  停止服务: 运行 .\stop-dev.ps1 或关闭各服务窗口" -ForegroundColor Red
} else {
    Write-Host "⏹️  停止服务: 运行 ./stop-dev.ps1 或 Ctrl+C" -ForegroundColor Red
}

Write-Host ""
Read-Host "按任意键继续"
