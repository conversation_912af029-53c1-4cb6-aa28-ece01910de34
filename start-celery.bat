@echo off
chcp 65001 >nul

echo 🔄 启动Celery Worker (Windows)...
echo.

REM 检查Redis是否可用
redis-cli --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Redis未安装，Celery将无法正常工作
    echo    请先安装Redis或使用Docker运行Redis:
    echo    docker run -d -p 6379:6379 redis:alpine
    echo.
    set /p continue="是否继续启动Celery? (y/N): "
    if /i not "!continue!"=="y" (
        exit /b 1
    )
) else (
    REM 检查Redis连接
    redis-cli ping >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  无法连接到Redis服务
        echo    请确保Redis服务正在运行
        echo.
        set /p continue="是否继续启动Celery? (y/N): "
        if /i not "!continue!"=="y" (
            exit /b 1
        )
    ) else (
        echo ✅ Redis连接正常
    )
)

REM 检查项目结构
if not exist "backend" (
    echo ❌ backend目录不存在，请确保在项目根目录运行
    pause
    exit /b 1
)

cd backend

REM 检查虚拟环境
if not exist "venv" (
    echo ❌ 虚拟环境不存在，请先运行 start-backend.bat
    pause
    exit /b 1
)

REM 激活虚拟环境
call venv\Scripts\activate.bat

REM 检查Celery是否安装
python -c "import celery" >nul 2>&1
if errorlevel 1 (
    echo ❌ Celery未安装，正在安装...
    pip install celery redis
)

echo.
echo ✅ Celery环境准备完成！
echo.
echo 🔄 启动Celery Worker...
echo    监控地址: http://localhost:5555 (如果安装了flower)
echo    日志级别: INFO
echo.
echo 💡 Celery功能:
echo    - 异步数据导入处理
echo    - 后台任务执行
echo    - 进度通知推送
echo.
echo ⏹️  停止服务: Ctrl+C
echo.

REM 启动Celery Worker (Windows使用solo pool)
celery -A config worker --loglevel=info --pool=solo
