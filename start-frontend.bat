@echo off
chcp 65001 >nul

echo 🚀 启动Vue3前端服务 (Windows)...
echo.

REM 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装Node.js
    echo    下载地址: https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装，请先安装npm
    pause
    exit /b 1
)

REM 检查项目结构
if not exist "frontend" (
    echo ❌ frontend目录不存在，请确保在项目根目录运行
    pause
    exit /b 1
)

cd frontend

REM 检查package.json
if not exist "package.json" (
    echo ❌ package.json 不存在，请确保前端项目结构完整
    pause
    exit /b 1
)

echo 📦 Node.js版本:
node --version
echo 📦 npm版本:
npm --version
echo.

REM 安装依赖
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    
    REM 配置npm镜像源
    echo 🔧 配置npm镜像源...
    npm config set registry https://registry.npmmirror.com
    
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败，尝试清理缓存后重试...
        npm cache clean --force
        if exist "node_modules" rmdir /s /q node_modules
        if exist "package-lock.json" del package-lock.json
        npm install
        if errorlevel 1 (
            echo ❌ 依赖安装失败，请检查网络连接或手动安装
            pause
            exit /b 1
        )
    )
) else (
    echo ✅ 依赖已安装，跳过安装步骤
)

REM 检查关键依赖
echo 🔍 检查关键依赖...
npm list vue >nul 2>&1
if errorlevel 1 (
    echo ❌ Vue3 未正确安装
    pause
    exit /b 1
)

npm list vite >nul 2>&1
if errorlevel 1 (
    echo ❌ Vite 未正确安装
    pause
    exit /b 1
)

echo.
echo ✅ 前端环境准备完成！
echo.
echo 🌐 启动前端开发服务器...
echo    访问地址: http://localhost:3000
echo    后端API: http://localhost:8000/api (请确保后端已启动)
echo.
echo 💡 开发提示:
echo    - 修改代码后会自动热重载
echo    - 使用 Ctrl+C 停止服务
echo    - 如需修改端口，编辑 vite.config.ts
echo.
echo ⏹️  停止服务: Ctrl+C
echo.

REM 启动开发服务器
npm run dev
